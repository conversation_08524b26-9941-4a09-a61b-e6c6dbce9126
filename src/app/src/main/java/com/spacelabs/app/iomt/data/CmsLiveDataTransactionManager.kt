package com.spacelabs.app.iomt.data

import android.util.Log
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.spacelabs.app.api.ApiPostObservations
import com.spacelabs.app.api.data.dataclasses.WaveObservationBean
import com.spacelabs.app.api.data.observations.ObservationManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.objectbox.encryption.EncryptionConverter
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.objectbox.boxes.AlarmEventData
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.helpers.AlarmBoxDaoHelper
import com.spacelabs.app.database.objectbox.helpers.MeasurementDataDaoHelper
import com.spacelabs.app.iomt.CmsClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.nio.ByteBuffer
import kotlin.coroutines.CoroutineContext

class CmsLiveDataTransactionManager {

    companion object {
        private val postDataCoroutine = CoroutineScope(Dispatchers.Default)

        private var measurementDaoHelper: MeasurementDataDaoHelper? = null
        private var alarmBoxDaoHelper: AlarmBoxDaoHelper? = null
        var historyJob: CoroutineContext? = null
        private var pendingMeasurements: List<MeasurementData>? = null
        private const val HL7_MDC_CODE_SYSTEM = "urn:oid:2.16.840.1.113883.6.24"

    }

    private val encryptionConverter = EncryptionConverter()
    private val encryptionVitalConverter = EncryptionVitalConverter()
    init {
        ApiPostObservations.ecgObservation = setupEcgObservation()
        ApiPostObservations.respObservation = setupRespObservation()
        ApiPostObservations.ppgIrObservation = setupPpgIrObservation()
    }

    private fun getMeasurementDaoHelper(): MeasurementDataDaoHelper? {
        if (measurementDaoHelper == null)
            measurementDaoHelper = MeasurementDataDaoHelper()

        return measurementDaoHelper
    }

    fun uploadHistoricalData() {
        historyJob = postDataCoroutine.launch {
            if (CmsClient.mWebSocketClient?.isClosed == true)
                return@launch

            val startTime = TimestampUtils.getCurrentTimeMillis() - 30000
            measurementDaoHelper = getMeasurementDaoHelper()
            pendingMeasurements =
                measurementDaoHelper?.getLastNNumberDataByTimeMillis(startTime, 1000)

            if (!pendingMeasurements.isNullOrEmpty()) {
                val (waveforms, vitals) = pendingMeasurements!!.partition { it.measurementData != null }
                uploadReceivedWaveData(waveforms)
                uploadReceivedVitals(vitals)
                if(historyJob?.isActive == true)
                    uploadHistoricalData()
            } else {
                Log.d(
                    "UploadHistoricalData",
                    "pendingMeasurements Empty:${pendingMeasurements.isNullOrEmpty()}  ,History Job : ${historyJob?.isActive}"
                )
                return@launch
            }
        }
    }

    fun uploadLiveMeasurementData(measurement: MeasurementData, measurementData: ByteArray?) {
        try {
            if (CmsClient.mWebSocketClient == null || CmsClient.mWebSocketClient?.isClosed!!)
                return
            else {
                if (measurement.measurementData != null)
                   /* parseAndUploadWaveData(measurement, measurementData)*/
                    parseAndUploadFhirWaveData(measurement, measurementData)
                else
                    uploadReceivedVitals(listOf(measurement))
            }
        } catch (ex: Exception) {
            Log.d("checkDbAndUploadPendingMeasurementToServer", ex.stackTraceToString())
        }
    }


    private fun uploadReceivedVitals(measurementData: List<MeasurementData>?) {
        measurementData ?: return

        measurementData.forEach {
            when (it.paramName) {
                StreamDataType.BP.toString() -> postNIBPToSns(it)
                /*else -> CommonDataArea.cmsClient.sendValue(it)*/
                else ->postVitals(it)
            }
        }
    }

    private fun uploadReceivedWaveData(measurementData: List<MeasurementData>?) {
        measurementData ?: return
        measurementData.forEach {
            val byteArray =
                it.measurementData?.let { it1 -> encryptionConverter.convertToEntityProperty(it1) }
            /*parseAndUploadWaveData(it, byteArray)*/
            parseAndUploadFhirWaveData(it, byteArray)

        }
    }

    private fun parseAndUploadWaveData(measurement: MeasurementData, byteArray: ByteArray?) {
        if (byteArray != null) {
            val byteBuffer = ByteBuffer.wrap(byteArray)
            val sampleArray = FloatArray(byteBuffer.remaining() / 8)
            for (i in sampleArray.indices) {
                sampleArray[i] = byteBuffer.double.toFloat()
            }
            CommonDataArea.cmsClient.sendPacket(sampleArray, measurement)
        }
    }
    private fun parseAndUploadFhirWaveData(measurement: MeasurementData, byteArray: ByteArray?) {
        if (byteArray != null) {
            val byteBuffer = ByteBuffer.wrap(byteArray)
            val sampleArray = DoubleArray(byteBuffer.remaining() / 8)
            for (i in sampleArray.indices) {
                sampleArray[i] = byteBuffer.double
            }
            /*CommonDataArea.cmsClient.sendPacket(sampleArray, measurement)*/
            CommonDataArea.cmsClient.sendFhirPacket(sampleArray, measurement)
        }
    }

    private fun postNIBPToSns(vitalData: MeasurementData) {
        val decryptedValue = encryptionVitalConverter.convertToEntityProperty(vitalData.value!!)
        val combinedBp: Long = decryptedValue!!.toLong()
        val sys: Int = getSys(combinedBp).toInt()
        val dia: Int = getDia(combinedBp).toInt()
        val bpSysAndDia = "$sys/$dia"
        CommonDataArea.cmsClient.sendBpValue(vitalData, bpSysAndDia)
    }

    private fun postNIBP(measurement: MeasurementData) {
        val uuId = measurement.measurementUuid
        val decryptedValue = encryptionVitalConverter.convertToEntityProperty(measurement.value!!)
        val combinedBp: Long = decryptedValue!!.toLong()
        val timestamp = measurement.timestamp
        val sys: Int = getSys(combinedBp).toInt()
        val dia: Int = getDia(combinedBp).toInt()
        val bpSysAndDia = "$sys/$dia"
        CommonDataArea.cmsClient.sendNIBPObservation(bpSysAndDia, timestamp, uuId,measurement)
    }

    private fun postVitals(measurementData: MeasurementData) {
        val uuId = measurementData.measurementUuid

        val triple = getApiParamTriple(measurementData.paramName, measurementData.sensorId)
        if(triple != null) {
            CommonDataArea.cmsClient.sendVitalObservation(measurementData, triple, uuId)
        }
    }


    fun checkResponseAndUpdateDb(type: Class<*>, data: Any) {
        when (type) {
            MeasurementData::class.java -> measurementDaoHelper?.updateMeasurementUploadStatusByUuid(
                data as MeasurementData,
                2
            )

            AlarmEventData::class.java -> alarmBoxDaoHelper?.updateAlarmEventUploadStatusByUuid(
                event = data as AlarmEventData,
                status = 2
            )
        }
    }

     fun getSys(combinedValue: Long): Double {
        val factor = 1_000_000L
        val combinedX = combinedValue shr 32
        return (combinedX.toDouble() / factor)
    }

     fun getDia(combinedValue: Long): Double {
        val factor = 1_000_000L
        val combinedY = combinedValue and 0xFFFFFFFFL
        return (combinedY.toDouble() / factor)
    }
    private fun setupEcgObservation(): WaveObservationBean {
        return WaveObservationBean(
            period = 3.90625,
            factor = 1.0,
            origin = 0/*getOrigin(4095, 0)*/,
            originalUpperLimit = 6.25,
            originalLowerLimit = -6.25,
            apiUpperLimit = 4095,
            apiLowerLimit = 0,
            code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "131328", "display" to "MDC_ECG_ELEC_POTL"),
            deviceDisplay = "SLHUB-ECG",
            componentCode = "131330",
        )
    }

    private fun setupPpgIrObservation(): WaveObservationBean {
        return WaveObservationBean(
            period = 15.625,
            factor = 1.0,
            origin = 0,
            originalUpperLimit = 65535.0,
            originalLowerLimit = 0.0,
            apiUpperLimit = 65535,   //2368
            apiLowerLimit = 0,   //1856
            code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "59408-5", "display" to "MDC_PULS_OXIM_PLETH"),
            deviceDisplay = "SLHUB-PPG-IR",
            componentCode = "59408-5"
        )
    }

    private fun setupRespObservation(): WaveObservationBean {
        return WaveObservationBean(
            period = 0.0,
            factor = 1.0,
            origin = getOrigin(2432, 1792),
            originalUpperLimit = 65536.0,
            originalLowerLimit = 0.0,
            apiUpperLimit = 2368,
            apiLowerLimit = 1856,
            code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "9279-1", "display" to "Respiratory rate"),
            deviceDisplay = "SLHUB-RR",
            componentCode = "9279-1"
        )
    }
    private fun getOrigin(upper: Int, lower: Int): Int{
        val center = (upper - lower) / 2
        return center + lower
    }

    private fun getApiParamTriple(type: String, sensorId: Int?): Triple<String, String, String>?{
        sensorId ?: return null
        return when(type.uppercase()){
            StreamDataType.HR.name -> ObservationManager.HR
            StreamDataType.RR.name -> ObservationManager.RR
            StreamDataType.SPO2.name -> ObservationManager.SpO2
            StreamDataType.TEMP_SKIN.name -> ObservationManager.getTempTriple(sensorId)
            else -> null
        }
    }

}