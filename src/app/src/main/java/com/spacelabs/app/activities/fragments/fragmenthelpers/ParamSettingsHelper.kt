package com.spacelabs.app.activities.fragments.fragmenthelpers

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.EditText
import android.widget.Spinner
import android.widget.Switch
import androidx.appcompat.widget.AppCompatButton
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.R
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.database.daoHelper.AlarmDaoHelper
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.dialogs.AlertConfirmDialogs

class ParamSettingsHelper(val context: Context) {

    enum class SettingsType {
        ExtremeHigh,
        ExtremeLow,
        High,
        Low
    }

    fun setupTimerSpinner(value: String, spinner: Spinner) {
        val timeIntervals = context.resources.getStringArray(R.array.timeIntervals)
        val adapter: ArrayAdapter<String> =
            ArrayAdapter(context, R.layout.spinner_item_layout, timeIntervals)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter

        spinner.setSelection(timeIntervals.indexOf(value))
    }

    fun onTimerSpinnerClickAction(
        periodicTime: String,
        settings: SettingDaoHelper.AppSettings,
    ): AdapterView.OnItemSelectedListener {
        val settingDaoHelper = SettingDaoHelper(context)
        return object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                val selectedValue = parent?.getItemAtPosition(position).toString()
                if (selectedValue != periodicTime) {
                    settingDaoHelper.updateSettings(settings, selectedValue)
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                Log.d("nothingSelected", "No value has selected")
            }
        }
    }

    fun incDecButtonsActionListeners(
        incButtons: Map<AppCompatButton, EditText>,
        decButtons: Map<AppCompatButton, EditText>,
        commonDifference: Int,
    ) {
        for (incPair in incButtons) {
            val button = incPair.key
            val editText = incPair.value
            button.setOnClickListener {
                val currentValue = editText.text.toString().toInt()
                val value = currentValue + commonDifference
                editText.setText(value.toString())
            }
        }

        for (decPair in decButtons) {
            val button = decPair.key
            val editText = decPair.value
            button.setOnClickListener {
                val currentValue = editText.text.toString().toInt()
                val value = currentValue - commonDifference
                editText.setText(value.toString())
            }
        }
    }

    fun onVolumeSliderChangeAction(value: Float, alarmParamsDao: AlarmParametersDao): Int {
        val volume = if (value > 0.2)
            (value * 15).toInt()
        else
            (0.2 * 15).toInt()
        alarmParamsDao.alarmSound = volume
        return volume
    }

    fun settingsValueTextChangedActionListener(
        editTextAndType: Map.Entry<EditText, SettingsType>,
        alarmParametersDao: AlarmParametersDao,
        alarmDaoHelper: AlarmDaoHelper,
    ) {
        try {
            editTextAndType.key.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int,
                ) {
                    //Action tobe performed before text change
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    //Action tobe performed while text is being changed
                }

                override fun afterTextChanged(s: Editable?) {
                    if (s!!.isNotEmpty()) {
                        val value = s.toString().toDouble()
                        afterTextChangedAction(
                            editTextAndType,
                            value,
                            alarmParametersDao,
                            alarmDaoHelper
                        )
                        checkAlarmParamAndNotifyBpAlarm(alarmParametersDao)
                    }
                }
            })
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("SettingsTextChangedListener", ex.stackTraceToString())
            Log.d("SettingsTextChangedListener", ex.stackTraceToString())
        }
    }

    private fun afterTextChangedAction(
        editTextAndType: Map.Entry<EditText, SettingsType>,
        value: Double,
        alarmParametersDao: AlarmParametersDao,
        alarmDaoHelper: AlarmDaoHelper,
    ) {
        if (alarmParametersDao.paramName.equals("HR", true))
            parametersWithExtremeSettingsOnTextChangedAction(
                editTextAndType,
                value,
                alarmParametersDao,
                alarmDaoHelper
            )
        else
            parametersWithoutExtremeSettingsOnTextChanged(
                editTextAndType,
                value,
                alarmParametersDao,
                alarmDaoHelper
            )
    }

    private fun parametersWithoutExtremeSettingsOnTextChanged(
        editTextAndType: Map.Entry<EditText, SettingsType>,
        value: Double,
        alarmParametersDao: AlarmParametersDao,
        alarmDaoHelper: AlarmDaoHelper,
    ) {
        try {
            when (editTextAndType.value) {
                SettingsType.High -> {
                    val changedVal = getChangedValue(
                        value,
                        alarmParametersDao.highMaxValue,
                        alarmParametersDao.lowValue
                    )
                    if (changedVal != null) {
                        alarmParametersDao.highValue = changedVal
                        alarmDaoHelper.updatePatientAlarmToDb(alarmParametersDao)
                    } else {
                        actionIfValueExceedsLimit(
                            editTextAndType.key,
                            alarmParametersDao.highValue.toInt().toString(),
                            alarmParametersDao.highMaxValue,
                            alarmParametersDao.lowValue
                        )
                    }
                }

                SettingsType.Low -> {
                    val changedVal = getChangedValue(
                        value,
                        alarmParametersDao.highValue,
                        alarmParametersDao.lowMinValue
                    )
                    if (changedVal != null) {
                        alarmParametersDao.lowValue = changedVal
                        alarmDaoHelper.updatePatientAlarmToDb(alarmParametersDao)
                    } else {
                        actionIfValueExceedsLimit(
                            editTextAndType.key,
                            alarmParametersDao.lowValue.toInt().toString(),
                            alarmParametersDao.highValue,
                            alarmParametersDao.lowMinValue
                        )
                    }
                }

                else -> {
                    Log.d("afterTextChangedAction", "Invalid EditText Type")
                }
            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("TextChangedException", ex.stackTraceToString())
            Log.d("TextChangedException", ex.stackTraceToString())
        }
    }

    private fun parametersWithExtremeSettingsOnTextChangedAction(
        editTextAndType: Map.Entry<EditText, SettingsType>,
        value: Double,
        alarmParametersDao: AlarmParametersDao,
        alarmDaoHelper: AlarmDaoHelper,
    ) {
        try {
            when (editTextAndType.value) {
                SettingsType.ExtremeHigh -> {
                    val changedVal = getChangedValue(
                        value,
                        alarmParametersDao.highMaxValue,
                        alarmParametersDao.highValue
                    )
                    if (changedVal != null) {
                        alarmParametersDao.extremeHighValue = changedVal
                        alarmDaoHelper.updatePatientAlarmToDb(alarmParametersDao)
                    } else {
                        actionIfValueExceedsLimit(
                            editTextAndType.key,
                            alarmParametersDao.extremeHighValue.toInt().toString(),
                            alarmParametersDao.highMaxValue,
                            alarmParametersDao.highValue,
                            5
                        )
                    }
                }

                SettingsType.High -> {
                    val changedVal = getChangedValue(
                        value,
                        alarmParametersDao.extremeHighValue,
                        alarmParametersDao.lowValue
                    )
                    if (changedVal != null) {
                        alarmParametersDao.highValue = changedVal
                        alarmDaoHelper.updatePatientAlarmToDb(alarmParametersDao)
                    } else {
                        actionIfValueExceedsLimit(
                            editTextAndType.key,
                            alarmParametersDao.highValue.toInt().toString(),
                            alarmParametersDao.extremeHighValue,
                            alarmParametersDao.lowValue,
                            5
                        )
                    }
                }

                SettingsType.Low -> {
                    val changedVal = getChangedValue(
                        value,
                        alarmParametersDao.highValue,
                        alarmParametersDao.extremeLowValue
                    )
                    if (changedVal != null) {
                        alarmParametersDao.lowValue = changedVal
                        alarmDaoHelper.updatePatientAlarmToDb(alarmParametersDao)
                    } else {
                        actionIfValueExceedsLimit(
                            editTextAndType.key,
                            alarmParametersDao.lowValue.toInt().toString(),
                            alarmParametersDao.highValue,
                            alarmParametersDao.extremeLowValue,
                            5
                        )
                    }
                }

                SettingsType.ExtremeLow -> {
                    val changedVal = getChangedValue(
                        value,
                        alarmParametersDao.lowValue,
                        alarmParametersDao.lowMinValue
                    )
                    if (changedVal != null) {
                        alarmParametersDao.extremeLowValue = changedVal
                        alarmDaoHelper.updatePatientAlarmToDb(alarmParametersDao)
                    } else {
                        actionIfValueExceedsLimit(
                            editTextAndType.key,
                            alarmParametersDao.extremeLowValue.toInt().toString(),
                            alarmParametersDao.lowValue,
                            alarmParametersDao.lowMinValue,
                            5
                        )
                    }
                }
            }
        } catch (ex: Exception) {
            Log.d("TextChangedException", ex.stackTraceToString())
        }
    }

    private fun getChangedValue(value: Double, upperLimit: Double, lowerLimit: Double): Double? {
        return if (value < upperLimit && value > lowerLimit) value else null
    }

    private fun actionIfValueExceedsLimit(
        editText: EditText,
        value: String,
        upperLimit: Double,
        lowerLimit: Double,
        commonDifference: Int = 1,
    ) {
        val alertConfirmDialogs = AlertConfirmDialogs(context)
        editText.setText(value)
        alertConfirmDialogs.getAlertDialog(
            "Invalid value",
            "Allowable range: ${lowerLimit + commonDifference} - ${upperLimit - commonDifference}",
            5000
        )
    }

    @SuppressLint("UseSwitchCompatOrMaterialCode")
    fun alarmSwitchActionListener(
        alarmSwitch: Switch,
        isChecked: Boolean,
        types: Array<StreamDataType>,
        sensor: Sensor?,
    ): Boolean {
        return if (sensor?.isConnected == true) {
            switchStyleChangeOnCheckedChange(alarmSwitch, isChecked)
            SibelSensorManager.sensorManagerHelper.measurementDataStreamController(
                isChecked,
                sensor,
                types
            )
            true
        } else {
            false
        }
    }

    fun switchStyleChangeOnCheckedChange(
        @SuppressLint("UseSwitchCompatOrMaterialCode") switch: Switch,
        isChecked: Boolean,
    ) {
        if (isChecked) {
            switch.thumbTintList =
                context.resources.getColorStateList(R.color.lightBlue, context.theme)
            switch.trackTintList =
                context.resources.getColorStateList(R.color.lightBlue, context.theme)
        } else {
            switch.thumbTintList = context.resources.getColorStateList(R.color.white, context.theme)
            switch.trackTintList = context.resources.getColorStateList(R.color.white, context.theme)
        }
    }

    fun uiEventOnSwitchAction(alarmParam: AlarmParametersDao) {
        if (alarmParam.isAttached) {
            alarmParam.alarmText = context.resources.getString(R.string.monitorOff)
            CommonEventHandler.postUiEvent(
                UiEventCallback.UiEventType.StreamVisibilityChange,
                alarmParam
            )

        }
    }
    
    fun uiEventOnSwitchActionForBP(alarmParam: AlarmParametersDao, isChecked: Boolean) {
        if (!isChecked || alarmParam.isAttached)
            alarmParam.alarmText = context.resources.getString(R.string.monitorOff)
        else alarmParam.alarmText = CommonDataArea.bpMonitorError.toString()

        CommonEventHandler.postUiEvent(
            UiEventCallback.UiEventType.StreamVisibilityChange,
            alarmParam
        )
    }

    private fun checkAlarmParamAndNotifyBpAlarm(alarmParametersDao: AlarmParametersDao) {
        val paramAndValue: Pair<CurrentAlarmParameter, Double> = when (alarmParametersDao) {
            CommonDataArea.bpSysAlarmDao -> Pair(CurrentAlarmParameter.BpSys, CommonDataArea.BpSys)
            CommonDataArea.bpDiaAlarmDao -> Pair(CurrentAlarmParameter.BpDia, CommonDataArea.BpDia)
            else -> return
        }

        CommonEventHandler.postAlarmEvent(paramAndValue.first, paramAndValue.second)
    }
}