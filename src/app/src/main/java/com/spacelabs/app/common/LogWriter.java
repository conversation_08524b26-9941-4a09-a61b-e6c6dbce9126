package com.spacelabs.app.common;

import android.os.Environment;
import android.util.Log;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Dictionary;
import java.util.Hashtable;
import java.util.Objects;

public class LogWriter {

    public static boolean disableLog = false;
    public static boolean disableDetailedLog = true;
    public static int exceptionCount = 0;
    public static String CommonPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath();
    private static void loggingFunction(String content, String event, String fileName) {
        try {
            if (disableLog) return;

            String filePath;
            File file = null;
            try {
                filePath = CommonPath + "/SLHub" + "/" + fileName + ".txt";
                File directory = new File(CommonPath + "/SLHub");
                if (!directory.exists()) {
                    boolean isDirectoryCreated = directory.mkdirs();
                    if (isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);
            } catch (Exception e) {
                Log.d("Log Writer", Objects.requireNonNull(e.getMessage()));
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            Date date = new Date();
            System.out.println(dateFormat.format(date));

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(dateFormat.format(date) + " : " + content + " : " + event + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void writeExceptLog(String content, String event) {
        loggingFunction(content, event, "ExceptLog");
    }

    public static void writeUILog(String content, String event) {
        if (disableLog || disableDetailedLog) return;
        loggingFunction(content, hidePartialName(event), "UILog");
    }

    public static void writeSensorLog(String content, String event) {
        loggingFunction(content, hidePartialName(event), "SensorLog");
    }


    private static String hidePartialName(String sensorName) {
        // Define the number of characters to hide
        int charactersToHide = Math.min(2, sensorName.length()); // Hides at most 3 characters

        // Replace characters at indices 0, 1,  with asterisks
        StringBuilder hiddenPartBuilder = new StringBuilder(sensorName);
        for (int i = 0; i < charactersToHide; i++) {
            hiddenPartBuilder.setCharAt(i, '*');
        }

        // Convert StringBuilder back to String
        return hiddenPartBuilder.toString();
    }

    static long startTime = 0;

    public static void timeCheckPointStart() {
        startTime = System.currentTimeMillis();
    }

    static Dictionary<String, Integer> countersDic;
    static Dictionary<String, Long> timersDic;

    public static void initCounter(String name) {
//        if(disableLog) return;
        if (countersDic == null) countersDic = new Hashtable<>();
        if (timersDic == null) timersDic = new Hashtable<>();
        countersDic.put(name, 0);
        timersDic.put(name, System.currentTimeMillis());
    }

    public static void writeException(String exception) throws IOException {
        BufferedWriter bw = null;
        try {
            String filePath;
            //++exceptionCount;
            File file = null;
            try {
                filePath = CommonPath
                        + "/SLHub" + "/NFCExceptions.txt";
                if (!(new File(CommonPath
                        + "/SLHub" + "/")).exists()) {
                    boolean isDirectoryCreated = (new File(CommonPath
                            + "/SLHub" + "/")).mkdirs();
                    if (isDirectoryCreated)
                        Log.d("Directory Creation", "-------------------------------Success-------------------------------");
                }
                file = new File(filePath);

            } catch (Exception e) {
                Log.d("Log Writer", Objects.requireNonNull(e.getMessage()));
            }

            assert file != null;
            FileWriter fw = new FileWriter(file.getAbsoluteFile(), true);
            bw = new BufferedWriter(fw);
            bw.write(exception + "\r\n");
            bw.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bw != null)
                bw.close();
        }
    }
}
