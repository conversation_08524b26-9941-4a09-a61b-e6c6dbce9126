package com.spacelabs.app.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.content.res.ColorStateList
import android.os.Build
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.HorizontalScrollView
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatButton
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.MainActivity
import com.spacelabs.app.adapter.*
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.SibelDatabase
import com.spacelabs.app.database.objectbox.dao.AlarmBoxDao
import com.spacelabs.app.database.objectbox.helpers.MeasurementDataDaoHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class DatabaseDialogController(mainActivity: MainActivity) : DialogManager(mainActivity) {

    //init variable for button for each tables
    private lateinit var btnPatientAlarm: AppCompatButton
    private lateinit var btnAlarms: AppCompatButton
    private lateinit var btnAlarmEvent: AppCompatButton
    private lateinit var btnEventLog: AppCompatButton
    private lateinit var btnMeasurementData: AppCompatButton
    private lateinit var btnUnsendMeasurementData: AppCompatButton
    private lateinit var btnPatient: AppCompatButton
    private lateinit var btnSensor: AppCompatButton
    private lateinit var btnSensorDef: AppCompatButton
    private lateinit var btnVisit: AppCompatButton
    private lateinit var btnSettings: AppCompatButton
    private lateinit var btnEventList: AppCompatButton
    private lateinit var btnDefaultAlarmSettings: AppCompatButton
    private lateinit var btnParameter: AppCompatButton

    private lateinit var patientAlarmHeaderView: View
    private lateinit var alarmHeaderView: View
    private lateinit var alarmEventHeaderView: View
    private lateinit var eventLogHeaderView: View
    private lateinit var measurementDataHeaderView: View
    private lateinit var unSendMeasurementDataHeaderView: View
    private lateinit var patientHeaderView: View
    private lateinit var sensorHeaderView: View
    private lateinit var sensorDefHeaderView: View
    private lateinit var visitHeaderView: View
    private lateinit var settingHeaderView: View
    private lateinit var defaultAlarmSettingsHeaderView: View
    private lateinit var evenListHeaderView: View
    private lateinit var parameterHeaderView: View

    private lateinit var headerScrollView:HorizontalScrollView
    private lateinit var recyclerView: RecyclerView
    private lateinit var selectedButton: AppCompatButton
    private lateinit var closeBtn: Button

    private lateinit var dbInstance: SibelDatabase

    private var allViews: List<View> = listOf()
    private val measurementHelper = MeasurementDataDaoHelper()

    private val fetchFromDbJob = Dispatchers.IO + Job()
    private val fetchDataCoroutine = CoroutineScope(fetchFromDbJob)


    @RequiresApi(Build.VERSION_CODES.S)
    override fun onCreateDialogActions(context: Context, dialog: Dialog, layout: Int) {
        recyclerView = dialog.findViewById(R.id.datas)
        val layoutManager = LinearLayoutManager(context,LinearLayoutManager.VERTICAL,false)
        recyclerView.layoutManager = layoutManager
        dbInstance = CommonDataArea.keystoreManager.getKeyFromKeystore()
            ?.let { SibelDatabase.getInstance(context, it)}!!
    }

    override fun initUIs(dialog: Dialog) {

        btnPatientAlarm = dialog.findViewById(R.id.btn_patient_alarm)
        btnAlarms = dialog.findViewById(R.id.btn_alarm)
        btnAlarmEvent = dialog.findViewById(R.id.btn_alarm_event)
        btnEventLog = dialog.findViewById(R.id.btn_event_log)
        btnMeasurementData = dialog.findViewById(R.id.btn_measurement_data)
        btnUnsendMeasurementData = dialog.findViewById(R.id.btn_unsend_measurement_data)
        btnPatient = dialog.findViewById(R.id.btn_patient)
        btnSensor = dialog.findViewById(R.id.btn_sensor)
        btnSensorDef = dialog.findViewById(R.id.btn_sensor_def)
        btnVisit = dialog.findViewById(R.id.btn_vist)
        btnSettings = dialog.findViewById(R.id.btn_settings)
        btnDefaultAlarmSettings = dialog.findViewById(R.id.btn_default_alarm_settings)
        btnEventList = dialog.findViewById(R.id.btn_event_list)
        btnParameter = dialog.findViewById(R.id.btn_parameter)

        closeBtn = dialog.findViewById(R.id.closeBtn)

        patientAlarmHeaderView = dialog.findViewById(R.id.table_patient_alarm_header)
        alarmHeaderView = dialog.findViewById(R.id.table_alarm_header)
        alarmEventHeaderView = dialog.findViewById(R.id.table_alarm_event_header)
        eventLogHeaderView = dialog.findViewById(R.id.table_event_log_header)
        measurementDataHeaderView = dialog.findViewById(R.id.table_measurement_data_header)
        unSendMeasurementDataHeaderView = dialog.findViewById(R.id.table_unsend_measurement_data_header)
        patientHeaderView = dialog.findViewById(R.id.table_patient_header)
        sensorHeaderView = dialog.findViewById(R.id.table_sensor_header)
        sensorDefHeaderView = dialog.findViewById(R.id.table_sensor_def_header)
        visitHeaderView = dialog.findViewById(R.id.table_visit_header)
        settingHeaderView = dialog.findViewById(R.id.table_setting_header)
        defaultAlarmSettingsHeaderView = dialog.findViewById(R.id.table_default_alarm_settings_header)
        evenListHeaderView = dialog.findViewById(R.id.table_event_list_header)
        parameterHeaderView = dialog.findViewById(R.id.table_parameter_header)

       headerScrollView = dialog.findViewById(R.id.headScrollView)

        // Set the initial background color for the first button
        selectedButton = btnPatientAlarm
        selectedButton.backgroundTintList =
            ColorStateList.valueOf(ContextCompat.getColor(context, R.color.lightBlue))

        // Set click listeners for each button
        btnPatientAlarm.setOnClickListener { handleButtonClick(btnPatientAlarm) }
        btnAlarms.setOnClickListener { handleButtonClick(btnAlarms) }
        btnAlarmEvent.setOnClickListener { handleButtonClick(btnAlarmEvent) }
        btnEventLog.setOnClickListener { handleButtonClick(btnEventLog) }
        btnMeasurementData.setOnClickListener { handleButtonClick(btnMeasurementData) }
        btnUnsendMeasurementData.setOnClickListener { handleButtonClick(btnUnsendMeasurementData) }
        btnPatient.setOnClickListener { handleButtonClick(btnPatient) }
        btnSensor.setOnClickListener { handleButtonClick(btnSensor) }
        btnSensorDef.setOnClickListener { handleButtonClick(btnSensorDef) }
        btnVisit.setOnClickListener { handleButtonClick(btnVisit) }
        btnSettings.setOnClickListener { handleButtonClick(btnSettings) }
        btnDefaultAlarmSettings.setOnClickListener { handleButtonClick(btnDefaultAlarmSettings) }
        btnEventList.setOnClickListener { handleButtonClick(btnEventList) }
        btnParameter.setOnClickListener { handleButtonClick(btnParameter) }
    }

    override fun postUiInitActions(context: Context) {
        fetchAllDetailsFromTblPatientAlarm()
        allViews = listOf(
            patientAlarmHeaderView,
            alarmHeaderView,
            alarmEventHeaderView,
            eventLogHeaderView,
            measurementDataHeaderView,
            unSendMeasurementDataHeaderView,
            patientHeaderView,
            sensorHeaderView,
            sensorDefHeaderView,
            visitHeaderView,
            settingHeaderView,
            defaultAlarmSettingsHeaderView,
            evenListHeaderView,
            parameterHeaderView
        )
    }

    override fun uiActionListeners(context: Context, dialog: Dialog) {
        closeBtn.setOnClickListener {
            closeDialog(dialog)
        }
    }


    private fun handleButtonClick(clickedButton: AppCompatButton) {
        selectedButton.backgroundTintList =
            ColorStateList.valueOf(ContextCompat.getColor(context, R.color.components_gray))
        clickedButton.backgroundTintList =
            ColorStateList.valueOf(ContextCompat.getColor(context, R.color.lightBlue))

        selectedButton = clickedButton
        patientHeaderView.visibility = View.GONE
        alarmHeaderView.visibility = View.GONE
        alarmEventHeaderView.visibility = View.GONE
        eventLogHeaderView.visibility =View.GONE
        measurementDataHeaderView.visibility =View.GONE
        unSendMeasurementDataHeaderView.visibility =View.GONE
        patientHeaderView.visibility =View.GONE
        sensorHeaderView.visibility =View.GONE
        sensorDefHeaderView.visibility =View.GONE
        visitHeaderView.visibility =View.GONE
        settingHeaderView.visibility =View.GONE
        defaultAlarmSettingsHeaderView.visibility =View.GONE
        evenListHeaderView.visibility =View.GONE
        parameterHeaderView.visibility =View.GONE

        when (clickedButton.id) {
            R.id.btn_patient_alarm -> {
                manageLayoutVisibility(patientAlarmHeaderView)
                fetchAllDetailsFromTblPatientAlarm()
            }
            R.id.btn_alarm -> {
                manageLayoutVisibility(alarmHeaderView)
                fetchAllDetailsFromTblAlarms()
            }
            R.id.btn_alarm_event -> {
                manageLayoutVisibility(alarmEventHeaderView)
                fetchAllDetailsFromTblAlarmEvent()
            }
            R.id.btn_event_log -> {
                manageLayoutVisibility(eventLogHeaderView)
                fetchAllDetailsFromTblEventLog()
            }
            R.id.btn_measurement_data -> {
                manageLayoutVisibility(measurementDataHeaderView)
                fetchAllDetailsFromTblMeasureDataTable()
            }
            R.id.btn_unsend_measurement_data -> {
                manageLayoutVisibility(unSendMeasurementDataHeaderView)
                fetchUnsendDetailsFromTblMeasureDataTable()
            }
            R.id.btn_patient -> {
                manageLayoutVisibility(patientHeaderView)
                fetchAllDetailsFromTblPatient()
            }
            R.id.btn_sensor -> {
                manageLayoutVisibility(sensorHeaderView)
                fetchAllDetailsFromTblSensor()
            }
            R.id.btn_sensor_def -> {
                manageLayoutVisibility(sensorDefHeaderView)
                fetchAllDetailsFromTblSensorDef()
            }
            R.id.btn_vist ->{
                manageLayoutVisibility(visitHeaderView)
                fetchAllDetailsFromTblVisit()
            }
            R.id.btn_settings ->{
                manageLayoutVisibility(settingHeaderView)
               fetchAllDetailsFromTblSetting()
            }
            R.id.btn_default_alarm_settings ->{
                manageLayoutVisibility(defaultAlarmSettingsHeaderView)
                fetchAllDetailsFromTblDefaultAlarmSettings()
            }
            R.id.btn_event_list ->{
                manageLayoutVisibility(evenListHeaderView)
                fetchAllDetailsFromTblEventList()
            }
            R.id.btn_parameter ->{
                manageLayoutVisibility(parameterHeaderView)
               fetchAllDetailsFromTblParameter()
            }

        }
    }

    private fun manageLayoutVisibility(requestedView: View){
        for (view in allViews){
            val visibility = if(view == requestedView) View.VISIBLE else View.GONE
            view.visibility = visibility
        }
    }

    private fun fetchAllDetailsFromTblParameter() {
        mainActivityContext.uiScope.launch {
            try {
                val parameterDao = dbInstance.parametersDao
                val event = parameterDao.getAllParametersDescOrder()
                val parameterAdapter = ParameterAdapter(event)
                recyclerView.adapter = parameterAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblEventList() {
        mainActivityContext.uiScope.launch {
            try {
                val eventDao = dbInstance.eventsDao
                val event = eventDao.getAllEventsDescOrder()
                val eventListAdapter = EventListAdapter(event)
                recyclerView.adapter = eventListAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }
    private fun fetchAllDetailsFromTblDefaultAlarmSettings() {
        mainActivityContext.uiScope.launch {
            try {
                val alarmDao = dbInstance.alarmDao
                val alarm = alarmDao.getAllDefaultAlarmSettings()
                val defaultAlarmSettingsAdapter = DefaultAlarmSettingsAdapter(alarm)
                recyclerView.adapter = defaultAlarmSettingsAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblSetting() {
        mainActivityContext.uiScope.launch {
            try {
                val settingDao = dbInstance.settingsDao
                val setting = settingDao.getAllSettingsOrderByDesc()
                val settingAdapter = SettingAdapter(setting)
                recyclerView.adapter = settingAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblVisit() {
            mainActivityContext.uiScope.launch {
                try {
                    val patientDao = dbInstance.patientDao
                    val patient = patientDao.getAllVisit()
                    val visitAdapter = VisitAdapter(patient)
                    recyclerView.adapter = visitAdapter
                } catch (e: Exception) {
                    Log.d("Exception", e.stackTraceToString())
                }
            }
    }

    private fun fetchAllDetailsFromTblSensorDef() {
        mainActivityContext.uiScope.launch {
            try {
                val sensorDao = dbInstance.sensorDao
                val patient = sensorDao.getAllSensorDef()
                val sensorAdapter = SensorDefAdapter(patient)
                recyclerView.adapter = sensorAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblSensor() {
        mainActivityContext.uiScope.launch {
            try {
                val sensorDao = dbInstance.sensorDao
                val patient = sensorDao.getAllSensorOrderByDesc()
                val sensorAdapter = SensorAdapter(patient)
                recyclerView.adapter = sensorAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }

    }

    private fun fetchAllDetailsFromTblMeasureDataTable() {
        fetchDataCoroutine.launch {
            try {
                val measurements = measurementHelper.getAllMeasurementByPatientId(CommonDataArea.PATIENT.patientId)
                val measureDataAdapter = MeasureDataAdapter(measurements)
                mainActivityContext.uiScope.launch {
                    recyclerView.adapter = measureDataAdapter
                }
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }
    private fun fetchUnsendDetailsFromTblMeasureDataTable() {
        fetchDataCoroutine.launch {
            try {
                val measurements = measurementHelper.getMeasurementByUploadStatus2(CommonDataArea.PATIENT.patientId)
                val measureDataAdapter = UnsendMeasureDataAdapter(measurements)
                mainActivityContext.uiScope.launch {
                    recyclerView.adapter = measureDataAdapter
                }
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblEventLog() {
        mainActivityContext.uiScope.launch {
            try {
                val eventsDao = dbInstance.eventsDao
                val patient = eventsDao.getAllEventLog()
                val eventLogAdapter = EventLogAdapter(patient)
                recyclerView.adapter = eventLogAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblPatientAlarm() {
        patientAlarmHeaderView.visibility = View.VISIBLE
        mainActivityContext.uiScope.launch {
            try {
                val alarmDao = dbInstance.alarmDao
                val patient = alarmDao.getAllPatientAlarm()
                val patientAlarmAdapter = PatientAlarmAdapter(patient)
                recyclerView.adapter = patientAlarmAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblAlarms() {
        mainActivityContext.uiScope.launch {
            try {
                val alarms = AlarmBoxDao().getAllAlarmsOfPatient(CommonDataArea.PATIENT.patientId)
                val alarmAdapter = AlarmAdapter(alarms)
                recyclerView.adapter = alarmAdapter
            } catch (e: Exception) {
                Log.d("errorOnFetchAlarms", e.stackTraceToString())
            }
        }
    }

    private fun fetchAllDetailsFromTblAlarmEvent() {
        mainActivityContext.uiScope.launch {
            try {
                val alarmEvents = AlarmBoxDao().getAllAlarmEventOfPatient()
                val alarmEventAdapter = AlarmEventAdapter(alarmEvents)
                recyclerView.adapter = alarmEventAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }

    }

    private fun fetchAllDetailsFromTblPatient() {
        mainActivityContext.uiScope.launch {
            try {
                val patientDao = dbInstance.patientDao
                val patientList = patientDao.getAllPatient()
                val patientAdapter = PatientAdapter(patientList)
                recyclerView.adapter = patientAdapter
            } catch (e: Exception) {
                Log.d("Exception", e.stackTraceToString())
            }
        }
    }
}