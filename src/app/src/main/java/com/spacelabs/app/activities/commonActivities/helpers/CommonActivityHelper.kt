package com.spacelabs.app.activities.commonActivities.helpers

import android.content.Context
import android.util.Log
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.KeystoreManager
import com.spacelabs.app.database.SibelDatabase
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

open class CommonActivityHelper(context: Context) {

    private val context: Context
    init {
        this.context = context
    }

    private lateinit var settingDaoHelper: SettingDaoHelper

    private val ioScope = CoroutineScope(Dispatchers.IO + Job())

    protected fun setupKeyStore(){
        CommonDataArea.keystoreManager = getKeyStoreManager("mykey")
        CommonDataArea.measurementDao = CommonDataArea.keystoreManager.getKeyFromKeystore()
            ?.let { SibelDatabase.getInstance(context, it) }!!.measurementDao
    }

    protected fun saveSettingsAndNavigate(value: String, settings: SettingDaoHelper.AppSettings){
        ioScope.launch {
            settingDaoHelper.updateSettings(settings, value)
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.NavigateToNext, null)
            Log.d("SAVE_ACCOUNT_ID", "saveSettingsAndNavigate: $value ,And $settings")
        }
    }

    protected fun navigateIfSettingExist(key: String){
        ioScope.launch {
            settingDaoHelper = SettingDaoHelper(context)
            val hasValue = settingDaoHelper.hasSettings(key)
            if(hasValue)
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.NavigateToNext, null)
        }
    }
    protected fun navigateIfHasAuthenticatedExist(key: String){
        Log.d("SNS_NAVIGATION_DEBUG", "navigateIfHasAuthenticatedExist: key = '$key'")
        ioScope.launch {
          /*  settingDaoHelper = SettingDaoHelper(context)
            val hasValue = settingDaoHelper.hasSettings(key)*/
            if(key =="true") {
                Log.d("SNS_NAVIGATION_DEBUG", "navigateIfHasAuthenticatedExist: key is 'true', posting NavigateToNext event")
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.NavigateToNext, null)
            } else {
                Log.d("SNS_NAVIGATION_DEBUG", "navigateIfHasAuthenticatedExist: key is not 'true', no navigation")
                Log.d("DEVICE_EXIST", "navigateIfHasAuthenticatedExist: ${key}")
            }
        }
    }


    private fun getKeyStoreManager(key: String): KeystoreManager {
        val keystoreManager = KeystoreManager(context)
        keystoreManager.saveKeyToKeystore(key)
        return keystoreManager
    }


    protected fun saveDeviceUuidOrPass(value: String, settings: SettingDaoHelper.AppSettings){
        settingDaoHelper = SettingDaoHelper(context)
        settingDaoHelper.updateSettings(settings, value)

    }

    protected suspend fun getDeviceUUidOrPass(key: String):Boolean {

            settingDaoHelper = SettingDaoHelper(context)
            val hasValue = settingDaoHelper.hasDeviceSettings(key)
            return  hasValue

    }


    protected fun saveDeviceRegisteredSettings(value: String, settings: SettingDaoHelper.AppSettings){
        ioScope.launch {
            settingDaoHelper.updateSettings(settings, value)
        }
    }

}