package com.spacelabs.app.activities.commonActivities

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Bundle
import android.transition.Slide
import android.util.Log


import android.view.Window
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.spacelabs.app.R
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.activities.commonActivities.activityUi.WifiConfigActivityUi
import com.spacelabs.app.activities.commonActivities.helpers.WifiConfigActivityHelper
import com.spacelabs.app.common.CommonEventHandler

class WifiConfigActivity : AppCompatActivity() {

     val wifiConfigActivityHelper by lazy { WifiConfigActivityHelper(this) }
     val wifiConfigActivityUi by lazy { WifiConfigActivityUi(this) }
     lateinit var wifiManager: WifiManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            setTheme(R.style.Theme_SibelPatch)
            setWindowProperties()
            setContentView(R.layout.wifi_config_activity)

            wifiManager = getSystemService(Context.WIFI_SERVICE) as WifiManager
            wifiConfigActivityUi.setUpActivityUi()
            wifiConfigActivityHelper.initialSetup()

           /* Thread.setDefaultUncaughtExceptionHandler { _, throwable ->
                Log.e("Global Crash Handler", throwable.stackTraceToString())
            }*/
        } catch (ex: Exception) {
            Log.e("Exception", ex.stackTraceToString())
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun onStart() {
        super.onStart()
        registerReceivers()
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun onStop() {
        super.onStop()
        unregisterReceivers()
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun registerReceivers() {
        CommonEventHandler.registerUiEventCallback(wifiConfigActivityUi.UiEventHandler(), WifiConfigActivity::class.java)
        wifiConfigActivityHelper.registerWifiReceiver(wifiManager)
        registerReceiver(broadcastReceiver, IntentFilter(WifiManager.ACTION_WIFI_NETWORK_SUGGESTION_POST_CONNECTION))
        wifiConfigActivityHelper.registerNetworkCallback()
    }

    private fun unregisterReceivers() {
        wifiConfigActivityHelper.unregisterWifiReceiver()
        unregisterReceiver(broadcastReceiver)
        wifiConfigActivityHelper.unregisterCallbacksAndCoroutines()
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
    }

    private val broadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == WifiManager.ACTION_WIFI_NETWORK_SUGGESTION_POST_CONNECTION) {
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.NavigateToNext, null)
            }
        }
    }

    private fun setWindowProperties() {
        with(window) {
            requestFeature(Window.FEATURE_ACTIVITY_TRANSITIONS)
            enterTransition = Slide()
            exitTransition = android.transition.Fade()
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.decorView.windowInsetsController?.hide(android.view.WindowInsets.Type.statusBars())
        }
    }
}
