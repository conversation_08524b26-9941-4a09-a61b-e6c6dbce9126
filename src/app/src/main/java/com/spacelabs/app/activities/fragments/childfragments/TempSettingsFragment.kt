package com.spacelabs.app.activities.fragments.childfragments

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.widget.EditText
import android.widget.RadioGroup
import android.widget.Switch
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.AlarmDaoHelper
import java.util.AbstractMap

class TempSettingsFragment(val settingsHelper: ParamSettingsHelper): Fragment(R.layout.settings_dialog_temp) {

    //UI Variables
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private lateinit var tempAlarmSwitch: Switch

    private lateinit var currentSensor: RadioGroup

    private lateinit var highTemp: EditText
    private lateinit var highIncButton: AppCompatButton
    private lateinit var highDecButton: AppCompatButton

    private lateinit var lowTemp: EditText
    private lateinit var lowIncButton: AppCompatButton
    private lateinit var lowDecButton: AppCompatButton


    //Normal Variables
    private lateinit var alarmDaoHelper: AlarmDaoHelper
    private var currentTempSensor: Sensor? = null

    private lateinit var alarmParamsDao: AlarmParametersDao
    private lateinit var incButtons: Map<AppCompatButton, EditText>
    private lateinit var decButtons: Map<AppCompatButton, EditText>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        alarmDaoHelper = AlarmDaoHelper(context!!)
        alarmParamsDao = CommonDataArea.tempAlarmDao

        tempAlarmSwitch = view.findViewById(R.id.tempAlarmSwitch)
        currentSensor = view.findViewById(R.id.currentSensor)

        highTemp = view.findViewById(R.id.highTemp)
        highIncButton = view.findViewById(R.id.tempHighIncButton)
        highDecButton = view.findViewById(R.id.tempHighDecButton)

        lowTemp = view.findViewById(R.id.lowTemp)
        lowIncButton = view.findViewById(R.id.tempLowIncButton)
        lowDecButton = view.findViewById(R.id.tempLowDecButton)
    }

    override fun onStart() {
        super.onStart()
        postUiInitActions()
        uiActionListeners()
    }

    private fun postUiInitActions() {
        incButtons = mapOf(highIncButton to highTemp, lowIncButton to lowTemp)
        decButtons = mapOf(highDecButton to highTemp, lowDecButton to lowTemp)

        tempAlarmSwitch.isChecked = alarmParamsDao.alarmStatus

        if(tempAlarmSwitch.isChecked){
            tempAlarmSwitch.thumbTintList = context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
            tempAlarmSwitch.trackTintList = context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
        }else{
            tempAlarmSwitch.thumbTintList = context?.resources?.getColorStateList(R.color.white, context!!.theme)
            tempAlarmSwitch.trackTintList = context?.resources?.getColorStateList(R.color.white, context!!.theme)
        }

        highTemp.setText("${alarmParamsDao.highValue.toInt()}")
        lowTemp.setText("${alarmParamsDao.lowValue.toInt()}")

        getCurrentTempSensorType(currentSensor)
    }

    private fun uiActionListeners() {
        settingsHelper.incDecButtonsActionListeners(incButtons, decButtons, 1)

        tempAlarmSwitch.setOnCheckedChangeListener { _, isChecked ->
            getCurrentTempSensorType(currentSensor)
            val hasActionCompleted = settingsHelper.alarmSwitchActionListener(tempAlarmSwitch, isChecked, arrayOf(
                StreamDataType.TEMP_SKIN), currentTempSensor)
            if(hasActionCompleted) {
                alarmParamsDao.alarmStatus = isChecked
                alarmDaoHelper.updatePatientAlarmToDb(alarmParamsDao)
                settingsHelper.uiEventOnSwitchAction(alarmParamsDao)
            } else {
                tempAlarmSwitch.isChecked = !isChecked
                Toast.makeText(context, "Action Not Allowed, No Sensor Found", Toast.LENGTH_SHORT).show()
            }
        }

        currentSensor.setOnCheckedChangeListener { _, checkedId ->
            when(checkedId){
                R.id.chestRadio -> {
                    CommonDataArea.tempDisplaySensorType = SensorType.CHEST
                    CommonDataArea.temperature = Double.NaN
                }
                R.id.limbRadio -> {
                    CommonDataArea.tempDisplaySensorType = SensorType.LIMB
                    CommonDataArea.temperature = Double.NaN
                }
            }
        }

        val highEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(highTemp, ParamSettingsHelper.SettingsType.High)
        val lowEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(lowTemp, ParamSettingsHelper.SettingsType.Low)
        settingsHelper.settingsValueTextChangedActionListener(highEditText, alarmParamsDao, alarmDaoHelper)
        settingsHelper.settingsValueTextChangedActionListener(lowEditText, alarmParamsDao, alarmDaoHelper)

    }

    private fun getCurrentTempSensorType(sensorRadioGroup: RadioGroup) {
        currentTempSensor =  when(CommonDataArea.tempDisplaySensorType){
            SensorType.LIMB -> {
                sensorRadioGroup.check(R.id.limbRadio)
                CommonDataArea.limbSensor
            }

            else -> {
                sensorRadioGroup.check(R.id.chestRadio)
                CommonDataArea.chestSensor
            }
        }
    }

}