package com.spacelabs.app.iomt.data;

import androidx.room.TypeConverter;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

public class Converters {

    @TypeConverter
    public static float[] fromString(String value) {
        return new Gson().fromJson(value, float[].class);
    }

    @TypeConverter
    public static String fromArray(float[] array) {
        return new Gson().toJson(array);
    }
}
