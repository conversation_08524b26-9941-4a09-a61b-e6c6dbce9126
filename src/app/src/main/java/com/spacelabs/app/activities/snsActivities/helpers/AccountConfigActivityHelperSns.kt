package com.spacelabs.app.activities.snsActivities.helpers

import android.content.Context
import android.util.Log
import com.spacelabs.app.activities.commonActivities.helpers.CommonActivityHelper
import com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
import com.spacelabs.app.database.daoHelper.SettingDaoHelper

class AccountConfigActivityHelperSns(accountConfigActivity: AccountConfigActivitySns): CommonActivityHelper(accountConfigActivity) {

    private val accountConfActivity: AccountConfigActivitySns
    private val context: Context
    init {
        this.accountConfActivity = accountConfigActivity
        context = accountConfigActivity
    }

  /*  fun navigateIfAccountIdExists(){
        navigateIfSettingExist(SettingDaoHelper.AppSettings.HasAuthenticated.key)
    }*/

    fun navigateIfAccountIdExists() {
        val hasAuthenticatedValue = SettingDaoHelper.AppSettings.HasAuthenticated.getValue()
        Log.d("SNS_NAVIGATION_DEBUG", "navigateIfAccountIdExists: HasAuthenticated = '$hasAuthenticatedValue'")
        Log.d("SNS_NAVIGATION_DEBUG", "navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist")
        navigateIfHasAuthenticatedExist(hasAuthenticatedValue)
        Log.d("DEVICE_EXIST", "navigateIfAccountIdExists: ${hasAuthenticatedValue}")
    }

    fun saveAccountIdSettingsAndNavigateToNext(value: String){
        saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.HasAuthenticated)
    }


    fun saveDeviceValues(key: Int, value: String) {
        when (key) {
            0 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.DeviceUuid)
            else -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.DevicePass)
        }
    }

}