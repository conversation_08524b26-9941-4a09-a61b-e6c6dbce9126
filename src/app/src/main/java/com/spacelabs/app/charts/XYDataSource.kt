package com.spacelabs.app.charts

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Typeface
import android.util.Log
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.utils.ColorTemplate
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.ecgXYData
import com.spacelabs.app.common.CommonDataArea.Companion.isECGWritePtrUpdated
import com.spacelabs.app.common.CommonDataArea.Companion.isPPGWritePtrUpdated
import com.spacelabs.app.common.CommonDataArea.Companion.isRESPWritePtrUpdated
import com.spacelabs.app.common.CommonDataArea.Companion.masterStream
import com.spacelabs.app.common.CommonDataArea.Companion.ppgIRXYData
import com.spacelabs.app.common.CommonDataArea.Companion.respXYData
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils

open class XYDataSource {

    val tfLight: Typeface = Typeface.DEFAULT

    enum class XYDataType {
        ECG,
        Resp,
        PPG_IR,
        SPO2,
        RR,
        HR,
    }

    var maxData = 0
    var xyDataType: XYDataType = XYDataType.ECG
    val values = ArrayList<Entry>()
    var writePtr = 0
    var isFilling = false
    var zeroHitTime: Long = 0
    open val highlightPixels = 3
    /*Sampling Rate*/
    var ecgSamplingRate =2500/*250*10*/
    var respSamplingRate =258 /*26*10*//*416*10 =4158 fda version */ /*Use 258 due  wave syncing issues sdk v1.5.1*//*For Emulator use 370*/
    var ppgSamplingRate =620/*62*10*/
    var hrSamplingRate =240
    var spo2SamplingRate =240

    var highlighterColor= Color.rgb(31, 31, 31)

    open fun initialize(xyDataType: XYDataType) {
        this.xyDataType = xyDataType
        when (xyDataType) {
            XYDataType.ECG -> maxData = ecgSamplingRate
            XYDataType.Resp -> maxData = respSamplingRate
            XYDataType.PPG_IR -> maxData = ppgSamplingRate
            XYDataType.HR -> maxData = hrSamplingRate //one data for a minute and trend for 4 hours
            XYDataType.SPO2 -> maxData = spo2SamplingRate //one data for a minute and trend for 4 hours
            else -> Log.d("chartInitialize", "Unknown chart type")
        }
        fillSine()
    }

    private fun fillSine() {
        for (i in 0 until maxData) {
            val value = 0
            values.add(Entry(i.toFloat(), value.toFloat()))
        }
    }

    open fun setFrequency(freq: Int) {
    }

    fun filling(fillStatus: Boolean) {
        isFilling = fillStatus
    }


    @SuppressLint("SuspiciousIndentation")
    open fun calculateFillingPosition() {
        var writePtrCopy=0
        when (masterStream) {
            XYDataType.ECG.name -> {
                writePtrCopy =
                    (maxData * CommonDataArea.currentFillingTimePos / CommonDataArea.windowSizeInSeconds).toInt()
                if (!isRESPWritePtrUpdated || !isPPGWritePtrUpdated) {
                    writePtr = writePtrCopy
                }
                updateMasterStream(XYDataType.ECG)
            }

            XYDataType.PPG_IR.name -> {
                writePtrCopy =
                    (maxData * CommonDataArea.currentFillingTimePos / CommonDataArea.windowSizeInSeconds).toInt()
                if (!isECGWritePtrUpdated || !isRESPWritePtrUpdated) {
                    writePtr = writePtrCopy
                }
                updateMasterStream(XYDataType.ECG)
            }

            XYDataType.RR.name -> {
                writePtrCopy =
                    (maxData * CommonDataArea.currentFillingTimePos / CommonDataArea.windowSizeInSeconds).toInt()
                if (!isPPGWritePtrUpdated || !isRESPWritePtrUpdated) {
                    writePtr = writePtrCopy
                }
                updateMasterStream(XYDataType.ECG)
            }
        }
    }

    open fun updateMasterStream(streamType: XYDataType) {
        if (streamType.ordinal < xyDataType.ordinal) {
            masterStream = streamType.name
            isPPGWritePtrUpdated = true
            isECGWritePtrUpdated = true
            isRESPWritePtrUpdated = true
        }
    }

    open fun updateFillPosTime() {

        val curFillPosTime = CommonDataArea.windowSizeInSeconds * writePtr.toFloat() / (maxData)
        // If CommonDataArea.currentFillingTimePos < curFillPosTime, update it
        if (CommonDataArea.currentFillingTimePos < curFillPosTime) {
            CommonDataArea.currentFillingTimePos = curFillPosTime
        }
        // If CommonDataArea.currentFillingTimePos >= CommonDataArea.windowSizeInSeconds, reset it to 0
        if (CommonDataArea.currentFillingTimePos >= CommonDataArea.windowSizeInSeconds) {
            CommonDataArea.currentFillingTimePos = 0f
        }
    }

    open fun addData(value: Float) {
        try {
            if (writePtr == 0 && !isAllOnZero() && !isWaitTimeOut()) {
                return
            }
            resetTimer()

            val valuesCloned = values.toMutableList()
            val valueEntry = valuesCloned[writePtr]
            valueEntry.y = value
            valueEntry.x = writePtr.toFloat()
            ++writePtr

            updateFillPosTime()

            if (writePtr >= maxData) {
                Log.i("Sibel", "Resetting write Ptr")
                writePtr = 0
                zeroHitTime = TimestampUtils.getCurrentTimeMillis()
            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("XyDataSource.AddData", ex.message)
        }
    }

    val highlighPixels = 5

    open fun updateData(chart: LineChart, color: Int) {
        try {
            val set: LineDataSet = chart.data.getDataSetByIndex(0) as LineDataSet
            if (values.isNotEmpty()) {
                set.values = values
                set.notifyDataSetChanged()
                set.calcMinMax()
                //CATION!!!! write PTR can be changed while below loop executes
                //A copy of writePtr may help avoid that
                val writePtrTemp = writePtr
                if (writePtrTemp < maxData - highlighPixels) {
                    chart.highlightValue(0.0f, -1)
                    val valuesCloned = values.toMutableList()
                    for (i in writePtrTemp + 1..writePtrTemp + (highlighPixels)) {
                        val valueEntry = valuesCloned[i]
                        valueEntry.x = i.toFloat()
                        val highlight = Highlight(valueEntry.x, valueEntry.y, 0)
                        highlight.dataIndex = 0
                        chart.highlightValue(highlight, false)
                        set.highlightLineWidth = 10f
                        set.setDrawHorizontalHighlightIndicator(false)
                        set.highLightColor = highlighterColor
                    }
                }
                val diff = set.yMax - set.yMin
                val leftAxis: YAxis = chart.axisLeft
                leftAxis.axisMaximum = set.yMax + diff / 10
                leftAxis.axisMinimum = set.yMin - diff / 10

                val rightAxis: YAxis = chart.axisRight
                rightAxis.axisMaximum = set.yMax + diff / 10
                rightAxis.axisMinimum = set.yMin - diff / 10

                chart.data.notifyDataChanged()
                chart.notifyDataSetChanged()
                chart.invalidate()
            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("Update Chart->" + xyDataType.name, "Exception->" + ex.stackTraceToString())
        }
    }

    fun setData(chart: LineChart, color: Int) {
        val set: LineDataSet

        if (chart.data != null && chart.data.dataSetCount > 0) {
            set = chart.data.getDataSetByIndex(0) as LineDataSet
            set.values = values
            set.notifyDataSetChanged()
            set.color = color

            chart.data.notifyDataChanged()
            chart.notifyDataSetChanged()
            chart.invalidate()
        } else {
            // create a dataset and give it a type
            set = LineDataSet(values, "DataSet 1")
            set.mode = LineDataSet.Mode.CUBIC_BEZIER
            set.setDrawCircles(false)
            set.axisDependency = YAxis.AxisDependency.LEFT
            set.color = color

            set.lineWidth = 1f
            set.fillAlpha = 65
            set.fillColor = ColorTemplate.getHoloBlue()
            set.highLightColor = color

            // create a data object with the data sets
            val lineData = LineData(set)
            lineData.setValueTextColor(Color.WHITE)
            lineData.setValueTextSize(9f)
            chart.data = lineData
            chart.invalidate()
        }
    }

    open fun setupYaxis(chart: LineChart, yMax: Float, yMin: Float) {
        chart.isAutoScaleMinMaxEnabled = false
        val leftAxis: YAxis = chart.axisLeft
        val rightAxis: YAxis = chart.axisRight

        setAxisMaxAndMin(leftAxis, rightAxis, yMax, yMin)

        leftAxis.typeface = tfLight
        leftAxis.textColor = ColorTemplate.getHoloBlue()
        leftAxis.setDrawGridLines(false)
        leftAxis.isGranularityEnabled = true

        rightAxis.typeface = tfLight
        rightAxis.textColor = Color.RED
        rightAxis.setDrawGridLines(false)
        rightAxis.setDrawZeroLine(false)
        rightAxis.isGranularityEnabled = false
    }

    private fun setAxisMaxAndMin(leftAxis: YAxis, rightAxis: YAxis, yMax: Float, yMin: Float) {
        if (yMax == 0f) {
            leftAxis.axisMaximum = 1f
            leftAxis.axisMinimum = -1f
            rightAxis.axisMaximum = 1f
            rightAxis.axisMinimum = -1f
        } else {
            leftAxis.axisMaximum = yMax
            leftAxis.axisMinimum = yMin
            rightAxis.axisMaximum = yMax
            rightAxis.axisMinimum = yMin
        }
    }

    open fun setupXAxis(chart: LineChart) {
        val xAxis: XAxis = chart.xAxis
        xAxis.typeface = tfLight
        xAxis.textSize = 11f
        xAxis.textColor = Color.WHITE
        xAxis.setDrawGridLines(false)
        xAxis.setDrawAxisLine(false)
    }

    fun hideInfo(chart: LineChart) {
        chart.axisLeft.setDrawLabels(false)
        chart.axisRight.setDrawLabels(false)
        chart.xAxis.setDrawLabels(false)
        chart.xAxis.setDrawAxisLine(false)
        chart.axisLeft.setDrawAxisLine(false)
        chart.xAxis.setDrawAxisLine(false)
        chart.axisRight.setDrawAxisLine(false)
        chart.description.isEnabled = false
        chart.legend.isEnabled = false
    }

    private fun isAtZero(): Boolean {
        if (writePtr == 0) return true
        val percent = writePtr * 100f / values.size

        if (percent < 10) return true
        return false
    }

    fun isWaitTimeOut(): Boolean {
        return if(xyDataType==XYDataType.ECG){
            if ((TimestampUtils.getCurrentTimeMillis() - zeroHitTime) > 2000) {
                zeroHitTime = 0
                true
            } else false
        } else {
            if ((TimestampUtils.getCurrentTimeMillis() - zeroHitTime) > 10000) {
                zeroHitTime = 0
                true
            } else false
        }
    }

    fun isAllOnZero():Boolean{
        return when {
            CommonDataArea.limbSensor != null && CommonDataArea.chestSensor != null -> isAllOnZeroWithAllSensors()
            CommonDataArea.limbSensor == null && CommonDataArea.chestSensor != null -> isAllOnZeroWithChestSensor()
            CommonDataArea.limbSensor != null && CommonDataArea.chestSensor == null -> isAllOnZeroWithLimbSensor()
            else -> false
        }
    }

    private fun isParamStreaming(alarmParamDao: AlarmParametersDao): Boolean {
        return alarmParamDao.isAttached && alarmParamDao.alarmStatus
    }

    private fun isAllOnZeroWithAllSensors(): Boolean {
        val isHrStreaming = isParamStreaming(CommonDataArea.hrAlarmDao)
        val isRrStreaming = isParamStreaming(CommonDataArea.respAlarmDao)
        val isSpo2Streaming = isParamStreaming(CommonDataArea.spo2AlarmDao)

        val dataSources = when {
            isHrStreaming && isRrStreaming && isSpo2Streaming -> listOf(ecgXYData, respXYData, ppgIRXYData)
            !isHrStreaming && isRrStreaming && isSpo2Streaming -> listOf(respXYData, ppgIRXYData)
            isHrStreaming && !isRrStreaming && isSpo2Streaming -> listOf(ecgXYData, ppgIRXYData)
            isHrStreaming && isRrStreaming && !isSpo2Streaming -> listOf(ecgXYData, respXYData)
            isHrStreaming && !isRrStreaming && !isSpo2Streaming -> listOf(ecgXYData)
            !isHrStreaming && isRrStreaming && !isSpo2Streaming -> listOf(respXYData)
            !isHrStreaming && !isRrStreaming && isSpo2Streaming -> listOf(ppgIRXYData)
            else -> null
        } ?: return false

        if (dataSources.isEmpty()) return false

        return checkIfTheDataSourcesAreAtZero(dataSources)
    }

    private fun checkIfTheDataSourcesAreAtZero(dataSources: List<XYDataSource?>): Boolean {
        var isAtZero = true
        dataSources.forEach {
            isAtZero = isAtZero && it?.isAtZero() == true
        }
        return isAtZero
    }

    private fun isAllOnZeroWithChestSensor(): Boolean {
        val isHrStreaming = isParamStreaming(CommonDataArea.hrAlarmDao)
        val isRrStreaming = isParamStreaming(CommonDataArea.respAlarmDao)
        val dataSources = when {
            isHrStreaming && isRrStreaming -> listOf(ecgXYData, respXYData)
            isHrStreaming && !isRrStreaming -> listOf(ecgXYData)
            !isHrStreaming && isRrStreaming -> listOf(respXYData)
            else -> null
        } ?: return false

        return checkIfTheDataSourcesAreAtZero(dataSources)
    }

    private fun isAllOnZeroWithLimbSensor(): Boolean {
        val isSpo2Streaming = isParamStreaming(CommonDataArea.spo2AlarmDao)
        return if(isSpo2Streaming) {
            checkIfTheDataSourcesAreAtZero(listOf(ppgIRXYData))
        } else false
    }

    fun resetTimer() {
        if ((TimestampUtils.getCurrentTimeMillis() - zeroHitTime) > 10000) {
            zeroHitTime = 0
        }
    }
}