package com.spacelabs.app.activities.iomtActivities.activityUi

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.os.Build
import android.util.Log
import android.widget.ImageView
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatButton
import com.spacelabs.app.MainActivity
import com.spacelabs.app.R
import com.spacelabs.app.activities.commonActivities.activityUi.ActivityUiController
import com.spacelabs.app.activities.iomtActivities.AccountConfigActivity
import com.spacelabs.app.activities.iomtActivities.QrScannerActivity
import com.spacelabs.app.activities.dialogs.FullScreenDialog
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.encryptWithPublicKey
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.generateQRCode
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.generateRandomPassphrase
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.generateRandomUUID
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.loadPublicKeyFromAssets
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.iomt.IomtApiManager
import com.spacelabs.app.iomt.NetworkUtils
import com.spacelabs.app.iomt.apiHandler.AuthenticationHandler
import com.spacelabs.app.ui.dialogs.ProgressDialogHandler
import java.security.PublicKey

@SuppressLint("HardwareIds")
class AccountConfigActivityUi(accountConfigActivity: AccountConfigActivity) :
    ActivityUiController(accountConfigActivity) {

    private lateinit var qrCodeImageView: ImageView
    private lateinit var icLogoImageView: ImageView

    private lateinit var nextBtn: AppCompatButton

    private val accountConfigActivity: AccountConfigActivity

    private val authenticationHandler = AuthenticationHandler()

    private var publicKey: PublicKey? = null

    private val progressDialogHandler = ProgressDialogHandler(accountConfigActivity)



    init {
        this.accountConfigActivity = accountConfigActivity
    }

    override fun initUi() {

        icLogoImageView = accountConfigActivity.findViewById(R.id.icLogoImageView)

        qrCodeImageView = accountConfigActivity.findViewById(R.id.qrCodeImageView)

        nextBtn = accountConfigActivity.findViewById(R.id.nextButton)

        publicKey = loadPublicKeyFromAssets()

    }

    override fun postInitUiActions() {
        Log.d("PostInitUi", "Not yet implemented")
        if (!enableSnsApi) {
            generateAndDisplayQRCode()
        }
    }

    override fun uiActionListeners() {
        nextBtn.setOnClickListener {
            onNextButtonClickedAction()

            /*For Skipping Register and directly to MainActivity Comment  onNextButtonClickedAction() and uncomment below */
          /*  val intent = Intent(context, MainActivity::class.java)
            accountConfigActivity.startActivity(intent)*/

        }

    }

    private fun onNextButtonClickedAction() {

            if (NetworkUtils.isNetworkAvailable(context)) {
                /*showToast("Checking For Device Registration...")*/
                progressDialogHandler.showProgressDialog("Checking For Device Registration...")
                authenticationHandler.isDeviceRegistered(SettingDaoHelper.AppSettings.DeviceUuid.getValue()) { isRegistered, message ->
                    if (isRegistered) {
                        accountConfigActivity.accountConfigHelper.saveDeviceRegisterSettings(
                            "true"
                        )
                        showFullScreenDialog(
                            "Device Registered Successfully"," Please ensure the device is turned off",
                            true
                        )
                    } else {
                        /*showToast(message)*/
                        showFullScreenDialog(
                            "Registration Unsuccessful", "Please ensure the device is connected to a stable network/server",
                            false
                        )
                    }
                }


            } else showToast("No Internet connection")
    }

    @SuppressLint("HardwareIds")
    private fun generateAndDisplayQRCode() {
        try {
            // Get device details
            val androidId = android.provider.Settings.Secure.getString(
                accountConfigActivity.contentResolver,
                android.provider.Settings.Secure.ANDROID_ID
            )
            val deviceUuid =  if (SettingDaoHelper.AppSettings.DeviceUuid.getValue().length!=36)  generateRandomUUID() else SettingDaoHelper.AppSettings.DeviceUuid.getValue()
            accountConfigActivity.accountConfigHelper.saveDeviceValues(0, deviceUuid)
            val devicePassPhrase =  if (SettingDaoHelper.AppSettings.DevicePass.getValue().length!=16) generateRandomPassphrase(16) else SettingDaoHelper.AppSettings.DevicePass.getValue()
            accountConfigActivity.accountConfigHelper.saveDeviceValues(1, devicePassPhrase)

            CommonDataArea.ANDROID_ID = androidId
            val osVersion = Build.VERSION.RELEASE
            val deviceConfig = Build.MANUFACTURER + " " + Build.MODEL

            val deviceDetails =
                "Android ID: $androidId\nOS Version: $osVersion \nDevice: $deviceConfig\nUUID: $deviceUuid\nPassphrase: $devicePassPhrase"
            /*val deviceDetails =
                "Android ID: $androidId\nOS Version: $osVersion\nDevice: $deviceConfig"*//*Old way of display Qr */
            if (deviceDetails.isNotBlank()) {
                // Generate and display QR code
                val encryptedDevice = encryptWithPublicKey(publicKey, deviceDetails)
                Log.d("deviceDetails", "generateAndDisplayQRCode: $deviceDetails")
                Log.d("deviceDetails", "generateAndDisplayQRCode: $encryptedDevice")
                val qrCodeBitmap: Bitmap? = generateQRCode(encryptedDevice)
                qrCodeImageView.setImageBitmap(qrCodeBitmap)
            } else {
                Log.e("QRcode", "Empty device details for QR code generation")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("QRcode", "Error generating QR code: ${e.message}")
        }
    }

    private fun showFullScreenDialog(replyMessage: String,replySubMessage: String, isreplyMessageSuccess: Boolean) {
        val dialog = FullScreenDialog(accountConfigActivity, replyMessage,replySubMessage, isreplyMessageSuccess)
        progressDialogHandler.dismissProgressDialog()
        dialog.show()
    }



    inner class UiEventHandler : UiEventCallback {
        @RequiresApi(Build.VERSION_CODES.P)
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            when (event) {
                UiEventCallback.UiEventType.NavigateToNext -> {
                   /* if (enableSnsApi) {
                        navigateToNextActivity(MainActivity::class.java)
                    } else {*/
                        navigateToNextActivity(
                            if (IomtApiManager.DEVICE_EXIST.isEmpty()) AccountConfigActivity::class.java
                            else if (IomtApiManager.ACCOUNT_ID.isEmpty()) QrScannerActivity::class.java
                            else MainActivity::class.java
                        )
                    //}
                }

                else -> Log.d("UiEvent", "ACCOUNT_ACTIVITY_UNKNOWN EVENT -> $event")
            }
        }
    }


}
