package com.spacelabs.app.sensor.helper

import android.util.Log
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.MainActivity
import com.spacelabs.app.R
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.dataManager.PatientDataManager
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.sensor.SibelSensorManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

abstract class SensorObserverHelper {

    lateinit var mnActivity: MainActivity

    val patientDataManager = PatientDataManager()

    private lateinit var sensor: Sensor
    private val connectionUpdateCoroutine = CoroutineScope(Dispatchers.Default)

    companion object {
        var chestSensorConnectedTime: String? = null
        var limbSensorConnectedTime: String? = null
        var bpSensorConnectedTime: String? = null
    }

    fun onConnectionStatusUpdatedAction(connectionStatus: ConnectionStatus, sensor: Sensor){
        mnActivity = CommonDataArea.curActivity!!
        this.sensor = sensor
        checkConnectionStatus(connectionStatus)
    }

    private fun checkConnectionStatus(connectionStatus: ConnectionStatus){
        when(connectionStatus){
            ConnectionStatus.CONNECTING -> onSensorConnectingAction(sensor)
            ConnectionStatus.CONNECTED -> onSensorConnectedAction(sensor)
            ConnectionStatus.CONNECTION_LOST -> onSensorConnectionLostAction(sensor)
            ConnectionStatus.DISCONNECTED -> {
                sendConnectivityAlarm(false, sensor.sensorType)
                onSensorDisconnectAction(sensor)
            }
        }
        CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.ConnectionStatusUpdate, Pair(sensor, connectionStatus))
    }

    abstract fun onSensorConnectingAction(sensor: Sensor)

    abstract fun onSensorConnectedAction(sensor: Sensor)

    abstract fun onSensorConnectionLostAction(sensor: Sensor)

    abstract fun onSensorDisconnectAction(sensor: Sensor)

    fun onBatteryLevelUpdatedAction(sensor: Sensor, batteryLevel: Double){
        try {
            when (sensor.sensorType) {
                SensorType.CHEST -> CommonDataArea.chestBattery = batteryLevel
                SensorType.LIMB -> CommonDataArea.limbBattery = batteryLevel
                else -> Log.e("batteryLevel", "Incompatible Sensor Type")
            }
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorBatteryChanged, sensor.sensorType)
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onBatteryLevelUpdated", e.message)
        }
    }

    protected fun updateSensorConnectedTime(sensorType: SensorType){
        try {
            val formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME
            val offsetDateTime = OffsetDateTime.now(ZoneOffset.UTC)
            val connectedTime = offsetDateTime.format(formatter)
            when(sensorType){
                SensorType.CHEST -> chestSensorConnectedTime = connectedTime
                SensorType.LIMB -> limbSensorConnectedTime = connectedTime
                SensorType.BP2_BLOOD_PRESSURE_MONITOR -> bpSensorConnectedTime = connectedTime
                else -> Log.w("typeError", "Undefined Sensor Type")
            }
        } catch (ex: Exception) {
            Log.d("SensorConnectedTime", ex.stackTraceToString())
        }
    }

    protected fun isSensorSavedOnTheSharedPreference(sensor: Sensor): Boolean {
        val sensorName = SibelSensorManager.getEnrolledSensorNameByType(mnActivity, sensor.sensorType)
        return if(sensorName.isNullOrEmpty()) {
            sensor.disconnect(true)
            false
        } else {
            true
        }
    }

    private fun getAlarmParamListBySensorType(sensorType: SensorType): List<AlarmParametersDao>? {
        val alarmParamsArray: MutableList<AlarmParametersDao>? = when(sensorType) {
            SensorType.CHEST -> mutableListOf(CommonDataArea.hrAlarmDao, CommonDataArea.respAlarmDao)
            SensorType.LIMB -> mutableListOf(CommonDataArea.spo2AlarmDao)
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> mutableListOf(CommonDataArea.bpSysAlarmDao, CommonDataArea.bpDiaAlarmDao)
            else -> null
        }

        if(CommonDataArea.tempDisplaySensorType == sensorType)
            alarmParamsArray?.add(CommonDataArea.tempAlarmDao)

        return alarmParamsArray
    }

    private fun getNoConnectionTextBySensorType(sensorType: SensorType): String {
        val string = when(sensorType) {
            SensorType.CHEST -> R.string.no_chest_sensor_connected
            SensorType.LIMB -> R.string.no_limb_sensor_connected
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> R.string.no_bp_sensor_connected
            else -> null
        }
        return mnActivity.getString(string ?: 0)
    }

  /*  protected fun sensorConnectionUiUpdate(sensorType: SensorType, connectionStatus: ConnectionStatus) {
        val alarmParamsList = getAlarmParamListBySensorType(sensorType) ?: return
        for (alarmParam in alarmParamsList) {
            when(connectionStatus) {
                ConnectionStatus.CONNECTED -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.NoAlarm)
                    alarmParam.alarmStatus = true
                    onAttachDetachStatusUpdate(false, sensorType, String())
                    continue
                }
                ConnectionStatus.CONNECTION_LOST -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.ConnectivityAlarm)
                    alarmParam.alarmStatus = true
                    onAttachDetachStatusUpdate(true, sensorType, mnActivity.getString(R.string.connectionLost))
                    continue
                }
                ConnectionStatus.DISCONNECTED -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.NoAlarm)
                    alarmParam.alarmStatus = false
                    alarmParam.isAttached = true
                    alarmParam.alarmText = getNoConnectionTextBySensorType(sensorType)
                }
                ConnectionStatus.CONNECTING -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.NoAlarm)
                    alarmParam.alarmStatus = false
                    alarmParam.isAttached = true
                    alarmParam.alarmText = String()
                }
            }
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.StreamVisibilityChange, alarmParam)
        }
    }*/

    protected fun sensorConnectionUiUpdate(sensorType: SensorType, connectionStatus: ConnectionStatus) {
        val alarmParamsList = getAlarmParamListBySensorType(sensorType) ?: return
        for (alarmParam in alarmParamsList) {
            when(connectionStatus) {
                ConnectionStatus.CONNECTED -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.NoAlarm)
                    alarmParam.alarmStatus = true
                    onAttachDetachStatusUpdate(false, sensorType, String())
                    continue
                }
                ConnectionStatus.CONNECTION_LOST -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.ConnectivityAlarm)
                    alarmParam.alarmStatus = true
                    onAttachDetachStatusUpdate(true, sensorType, mnActivity.getString(R.string.connectionLost))
                    continue
                }
                ConnectionStatus.DISCONNECTED -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.NoAlarm)
                    alarmParam.alarmStatus = false
                    alarmParam.isAttached = true
                    alarmParam.alarmText = getNoConnectionTextBySensorType(sensorType)
                }
                ConnectionStatus.CONNECTING -> {
                    alarmParam.setAlarmParameter(CurrentAlarmParameter.NoAlarm)
                    alarmParam.alarmStatus = false
                    alarmParam.isAttached = true
                    alarmParam.alarmText = String()
                }
            }
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.StreamVisibilityChange, alarmParam)
        }
    }

    protected fun onAttachDetachStatusUpdate(isDetached: Boolean, sensorType: SensorType, connectivityText: String) {
        val alarmParamList = getAlarmParamListBySensorType(sensorType) ?: return

        for (alarmParam in alarmParamList) {
            val currentAlarmParam = if(isDetached) CurrentAlarmParameter.ConnectivityAlarm else alarmParam.currentAlarmParameter
            alarmParam.setAlarmParameter(currentAlarmParam)
            alarmParam.isAttached = !isDetached
            alarmParam.alarmText = getAlarmText(alarmParam, connectivityText)
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.StreamVisibilityChange, alarmParam)
        }
        sendConnectivityAlarm(isDetached, sensorType)
    }

    private fun getAlarmText(alarmParam: AlarmParametersDao, connectivityText: String): String {
        return when {
            !alarmParam.isAttached -> connectivityText
            !alarmParam.alarmStatus -> mnActivity.getString(R.string.monitorOff)
            else -> String()
        }
    }

    private fun sendConnectivityAlarm(isDetached: Boolean, sensorType: SensorType) {
        var isAttached: Boolean = !isDetached
        if(isAttached) {
            isAttached = when(sensorType) {
                SensorType.CHEST -> CommonDataArea.spo2AlarmDao.isAttached && CommonDataArea.bpSysAlarmDao.isAttached
                SensorType.LIMB -> CommonDataArea.hrAlarmDao.isAttached && CommonDataArea.bpSysAlarmDao.isAttached
                SensorType.BP2_BLOOD_PRESSURE_MONITOR -> CommonDataArea.hrAlarmDao.isAttached && CommonDataArea.spo2AlarmDao.isAttached
                else -> null
            } ?: return
        }
        CommonDataArea.connectivityAlarmDao.isAttached = isAttached
        CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.ConnectivityAlarm, null)
    }
}