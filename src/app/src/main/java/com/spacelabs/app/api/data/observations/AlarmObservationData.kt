package com.spacelabs.app.api.data.observations

import com.google.gson.GsonBuilder
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.api.data.dataclasses.AlarmObservationBean
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.objectbox.helpers.AlarmBoxDaoHelper
import com.spacelabs.app.iomt.IomtApiManager

class AlarmObservationData : ObservationManager() {

    data class Observation(
        val resourceType: String,
        val id: String,
        val status: String,
        val meta: Meta,
        val device: Device,
        val category: List<Category>,
        val code: Code,
        val subject: Subject,
        val effectivePeriod: EffectivePeriod,
        val performer: List<Performer>,
        val interpretation: List<Interpretation>,
        val component: List<Component>
    )

    data class Meta(
        val profile: List<String>,
        val tag: List<Tag>
    )

    data class Tag(
        val display: String,
        val code: String
    )

    data class Device(
        val reference: String,
        val display: String
    )

    data class Category(
        val coding: List<Coding>
    )

    data class Coding(
        val system: String,
        val code: String,
        val display: String
    )

    data class Code(
        val coding: List<Coding>,
        val text: String
    )

    data class Subject(
        val reference: String
    )

    data class EffectivePeriod(
        val start: String,
        val end: String?
    )

    data class Performer(
        val reference: String
    )

    data class Interpretation(
        val coding: List<Coding>,
        val text: String
    )

    data class Component(
        val code: Code,
        val valueQuantity: ValueQuantity,
        val interpretation: List<Interpretation>,
        val referenceRange: List<Any>
    )

    data class ValueQuantity(
        val value: String,
        val unit: String,
        val system: String,
        val code: String
    )

    data class ReferenceRange(
        val high: ReferenceValue,
        val low: ReferenceValue,
        val extremeHigh: ReferenceValue,
        val extremeLow: ReferenceValue
    )

    data class ReferenceRange2(
        val high: ReferenceValue,
        val low: ReferenceValue,
    )

    data class ReferenceValue(
        val value: Int,
        val unit: String,
        val code: String
    )

    private fun getReferenceRange(ranges: Map<String, Int>, unit: String, valueQuantityCode: String): List<Any>{
        return if(ranges.size > 2){
            listOf(
                ReferenceRange(
                    high = ReferenceValue(
                        ranges[AlarmObservationBean.high]!!,
                        unit,
                        valueQuantityCode
                    ),
                    low = ReferenceValue(
                        ranges[AlarmObservationBean.low]!!,
                        unit,
                        valueQuantityCode
                    ),
                    extremeHigh = ReferenceValue(
                        ranges[AlarmObservationBean.extremeHigh]!!,
                        unit,
                        valueQuantityCode
                    ),
                    extremeLow = ReferenceValue(
                        ranges[AlarmObservationBean.extremeLow]!!,
                        unit,
                        valueQuantityCode
                    )
                )
            )
        } else {
            listOf(
                ReferenceRange2(
                    high = ReferenceValue(
                        ranges[AlarmObservationBean.high]!!,
                        unit,
                        valueQuantityCode
                    ),
                    low = ReferenceValue(
                        ranges[AlarmObservationBean.low]!!,
                        unit,
                        valueQuantityCode
                    )
                )
            )
        }
    }

    private fun getMeta(observationData: AlarmObservationBean): Meta{
        return if(observationData.alarmType == AlarmBoxDaoHelper.States.ALARM_INTERIM.state)
            Meta(
                listOf(observationData.metaProfile),
                listOf(
                    Tag(observationData.metaTagDisplay1, observationData.isExtreme.toString()),
                    Tag(observationData.metaTagDisplay2, observationData.isAcknowledged.toString()),
                    Tag(observationData.metaTagDisplay3, observationData.alarmStartTime)
                )
            )
        else Meta(
            listOf(observationData.metaProfile),
            listOf(
                Tag(observationData.metaTagDisplay1, observationData.isExtreme.toString()),
                Tag(observationData.metaTagDisplay2, observationData.isAcknowledged.toString())
            )
        )
    }

    private fun getDevice(): Device{
        return Device(
            /*"Device/${SnsApiManager.IMEI}"*/
            "Device/${IomtApiManager.DEVICE_UUID}",
            "HUB device"
        )
    }

    private fun getCategory(observationData: AlarmObservationBean): Category{
        return Category(
            listOf(
                Coding(
                    observationData.codingSystem,
                    observationData.codingCode,
                    observationData.codingDisplay
                )
            )
        )
    }

    private fun getCode(observationData: AlarmObservationBean): Code{
        return Code(
            listOf(Coding(
                observationData.vitalSystem,
                observationData.vitalTriple.first,
                observationData.vitalTriple.third
            )),
            observationData.vitalTriple.third
        )
    }

    private fun getInterpretation(observationData: AlarmObservationBean): List<Interpretation>{
        return listOf(
            Interpretation(
                listOf(
                    Coding(
                        observationData.interpretationSystem,
                        observationData.interpretationCode,
                        observationData.interpretationDisplay
                    )
                ),
                observationData.interpretationText
            )
        )
    }

    private fun getComponent(observationData: AlarmObservationBean): List<Component>{
        val referenceRange = getReferenceRange(
            observationData.vitalRanges,
            observationData.vitalTriple.second,
            observationData.valueQuantityCode
        )
        return listOf(
            Component(
                getCode(observationData),
                ValueQuantity(
                    observationData.vitalValue,
                    observationData.vitalTriple.second,
                    observationData.valueQuantitySystem,
                    observationData.valueQuantityCode
                ),
                getInterpretation(observationData),
                referenceRange
            )
        )
    }

    fun getObservationJson(observationData: AlarmObservationBean): String {
        // Initialize the values for each field based on your data
        val observation = Observation(
            observationData.resourceType,
            observationData.observationId,
            observationData.status,
            getMeta(observationData),
            getDevice(),
            listOf(getCategory(observationData)),
            getCode(observationData),
            //Subject("${observationData.subjectReference}/${SnsApiManager.apiResponseData.patient.patientID1}"),
            Subject("${observationData.subjectReference}/${CommonDataArea.PATIENT.patientId1}"),
            EffectivePeriod(
                observationData.alarmStartTime,
                observationData.alarmEndTime
            ),
            listOf(Performer("${observationData.performerReference}/${observationData.performerId}")),
            getInterpretation(observationData),
            getComponent(observationData)
        )

        val gson = GsonBuilder().create()
        return gson.toJson(observation)
    }
}