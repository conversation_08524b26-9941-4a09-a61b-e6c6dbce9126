package com.spacelabs.app.database.objectbox.helpers

import android.util.Log
import com.spacelabs.app.activities.fragments.fragmenthelpers.TrendGraphFragmentHelper
import com.spacelabs.app.api.data.ApiDataTransactionManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.dataManager.ObjectBoxManager
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.boxes.MeasurementData_
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.interfaces.UiEventCallback
import io.objectbox.query.QueryBuilder

class MeasurementDataDaoHelper {

    fun getAllMeasurementByPatientId(patientId: Long): List<MeasurementData> {
        val dataWithIn = 10 * 60000
        val box = ObjectBoxManager.getMeasurementBox()
        val query = box.query().apply {
            equal(MeasurementData_.patientId, patientId)
            //isNull(MeasurementData_.measurementData)
            greater(MeasurementData_.tickValue, TimestampUtils.getCurrentTimeMillis() - dataWithIn)
            order(MeasurementData_.measurementId)
        }
        val result = query.build().find()
        return result.reversed()
    }
    fun getMeasurementByUploadStatus2(patientId: Long): List<MeasurementData> {
        val dataWithIn = 10 * 60000
        val box = ObjectBoxManager.getMeasurementBox()
        val query = box.query().apply {
            equal(MeasurementData_.patientId, patientId)
            //isNull(MeasurementData_.measurementData)
            greater(MeasurementData_.tickValue, TimestampUtils.getCurrentTimeMillis() - dataWithIn)
            equal(MeasurementData_.uploadStatus,0)
            order(MeasurementData_.measurementId)
        }
        val result = query.build().find()
        return result.reversed()
    }

    private fun getPendingMeasurementDataOfThePatient(patientId: Long): List<MeasurementData>? {
        //SELECT * FROM tblMeasurementData WHERE patientId = patientId AND uploadStatus = 0 ORDER BY md.tickValue ASC
        val dataWithIn = 10 * 60000
        val currentTime = TimestampUtils.getCurrentTimeMillis()
        val box = ObjectBoxManager.getMeasurementBox()
        val query = box.query()?.apply {
            equal(MeasurementData_.patientId, patientId)
            equal(MeasurementData_.uploadStatus, 0)
            greater(MeasurementData_.tickValue, currentTime - dataWithIn)
            order(MeasurementData_.tickValue)
        }
        return query?.build()?.find()
    }

    fun updateMeasurementUploadStatusByUuId(uuId: String) {
        val box = ObjectBoxManager.getMeasurementBox()
        val measurementData = box.query().apply {
            equal(MeasurementData_.measurementUuid, uuId, QueryBuilder.StringOrder.CASE_INSENSITIVE)
        }.build().findFirst()

        if (measurementData != null) {
            measurementData.uploadStatus = 2
            measurementData.uploadTimestamp = TimestampUtils.getCurrentTime()
            box.put(measurementData)
        }
    }

    fun updateMeasurementUploadStatusByUuid(data: Any, status: Int) {
        try {
            val measurement = data as MeasurementData
            val uuId = measurement.measurementUuid
            measurement.uploadStatus = status
            measurement.uploadTimestamp = TimestampUtils.getCurrentTime()
            val box = ObjectBoxManager.getMeasurementBox()
            val updateValue = box.put(measurement)
            if (updateValue > 0/* && status == 2*/) {
                if (enableSnsApi) {
                    ApiDataTransactionManager.uuIdAndData.remove(uuId)
                }
            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog(
                "UpdateMeasurementUploadStatusByUuid",
                ex.stackTraceToString()
            )
            Log.d("UpdateMeasurementUploadStatusByUuid", ex.stackTraceToString())
        }
    }

    fun deleteMeasurementsOnDischarge() {
        val measurementBox = ObjectBoxManager.getMeasurementBox()

        // Find entities with uploadStatus 1
        val queryToDelete = measurementBox.query().greaterOrEqual(MeasurementData_.uploadStatus, 1).build()
        val entitiesToDelete = queryToDelete.find()

        // Remove entities with uploadStatus 1
        measurementBox.remove(entitiesToDelete)
        queryToDelete.close()

    }

    fun deleteMeasurementsData(tickValue: Long) {
        val measurementBox = ObjectBoxManager.getMeasurementBox()

        // Find entities with uploadStatus 1 and tick value should be less than equal to the given value
        val queryToDelete =
            measurementBox.query().lessOrEqual(MeasurementData_.tickValue, tickValue).and()
                .greaterOrEqual(MeasurementData_.uploadStatus, 1).build()
        val entitiesToDelete = queryToDelete.find()

        // Remove entities with uploadStatus 1 and tick value should be less than equal to the given value
        measurementBox.remove(entitiesToDelete)
        queryToDelete.close()
    }

    fun getMeasurementListToSend(): List<MeasurementData>? {
        return getPendingMeasurementDataOfThePatient(CommonDataArea.PATIENT.patientId)
    }

    fun getTrendDataForInterValAndNotifyCharts(hoursInterval: Int, endTime: Long) {
        val timeDifference = hoursInterval * 60 * 60 * 1000
        val  box = ObjectBoxManager.getMeasurementBox()
        var startTime = endTime - timeDifference
        var data = mutableListOf<MeasurementData>()
        do {
            if(!TrendGraphFragmentHelper.trendLoader)
                break

            val toTime = startTime + hoursInterval * 600000
            data = box.query().apply {
                equal(MeasurementData_.patientId, CommonDataArea.PATIENT.patientId)
                between(MeasurementData_.tickValue, startTime, toTime)
                isNull(MeasurementData_.measurementData)
                order(MeasurementData_.tickValue)
            }.build().find()
            startTime = toTime

            Log.d("TrendIterator", "${true}")

            if(data.isNotEmpty())
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.TrendDataReceived, data)
        } while (startTime <= endTime)
        data.clear()
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.TrendLoading, false)
    }

    //timeMillis = TimestampUtils.getCurrentTimeMillis - 30000
    fun getLastNNumberDataByTimeMillis(timeMillis: Long, count: Int): List<MeasurementData>? {
        val box = ObjectBoxManager.getMeasurementBox()
        val query = box.query()?.apply {
            equal(MeasurementData_.patientId, CommonDataArea.PATIENT.patientId)
            equal(MeasurementData_.uploadStatus, 0)
            less(MeasurementData_.tickValue, timeMillis)
            order(MeasurementData_.tickValue)
        }
        return query?.build()?.find()?.take(count)
    }
}