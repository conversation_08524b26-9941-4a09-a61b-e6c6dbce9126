package com.spacelabs.app.iomt.data

import android.util.Log
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.api.data.dataclasses.BpObservationBean
import com.spacelabs.app.api.data.dataclasses.Component
import com.spacelabs.app.api.data.dataclasses.VitalObservationBean
import com.spacelabs.app.api.data.dataclasses.WaveObservationBean
import com.spacelabs.app.api.data.observations.NibpObservationData
import com.spacelabs.app.api.data.observations.VitalObservationData
import com.spacelabs.app.api.data.observations.WaveformObservationData
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter
import com.spacelabs.app.iomt.CmsClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import org.java_websocket.client.WebSocketClient
import org.java_websocket.exceptions.WebsocketNotConnectedException
import org.json.JSONException
import org.json.JSONObject
import java.nio.ByteBuffer
import java.util.Base64

class CmsDataSender(private val webSocketClient: WebSocketClient?) {
    private val cmsLiveDataManager = CmsLiveDataTransactionManager()
    private val websocketCoroutine = CoroutineScope(Dispatchers.Default)
    private val waveformObservation = WaveformObservationData()
    private val encryptionVitalConverter = EncryptionVitalConverter()
    private val vitalObservation = VitalObservationData()
    private val nibpObservation = NibpObservationData()

    companion object {
        private const val TAG = "Cms_Data_Sender"
    }

    fun sendPacket(
        array: FloatArray,
        cmsId: String?,
        measurement: MeasurementData
    ) {
        try {
            val measurementId = measurement.measurementId
            val timestamp = measurement.timestamp
            if (CmsClient.mWebSocketClient == null || !CmsClient.mWebSocketClient!!.isOpen || cmsId == null) return
            val buf = ByteBuffer.allocate(java.lang.Float.SIZE / java.lang.Byte.SIZE * array.size)
            buf.asFloatBuffer().put(array)
            val base64encodedString = Base64.getEncoder().encodeToString(buf.array())
            val message = JSONObject()
            message.accumulate("PacketID", "WS_PARAM_DATA")
            message.accumulate("ParamUUID", cmsId)
            message.accumulate("length", base64encodedString.length)
            message.accumulate("Data", base64encodedString)
            message.accumulate("ParamType", "Stream")
            message.accumulate("SNo", measurementId)
            message.accumulate("TimeStamp", timestamp)

            buf.clear()
            sendData(message, measurement)
        } catch (e: WebsocketNotConnectedException) {
            Log.e(TAG, "WebSocket not connected. Unable to send packet.", e)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }


    fun sendWaveformObservation(paramData: WaveObservationBean, samples: DoubleArray, timeStamp: String,measurement: MeasurementData) {
        try{
            paramData.timestamp = timeStamp
            val observation = waveformObservation.createWaveformObservation(paramData, samples).toString()
            if (CmsClient.mWebSocketClient != null && CmsClient.mWebSocketClient!!.isOpen) {
                sendFhirData(observation, measurement)
            }
        } catch(ex: Exception){

            Log.d("WaveApiException", ex.stackTraceToString())
        }
    }

    fun sendVitalObservation(measurement: MeasurementData, parameter: Triple<String, String, String>, uuId: String){
        try{
            val component = Component(
                system = "http://loinc.org",  // Assuming LOINC system for the components
                code = parameter.first,
                display = parameter.third,
                value = encryptionVitalConverter.convertToEntityProperty(measurement.value!!).toString(),
                unit = parameter.second
            )

            val paramData = VitalObservationBean(
                components = listOf(component),
                timestamp = measurement.timestamp,
                uuId = uuId
            )
            val observation = vitalObservation.getObservation(paramData).toString()
            if(CmsClient.mWebSocketClient != null && CmsClient.mWebSocketClient!!.isOpen){
                sendFhirData(observation, measurement)
            }
        } catch (ex: Exception){
            Log.d("VitalApiException", ex.stackTraceToString())
        }
    }

    fun sendNIBPObservation(niBp: String, timeStamp: String, uuId: String,measurement: MeasurementData){
        val splitNiBp = niBp.split("/")
        try{
            val bpObservationData = BpObservationBean(
                timestamp = timeStamp,
                bpSys = splitNiBp.first().toInt(),
                bpDia = splitNiBp.last().toInt(),
                uuId = uuId
            )
            val observation = nibpObservation.getObservationJson(bpObservationData)
            if(CmsClient.mWebSocketClient != null && CmsClient.mWebSocketClient!!.isOpen){
                sendFhirData(observation, measurement)
            }
        } catch(ex: Exception){
            Log.d("NiBpApiException", ex.stackTraceToString())
        }
    }

    private fun sendData(jsonObject: JSONObject, measurement: MeasurementData) {
        websocketCoroutine.launch {
            withTimeout(2000) {
                val message = jsonObject.toString()
                if (CmsClient.mWebSocketClient != null && CmsClient.mWebSocketClient!!.isOpen) {
                    CmsClient.mWebSocketClient!!.send(message)
                    //Log.d(TAG, "CmsClient.mWebSocketClient: ${message}")
                    cmsLiveDataManager.checkResponseAndUpdateDb(MeasurementData::class.java, measurement)
                } else {
                    Log.d(TAG, "WebSocket connection is not open.")
                }
            }
        }
    }
    private fun sendFhirData(message: String, measurement: MeasurementData) {
        websocketCoroutine.launch {
            withTimeout(2000) {
                if (CmsClient.mWebSocketClient != null && CmsClient.mWebSocketClient!!.isOpen) {
                    CmsClient.mWebSocketClient!!.send(message)
                    Log.d(TAG, "Data: ${message}")
                    cmsLiveDataManager.checkResponseAndUpdateDb(MeasurementData::class.java, measurement)
                } else {
                    Log.d(TAG, "WebSocket connection is not open.")
                }
            }
        }
    }

    fun sendValue(
        value: Float,
        cmsId: String?,
        measurement: MeasurementData
    ) {
        try {
            if (CmsClient.mWebSocketClient == null || !CmsClient.mWebSocketClient!!.isOpen || cmsId == null) return

            val measurementId = measurement.measurementId
            val timestamp = measurement.timestamp
            val message = JSONObject()
            message.accumulate("PacketID", "WS_PARAM_DATA")
            message.accumulate("ParamUUID", cmsId)
            message.accumulate("length", 1)
            message.accumulate("Value", value)
            message.accumulate("ParamType", "Value")
            message.accumulate("SNo", measurementId)
            message.accumulate("TimeStamp", timestamp)

            sendData(message, measurement)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun sendBpValue(
        value: String,
        cmsId: String?,
        measurement: MeasurementData
    ) {
        try {
            if (CmsClient.mWebSocketClient == null || !CmsClient.mWebSocketClient!!.isOpen || cmsId == null) return

            val measurementId = measurement.measurementId
            val timestamp = measurement.timestamp
            val message = JSONObject()
            message.accumulate("PacketID", "WS_PARAM_DATA")
            message.accumulate("ParamUUID", cmsId)
            message.accumulate("length", 1)
            message.accumulate("Value", value)
            message.accumulate("ParamType", "Value")
            message.accumulate("SNo", measurementId)
            message.accumulate("TimeStamp", timestamp)

            sendData(message, measurement)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }


}