package com.spacelabs.app.activities.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.childfragments.ActivitySettingsFragment
import com.spacelabs.app.activities.fragments.childfragments.BpSettingsFragment
import com.spacelabs.app.activities.fragments.childfragments.CommonSettingsFragment
import com.spacelabs.app.activities.fragments.childfragments.EcgSettingsFragment
import com.spacelabs.app.activities.fragments.childfragments.RespSettingsFragment
import com.spacelabs.app.activities.fragments.childfragments.Spo2SettingsFragment
import com.spacelabs.app.activities.fragments.childfragments.TempSettingsFragment
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.interfaces.UiEventCallback

class ParamSettingsFragment(val navBtn: NavButtons, val fragmentHolder: FrameLayout): Fragment(R.layout.all_settings_dialogs) {

    private lateinit var closeBtn: Button
    private lateinit var childHolder: FrameLayout

    private lateinit var paramSettingsHelper: ParamSettingsHelper

    private val buttonsAndFragments = mutableMapOf<AppCompatButton, Fragment>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        paramSettingsHelper = ParamSettingsHelper(requireContext())
        buttonsAndFragments.putAll(
            mapOf(
                view.findViewById<AppCompatButton>(R.id.ecgBtn) to EcgSettingsFragment(paramSettingsHelper),
                view.findViewById<AppCompatButton>(R.id.respBtn) to RespSettingsFragment(paramSettingsHelper),
                view.findViewById<AppCompatButton>(R.id.spo2Btn) to Spo2SettingsFragment(paramSettingsHelper),
                view.findViewById<AppCompatButton>(R.id.tempBtn) to TempSettingsFragment(paramSettingsHelper),
                view.findViewById<AppCompatButton>(R.id.bpBtn) to BpSettingsFragment(paramSettingsHelper),
                view.findViewById<AppCompatButton>(R.id.activityBtn) to ActivitySettingsFragment(paramSettingsHelper),
                view.findViewById<AppCompatButton>(R.id.trendGraphBtn) to TrendGraphFragment(fragmentHolder),
                view.findViewById<AppCompatButton>(R.id.settingsBtn) to CommonSettingsFragment(paramSettingsHelper),
            )
        )

        closeBtn = view.findViewById(R.id.closeBtn)
        childHolder = view.findViewById(R.id.settings_fragment_holder)

        closeBtn.setOnClickListener {
            closeFragment()
        }

        initialSetupAndmenuBarActionListeners(navBtn)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        view?.setOnTouchListener(fun(_: View, _: MotionEvent): Boolean {
            return true
        })

        return view
    }

    override fun onStart() {
        super.onStart()
        fragmentHolder.visibility = View.VISIBLE
    }

    private fun initialSetupAndmenuBarActionListeners(navBtn: NavButtons) {
        val btnFragment = buttonsAndFragments.filter {
            it.key.id == navBtn.btnId
        }.entries.first()
        onNavButtonClickAction(btnFragment.key, btnFragment.value)

        buttonsAndFragments.forEach {
            val btn = it.key
            val fragment = it.value
            btn.setOnClickListener {
                onNavButtonClickAction(btn, fragment)
            }
        }
    }

    private fun onNavButtonClickAction(button: AppCompatButton, fragment: Fragment) {

        when(button.id) {
            R.id.trendGraphBtn -> {
                parentFragmentManager.beginTransaction()
                    .replace(fragmentHolder.id,TrendGraphFragment(fragmentHolder))
                    .commit()
            }
            else -> {
                childFragmentManager.beginTransaction()
                    .replace(childHolder.id, fragment)
                    .commit()
                updateButtonStyles(button)
            }
        }
    }
    enum class NavButtons(val btnId: Int) {
        ECG(R.id.ecgBtn),
        RESP(R.id.respBtn),
        SPO2(R.id.spo2Btn),
        TEMP(R.id.tempBtn),
        BP(R.id.bpBtn),
        ACTIVITY(R.id.activityBtn),
        SETTINGS(R.id.settingsBtn)
    }
    fun closeFragment(){
        parentFragmentManager.beginTransaction().remove(this).commit()
        fragmentHolder.visibility = View.GONE
    }
    private fun updateButtonStyles(selectedButton: AppCompatButton){
        selectedButton.backgroundTintList =
            context?.resources?.getColorStateList(R.color.lightBlue, context?.theme)
        context?.resources?.let { selectedButton.setTextColor(it.getColor(R.color.white, context?.theme)) }

        buttonsAndFragments.keys.forEach {
            if(it == selectedButton) return@forEach
            it.backgroundTintList =
                context?.resources?.getColorStateList(R.color.components_gray, context?.theme)
            context?.resources?.let { it1 ->
                selectedButton.setTextColor(
                    it1.getColor(com.androidplot.R.color.ap_gray,
                        context?.theme
                    ))
            }
        }
    }
}