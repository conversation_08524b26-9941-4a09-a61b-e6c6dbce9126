package com.spacelabs.app.interfaces

fun interface UiEventCallback {
    enum class UiEventType{
        StartScanningAnimation,
        StopScanningAnimation,
        NavigateToNext,
        ServerConnectionStatusChanged,
        PatientChange,
        VitalReceived,
        VitalCompanionText,
        AlarmThresholdChange,
        AlarmStateChange,
        SensorDiscovered,
        SensorConnecting,
        StreamVisibilityChange,  //Disconnect / Detach / MonitorOFF
        StreamChartUpdate,
        SensorDisconnected,
        SensorBatteryChanged,
        SettingsChanged,
        SetResetTurnDue,
        FallNotified,
        PopupDialog,
        TrendDataReceived,
        TrendLoading,
        CloseSettings
    }

    fun uiEvent(event: UiEventType, eventData: Any?)
}