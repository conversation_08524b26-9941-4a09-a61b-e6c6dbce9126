package com.spacelabs.app.database.daoHelper

import android.content.Context
import com.spacelabs.app.api.data.ApiDataTransactionManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.SibelDatabase
import com.spacelabs.app.database.dao.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.*

open class DbDaoHelperManager(context: Context) {

    private val dbInstance: SibelDatabase

    protected val alarmDao: AlarmDao
    protected val eventsDao: EventsDao
    protected val measurementDao: MeasurementDataDao
    protected val parametersDao: ParametersDao
    protected val patientDao: PatientDao
    protected val sensorDao: SensorDao
    protected val settingsDao: SettingDao
    protected val visitDao: VisitDao

    private val dbJob: Job
    protected val dbCoroutine: CoroutineScope

    init {
        dbJob = Job()
        dbCoroutine = CoroutineScope(Dispatchers.IO + dbJob)
    }

    init {
        dbInstance = CommonDataArea.keystoreManager.getKeyFromKeystore()
            ?.let { SibelDatabase.getInstance(context, it) }!!

        alarmDao = dbInstance.alarmDao
        eventsDao = dbInstance.eventsDao
        measurementDao = dbInstance.measurementDao
        parametersDao = dbInstance.parametersDao
        patientDao = dbInstance.patientDao
        sensorDao = dbInstance.sensorDao
        settingsDao = dbInstance.settingsDao
        visitDao = dbInstance.visitDao
    }

    protected fun getCurrentTime(): String{
        val currentTime = OffsetDateTime.now()
        val usTimeZone = ZoneOffset.UTC

        val usTime = currentTime.atZoneSameInstant(usTimeZone)
        return usTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }
}