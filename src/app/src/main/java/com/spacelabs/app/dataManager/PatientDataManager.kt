package com.spacelabs.app.dataManager

import android.util.Log
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.entities.SensorTable
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.lang.Exception
import java.util.*

class PatientDataManager {

    @OptIn(DelicateCoroutinesApi::class)
    fun updateSensorsAgainstPatient(sensor: Sensor){
        GlobalScope.launch {
            try{
                val patientId = CommonDataArea.measurementDao.getActivePatientId()
                if (patientId != null) {
                    CommonDataArea.measurementDao.setPreviousPatchStatusesOfPatientToDisconnect(
                        patientId,
                        sensor.sensorType.name
                    )
                    val sensorName = sensor.name
                    val sensorType = sensor.sensorType.name
                    val calendar = Calendar.getInstance()
                    val time = calendar.time.toString()
                    val newSensor = SensorTable(
                        null,
                        null,
                        patientId,
                        sensorType,
                        sensorName,
                        null,
                        time,
                        "connected"
                    )
                    val sensorId =
                        CommonDataArea.measurementDao.insertOrUpdateSensor(newSensor)

                    when(sensor.sensorType){
                        SensorType.CHEST -> CommonDataArea.currentChestSensorId = sensorId
                        SensorType.LIMB -> CommonDataArea.currentLimbSensorId = sensorId
                        else -> CommonDataArea.currentBpSensorId = sensorId
                    }
                }
            }catch (ex: Exception){
                Log.d("updateSensorsAgainstPatient", ex.stackTraceToString())
            }
        }
    }
}