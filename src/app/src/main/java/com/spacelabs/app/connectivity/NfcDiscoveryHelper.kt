package com.spacelabs.app.connectivity

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.nfc.NdefRecord
import android.nfc.NfcAdapter
import android.nfc.Tag
import android.nfc.tech.Ndef
import android.nfc.tech.NdefFormatable
import android.os.Build
import android.os.Build.VERSION
import android.util.Log
import android.widget.Toast
import androidx.annotation.RequiresApi
import com.spacelabs.app.MainActivity
import java.io.UnsupportedEncodingException
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.sensorService
import com.spacelabs.app.ui.Dialogs
import kotlinx.coroutines.launch
import java.nio.charset.Charset
import java.util.*

class NfcDiscoveryHelper(mainActivity: MainActivity) {

    private val mainActivity: MainActivity
    private val context: Context

    private var nfcAdapter: NfcAdapter? = null
    private var pendingIntent: PendingIntent? = null
    private var intentFilters: Array<IntentFilter>? = null
    private var techList: Array<Array<String>>? = null

    init {
        this.mainActivity = mainActivity
        context = this.mainActivity.applicationContext
    }

    fun nfcInit(): NfcAdapter?{
        val packageManager = context.packageManager
        if(!packageManager.hasSystemFeature(PackageManager.FEATURE_NFC)){
            Toast.makeText(context, "NFC not supported", Toast.LENGTH_SHORT).show()
        }

        nfcAdapter = NfcAdapter.getDefaultAdapter(context)
        if(nfcAdapter == null){
            Toast.makeText(context, "NFC is not supported", Toast.LENGTH_SHORT).show()
        }
        return nfcAdapter
    }

    @RequiresApi(Build.VERSION_CODES.S)
    fun onResumeAction(){
        pendingIntent = PendingIntent.getActivity(context, 0,
            Intent(context, javaClass).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP), PendingIntent.FLAG_MUTABLE)
        intentFilters = arrayOf()
        techList = arrayOf(
            arrayOf(
                Ndef::class.java.name
            ), arrayOf(NdefFormatable::class.java.name)
        )
        nfcAdapter!!.enableForegroundDispatch(mainActivity, pendingIntent, intentFilters, techList)
    }


    fun onNewIntentAction(intent: Intent?){
        try {

            if(intent!!.hasExtra(NfcAdapter.EXTRA_TAG)){
                val tag: Tag? = if(VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU)
                    intent.getParcelableExtra(NfcAdapter.EXTRA_TAG)
                else
                    intent.getParcelableExtra(NfcAdapter.EXTRA_TAG, Tag::class.java)

                Toast.makeText(context, "Tag Discovered", Toast.LENGTH_SHORT).show()
                val techList = tag!!.techList
                for(tech in techList!!){
                    Log.d("Tech List", tech)
                }

                Toast.makeText(context, "Tag Discovered", Toast.LENGTH_SHORT).show()

                val ndef = Ndef.get(tag)
                ndef.connect()
                val tagName = getTagName(ndef)
                if (tagName != null && ndef.isConnected) {
                    Log.d("TagName", tagName)
                    mainActivity.uiScope.launch {
                        if(Dialogs.ManageSensorDialog != null && Dialogs.ManageSensorDialog!!.isShowing) {
                            Dialogs.ManageSensorDialog!!.dismiss()
                        }
                    }
                    SibelSensorManager.setSensorScanMode(SibelSensorManager.SensorScanMode.NfcDiscovered)
                    SibelSensorManager.setSensorToScanFor(tagName)
                    sensorService.startScan(10000)
                }
                ndef.close()
            }
        }catch (ex: Exception){
            Log.d("newIntent",ex.stackTraceToString())
        }
    }

    private fun getTagName(ndef: Ndef?): String?{
        val tagName: String? = null
        if(ndef != null){
            val ndefMessage = ndef.cachedNdefMessage
            val records = ndefMessage.records
            for(ndefRcd in records){
                Log.d("Record", ndefRcd.toString())
                if(ndefRcd.tnf == NdefRecord.TNF_WELL_KNOWN && Arrays.equals(ndefRcd.type, NdefRecord.RTD_TEXT)){
                    try {
                        val payload = ndefRcd.payload
                        val textEncoding =
                            if ((payload[0].toInt() and 128) == 0) "UTF-8" else "UTF-16"
                        val languageCodeLength = 2
                        return String(
                            payload,
                            languageCodeLength + 1,
                            payload.size - languageCodeLength - 1,
                            Charset.forName(textEncoding)
                        )
                    } catch (ex: UnsupportedEncodingException) {
                        Log.d("Exception", ex.stackTraceToString())
                    }
                }
            }
        }
        return tagName
    }
}