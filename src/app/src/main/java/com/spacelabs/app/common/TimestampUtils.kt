package com.spacelabs.app.common

import android.util.Log
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.sensor.helper.SensorObserverHelper
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

object TimestampUtils {

    private const val EPOCH_TIME = "1970-01-01T00:00:00.0Z"

    private val timeZone = ZoneOffset.UTC
    private val TimestampPattern = DateTimeFormatter.ISO_OFFSET_DATE_TIME

    fun convertToTimestamp(timeInterval: String): String {
        val value = timeInterval.substring(0, timeInterval.length - 1).toLong()
        val convertedInterval = when (val unit = timeInterval.last().toString()) {
            "m" -> value * 60 * 1000
            "h" -> value * 60 * 60 * 1000
            "d" -> value * 24 * 60 * 60 * 1000
            else -> throw IllegalArgumentException("Invalid time interval unit: $unit")
        }

        val currentTimestamp = ZonedDateTime.now(timeZone)
        val newTimestamp = currentTimestamp.plusNanos(convertedInterval * 1_000_000)
        return newTimestamp.format(TimestampPattern)
    }

    fun convertMillisToDayMonthHourMinutes(timeMillis: Long): String {
        val instant = Instant.ofEpochMilli(timeMillis)
        val dateTime = instant.atZone(timeZone)
        val formatter = DateTimeFormatter.ofPattern("MMM d, h:mm a")
        return formatter.format(dateTime)
    }

    fun timestampToMillis(timestamp: String): Long{
        val zonedDateTime = ZonedDateTime.parse(timestamp, TimestampPattern.withZone(timeZone))
        return zonedDateTime.toInstant().toEpochMilli()
    }

    fun getCurrentTimeMillis(): Long {
        val currentTimeInNewYork = ZonedDateTime.now(timeZone)
        return currentTimeInNewYork.toInstant().toEpochMilli()
    }

    fun getCurrentTime(): String {
        val currentTime = OffsetDateTime.now()
        val usTime = currentTime.atZoneSameInstant(timeZone)
        return usTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }

    fun getCurrentTimeAndMillis(): Pair<String, Long> {
        val currentTime = OffsetDateTime.now()
        val usTime = currentTime.atZoneSameInstant(timeZone)
        return Pair(
            usTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME),
            usTime.toInstant().toEpochMilli()
        )
    }

    fun getFormattedTimeWithEpochTime(timestamp: Double, sensorType: SensorType): Pair<String, Long> {
        var receivedTime = ""
        var timeMillis: Long = 0
        val instant = Instant.ofEpochMilli(timestamp.toLong())
        val offsetDateTime = OffsetDateTime.ofInstant(instant, timeZone)
        val formattedUtcTime = offsetDateTime.format(DateTimeFormatter.ISO_INSTANT)

        val formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME
        try {
            val startDateTime = LocalDateTime.parse(EPOCH_TIME, formatter)
            val endDateTime = LocalDateTime.parse(formattedUtcTime, formatter)
            val duration = Duration.between(startDateTime, endDateTime)
            val currentTime = when (sensorType) {
                SensorType.CHEST -> SensorObserverHelper.chestSensorConnectedTime
                SensorType.LIMB -> SensorObserverHelper.limbSensorConnectedTime
                else -> null
            }

            if (currentTime != null) {
                val timeToAdd =
                    ZonedDateTime.parse(currentTime, formatter) // Use ZonedDateTime here
                val newTime = timeToAdd.plus(duration)
                receivedTime = newTime.format(formatter)
                timeMillis = newTime.toInstant().toEpochMilli()
            }
        } catch (ex: Exception) {
            Log.e("DataStreamError", ex.stackTraceToString())
        }
        return Pair(receivedTime, timeMillis)
    }

    fun getRoundedOffTimeForIntervals(interval: String): String {
        val addOnTime = interval.filter { it.isDigit() }.toLong()
        val currentTime = ZonedDateTime.now(timeZone)

        val newTime = if (interval.last() == 'm') {
            currentTime.withHour(currentTime.hour).plusMinutes(addOnTime).withSecond(0).withNano(0)
        } else {
            currentTime.plusHours(addOnTime).withMinute(0).withSecond(0).withNano(0)
        }
        return newTime.format(TimestampPattern)
    }
}
