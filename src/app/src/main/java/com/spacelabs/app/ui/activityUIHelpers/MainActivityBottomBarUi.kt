package com.spacelabs.app.ui.activityUIHelpers

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.os.SystemClock
import android.text.Editable
import android.text.Html
import android.text.Spannable
import android.util.Log
import android.view.View
import android.widget.Chronometer
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.core.content.res.ResourcesCompat
import androidx.core.widget.addTextChangedListener
import com.google.android.material.card.MaterialCardView
import com.sibelhealth.bluetooth.sensor.sibel.model.BodyPosition
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.R
import com.spacelabs.app.MainActivity
import com.spacelabs.app.alarms.alarmDAO.ParameterViewsDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.IbsmFragments
import com.spacelabs.app.ui.MainActivityUIs
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class MainActivityBottomBarUi(mainActivity: MainActivity): ActivityUiManager(mainActivity) {

    private lateinit var scanButton: MaterialCardView
    private lateinit var pauseAlarm: MaterialCardView
    private lateinit var tempCard: MaterialCardView
    private lateinit var bpCard: MaterialCardView
    private lateinit var bpDiaCard: MaterialCardView
    private lateinit var bpErrorCard: MaterialCardView
    private lateinit var bedActivityCard: MaterialCardView
    private lateinit var bedPositionCard: MaterialCardView
    private lateinit var activeActivityCard: MaterialCardView
    private lateinit var activePositionCard: MaterialCardView
    private lateinit var appLogoButton: MaterialCardView

    lateinit var temp: TextView
    lateinit var bpSys: TextView
    lateinit var bpDia: TextView
    lateinit var bpTime: TextView
    private lateinit var bpError: TextView
    lateinit var bpAutoMeasurementTime: TextView
    lateinit var bpAutoMeasurementTimeTv: TextView
    lateinit var bpMeasuringStatusTv: TextView
    lateinit var turnTimeSetting: TextView
    lateinit var stepCount: TextView
    private lateinit var textView4: TextView
    private lateinit var turnTimeLabel: TextView
    private lateinit var textSecondBottom: TextView
    private lateinit var bodyPosition: TextView
    lateinit var rollFallCount: TextView
    lateinit var activeFallCount: TextView
    lateinit var bodyAngle: TextView
    lateinit var activeAngle: TextView
    private lateinit var turnTime: TextView
    private lateinit var positionLeft: TextView
    private lateinit var positionBack: TextView
    private lateinit var positionRight: TextView
    lateinit var tempSensor: TextView

    private lateinit var bpVitalLayer: LinearLayout
    private lateinit var positionVitalLayer: LinearLayout

    private lateinit var positionTime: Chronometer

    private lateinit var mainActivityUIs: MainActivityUIs
    private lateinit var noValueText: String

    private lateinit var settingDaoHelper :SettingDaoHelper

    private val sharedPreferences: SharedPreferences
    private val mainActivity: MainActivity

    private val activeMode: String
    private val bedBoundMode: String
    init {
        this.mainActivity = mainActivity
        sharedPreferences = mainActivity.getSharedPreferences("settings", Context.MODE_PRIVATE)
        activeMode = mainActivity.resources.getString(R.string.active)
        bedBoundMode = mainActivity.resources.getString(R.string.bed_bound)
    }

    companion object{
        var positionCardColorTheme = R.color.white
    }

    override fun beforeUiInit() {
        mainActivityUIs = mainActivity.mainActivityUIs
        noValueText = mainActivityUIs.noValueText
        settingDaoHelper  = SettingDaoHelper(context)
    }

    override fun initUi() {
        scanButton = mainActivity.findViewById(R.id.scanCard)
        pauseAlarm = mainActivity.findViewById(R.id.pauseAlarm)
        tempCard = mainActivity.findViewById(R.id.tempCard)
        bpCard = mainActivity.findViewById(R.id.bpSysCard)
        bpDiaCard = mainActivity.findViewById(R.id.bpDiaCard)
        bedActivityCard = mainActivity.findViewById(R.id.bedActivityCard)
        bedPositionCard = mainActivity.findViewById(R.id.bedPositionCard)
        activeActivityCard = mainActivity.findViewById(R.id.activeActivityCard)
        activePositionCard = mainActivity.findViewById(R.id.activePositionCard)
        appLogoButton = mainActivity.findViewById(R.id.appLogo)

        temp = mainActivity.findViewById(R.id.temp)
        bpSys = mainActivity.findViewById(R.id.bpSys)
        bpDia = mainActivity.findViewById(R.id.bpDia)
        bpError = mainActivity.findViewById(R.id.bpError)
        bpAutoMeasurementTime = mainActivity.findViewById(R.id.bp_auto_time)
        bpAutoMeasurementTimeTv = mainActivity.findViewById(R.id.bp_auto_time_tv)
        bpMeasuringStatusTv = mainActivity.findViewById(R.id.bpMeasuringStatus)
        turnTimeSetting = mainActivity.findViewById(R.id.turnTimeSetting)
        textView4 = mainActivity.findViewById(R.id.textView4)
        turnTimeLabel = mainActivity.findViewById(R.id.labelTurnTime)
        textSecondBottom = mainActivity.findViewById(R.id.text_second_bottom)
        positionLeft = mainActivity.findViewById(R.id.left)
        positionBack = mainActivity.findViewById(R.id.back)
        positionRight = mainActivity.findViewById(R.id.right)
        bpErrorCard = mainActivity.findViewById(R.id.bpErrorCard)
        bpVitalLayer = mainActivity.findViewById(R.id.bpVitalLayer)
        positionVitalLayer = mainActivity.findViewById(R.id.positionVitalLayer)

        initBodyPositionUi()

        rollFallCount = mainActivity.findViewById(R.id.bed_fall_alert)
        activeFallCount = mainActivity.findViewById(R.id.active_fall_count)

        tempSensor = mainActivity.findViewById(R.id.tempSensor)
        bpTime = mainActivity.findViewById(R.id.bpTime)

        try{
            CommonDataArea.tempAlarmDao.setAlarmViewAndProperties(ParameterViewsDao(temp, tempCard, null))
            CommonDataArea.bpSysAlarmDao.setAlarmViewAndProperties(ParameterViewsDao(bpSys, bpCard, bpErrorCard, connectionAlertTextView = bpError, currentTimeTextView = bpTime))
            CommonDataArea.bpDiaAlarmDao.setAlarmViewAndProperties(ParameterViewsDao(bpDia, bpDiaCard, bpErrorCard, connectionAlertTextView = bpError, currentTimeTextView = bpTime))
        } catch (ex: Exception){
            LogWriter.writeExceptLog("ViewSetException", ex.stackTraceToString())
            Log.e("ViewSetException", ex.stackTraceToString())
        }
    }

    override fun postUiInitLooperAction() {

    }

    override fun postUiInitAction() {
        onPatientModeChange()
        rollFallCount.text = Html.fromHtml(SettingDaoHelper.AppSettings.FallStream.getValue(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        activeFallCount.text = Html.fromHtml(SettingDaoHelper.AppSettings.FallStream.getValue(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        bpAutoMeasurementTime.text = Html.fromHtml(SettingDaoHelper.AppSettings.BpTimer.getValue(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        bpAutoMeasurementTimeTv.text = Html.fromHtml(SettingDaoHelper.AppSettings.BpTimer.getValue(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        turnTimeSetting.text = Html.fromHtml(SettingDaoHelper.AppSettings.TurnTimer.getValue(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun uiActionListeners() {
        bpCard.setOnClickListener {
            mainActivity.getFragment(IbsmFragments.Windows.BP_SETTINGS)
        }

        bpErrorCard.setOnClickListener {
            mainActivity.getFragment(IbsmFragments.Windows.BP_SETTINGS)
        }

        bedActivityCard.setOnClickListener {
            mainActivity.getFragment(IbsmFragments.Windows.ACTIVITY_SETTINGS)
        }

        bedPositionCard.setOnClickListener {
            mainActivity.getFragment(IbsmFragments.Windows.ACTIVITY_SETTINGS)
        }

        activePositionCard.setOnClickListener {
            mainActivity.getFragment(IbsmFragments.Windows.ACTIVITY_SETTINGS)
        }

        activeActivityCard.setOnClickListener {
            mainActivity.getFragment(IbsmFragments.Windows.ACTIVITY_SETTINGS)
        }

        tempCard.setOnClickListener {
            mainActivity.getFragment(IbsmFragments.Windows.TEMP_SETTINGS)
        }

        pauseAlarm.setOnClickListener {
            mainActivityUIs.toolbarUis.startPauseAlarm()
        }

        scanButton.setOnClickListener {
            if(!CommonDataArea.isBluetoothEnabled) {
                CommonDataArea.sensorList.clear()
                mainActivity.mainActivityHelper.enableBluetoothIfTurnedOff()
            }
            SibelSensorManager.setSensorScanMode(SibelSensorManager.SensorScanMode.NormalScan)
            mainActivity.appManager.requestPermissions()
            mainActivity.dialog.getSensorListDialog()
//            mainActivity.getFragment(IbsmFragments.Windows.MANAGE_SENSORS)
        }

        appLogoButton.setOnClickListener {
           // mainActivity.getFragment(IbsmFragments.Windows.TREND_GRAPHS)
        }

        appLogoButton.setOnLongClickListener {
            mainActivity.dialog.getDataBaseSettingsView()
            true
        }

        bodyPosition.addTextChangedListener {
            if(it != null)
                onBodyPositionTextChangedAction(it.toString())
        }

        tempSensor.addTextChangedListener {
            onTempSensorTextChangeAction(it)
        }

        positionTime.setOnChronometerTickListener {
            updateTurnTime(positionTime.text.toString(), turnTime)
        }

        temp.viewTreeObserver.addOnPreDrawListener {
            tempSensor.visibility = temp.visibility
            true
        }
    }

    private fun initBodyPositionUi(){
        bodyPosition = mainActivity.findViewById(R.id.bodyPosition)
        bodyAngle = mainActivity.findViewById(R.id.bodyAngle)
        activeAngle = mainActivity.findViewById(R.id.activeAngle)
        positionTime = mainActivity.findViewById(R.id.positionTime)
        turnTime = mainActivity.findViewById(R.id.turnTime)
        stepCount = mainActivity.findViewById(R.id.stepCount)
        positionTime.format = "%s"
    }

    fun updateBodyPosition(position: String){
        val currentPosition = bodyPosition.text.toString()
        if(currentPosition.equals(position.ifEmpty { noValueText }, true))
            return

        stopChronometer()
        bodyPosition.text = Html.fromHtml(position, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun onTempSensorTextChangeAction(content: Editable?) {
        content ?: return
        val text = content.toString()
        if (text != SensorType.CHEST.name && text != SensorType.LIMB.name)
            temp.text = Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun onBodyPositionTextChangedAction(newPosition: String){
        if(CommonDataArea.patientMode.equals(bedBoundMode, true)) {
            updatePositionTexts(newPosition)
        }
    }

    private fun startChronometer(){
        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.TurnTimer,CommonDataArea.patientModePeriodicTime)
        positionTime.start()
    }

    private fun stopChronometer(){
        positionTime.stop()
        positionTime.base = SystemClock.elapsedRealtime()
        turnTime.text = Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.CancelAlarm, SettingDaoHelper.AppSettings.TurnTimer)
    }

    private fun updateTurnTime(time: String, textView: TextView){
        val receivedFormat = SimpleDateFormat("mm:ss", Locale.getDefault())
        val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        try {
            val formattedTime = if(time.split(":").size < 3){
                val date = receivedFormat.parse(time)
                dateFormat.parse(dateFormat.format(date!!))
            } else{
                dateFormat.parse(time)
            }
            val calendar = Calendar.getInstance()
            calendar.time = formattedTime!!
            val hour = calendar[Calendar.HOUR_OF_DAY]
            val minutes = calendar[Calendar.MINUTE]
            val seconds = calendar[Calendar.SECOND]
            textView.text = Html.fromHtml(getElapsedTimeString(hour, minutes, seconds), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
    }

    private fun getElapsedTimeString(hr: Int, min: Int, sec: Int): String{
        val hours = if(hr == 0) "" else hr.toString()+"h"
        val minutes = if(min == 0) "" else min.toString()+"m"
        val seconds = if(sec == 0) noValueText else sec.toString()+"s"
        return if(hours.isEmpty() && minutes.isEmpty())
            seconds
        else
            "$hours $minutes"
    }

    private fun updatePositionTexts(position: String){
        val lbrTextView: TextView? = when(position){
            BodyPosition.RIGHT.name -> positionRight
            BodyPosition.LEFT.name -> positionLeft
            BodyPosition.SUPINE.name -> positionBack
            else -> null
        }

        setLBRText(lbrTextView)
    }

    private fun setLBRText(textView: TextView?){
        val positionTextViews = listOf(positionLeft, positionBack, positionRight)
        var color: Int
        for(positionText in positionTextViews){
            color = if(textView != null && positionText == textView)
                ResourcesCompat.getColor(context.resources, R.color.lightBlue, context.theme)
            else
                ResourcesCompat.getColor(context.resources, R.color.white, context.theme)

            positionText.setTextColor(color)
        }
        if(textView != null)
            startChronometer()
    }

    fun onPatientModeChange() {
        val isActive = CommonDataArea.patientMode.equals(activeMode, true)
        if(isActive) {
            activePositionCard.visibility = View.VISIBLE
            activeActivityCard.visibility = View.VISIBLE
            bedPositionCard.visibility = View.GONE
            bedActivityCard.visibility = View.GONE
        } else {
            activePositionCard.visibility = View.GONE
            activeActivityCard.visibility = View.GONE
            bedPositionCard.visibility = View.VISIBLE
            bedActivityCard.visibility = View.VISIBLE
        }

        resetActivityParamsUi()
    }

    private fun resetActivityParamsUi() {
        updateBodyPosition(noValueText)
        bodyAngle.text = Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        activeAngle.text = Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        stepCount.text = Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        setLBRText(null)
    }

    fun uiUpdateOnTurnDue(hasTurnDue: Boolean) {
        if(hasTurnDue) {
            bedPositionCard.strokeColor = context.resources.getColor(R.color.red, context.theme)
            turnTimeSetting.setTextColor(context.getColorStateList(R.color.red))
        } else {
            bedPositionCard.strokeColor = context.resources.getColor(R.color.gray, context.theme)
            turnTimeSetting.setTextColor(context.getColorStateList(R.color.white))
        }

    }

    fun onFallNotified(isAcknowledge: Boolean) {
        val cardAndTextColor = if(isAcknowledge)
            Pair(R.color.gray, R.color.white)
        else
            Pair(R.color.red, R.color.red)

        activeActivityCard.strokeColor = context.resources.getColor(cardAndTextColor.first, context.theme)
        bedActivityCard.strokeColor = context.resources.getColor(cardAndTextColor.first, context.theme)
        rollFallCount.setTextColor(context.getColorStateList(cardAndTextColor.second))
        activeFallCount.setTextColor(context.getColorStateList(cardAndTextColor.second))
    }
}