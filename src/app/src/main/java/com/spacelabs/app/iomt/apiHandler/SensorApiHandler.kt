package com.spacelabs.app.iomt.apiHandler

import android.content.Context
import com.spacelabs.app.common.CommonDataArea.Companion.hasInternetConnection
import com.spacelabs.app.iomt.ApiService
import com.spacelabs.app.iomt.IomtApiManager
import com.spacelabs.app.iomt.RetrofitClient
import com.spacelabs.app.iomt.apiHandler.interfaces.SensorAuthenticationCallback
import com.spacelabs.app.iomt.apiHandler.interfaces.SensorDisconnectCallback
import com.spacelabs.app.iomt.apiHandler.interfaces.SensorMappingCallback
import com.spacelabs.app.iomt.data.dataclasses.MappingResponse
import com.spacelabs.app.iomt.data.dataclasses.SensorAuthenticationResponse
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class SensorApiHandler() {

    private val TAG = "sensorName"

    fun checkSensorAuthentication(sensorName: String, sensorAddress:String, callback: SensorAuthenticationCallback) {
        val apiService = RetrofitClient.instance
        val call = apiService.checkSensorAuthentication(sensorName,sensorAddress)
      //  Log.d(TAG, "sensorName :${sensorName} sensorAddress: $sensorAddress  val tenantID : $tenantID ")
        call.enqueue(object : Callback<SensorAuthenticationResponse> {
            override fun onResponse(
                call: Call<SensorAuthenticationResponse>,
                response: Response<SensorAuthenticationResponse>,
            ) {
                if (response.isSuccessful) {
                    val sensorAuthenticationResponse = response.body()
                    handleSensorAuthenticationResponse(
                        sensorName,
                        sensorAuthenticationResponse,
                        callback
                    )
                } else {
                    callback.onAuthenticationResult(sensorName, false,"Unable to Connect\nServer Error:")
                }
            }

            override fun onFailure(call: Call<SensorAuthenticationResponse>, t: Throwable) {
                if(!hasInternetConnection){
                    callback.onAuthenticationResult(sensorName, false,"Network Error.Unable to Connect")

                }else{
                    callback.onAuthenticationResult(sensorName, false,"Unable to Connect\nServer Error: ")

                }
            }
        })
    }

    private fun handleSensorAuthenticationResponse(
        sensorName: String,
        sensorAuthenticationResponse: SensorAuthenticationResponse?,
        callback: SensorAuthenticationCallback,
    ) {
        if (sensorAuthenticationResponse != null) {
            val status = sensorAuthenticationResponse.status
            if (status.message == "Success" && status.details == "Existing Sensor") {
                //showToast("$sensorName Existing Sensor - Allowing Connection")
                callback.onAuthenticationResult(sensorName, true,"Allowing Connection")
            } else if (status.message == "Error" && status.details == "Sensor Not Registered") {
               // showToast("$sensorName Sensor Not Registered - Connection not allowed")
                callback.onAuthenticationResult(sensorName, false,"$sensorName Sensor Not Registered - Connection not allowed")
            } else {
               // showToast("Unknown status received from server")
                callback.onAuthenticationResult(sensorName, false,"Unknown status received from server")
            }
        } else {
            //showToast("Empty response from server")
            callback.onAuthenticationResult(sensorName, false,"Empty response from server")
        }
    }


    fun mapSensorToDevice(
        deviceId: String,
        sensorName: String,
        sensorMacId: String,
        callback: SensorMappingCallback,
    ) {
        val apiService = RetrofitClient.instance
        val call = apiService.mapSensorToDevice(deviceId, sensorName, sensorMacId)
        call.enqueue(object : Callback<MappingResponse> {
            override fun onResponse(call: Call<MappingResponse>, response: Response<MappingResponse>) {
                if (response.isSuccessful) {
                    val mappingResponse = response.body()
                    if (mappingResponse != null && mappingResponse.status.message == "Success") {
                        callback.onMappingResult(true, mappingResponse.status.details)
                    } else if (mappingResponse != null && mappingResponse.status.message == "Error") {
                        callback.onMappingResult(true, mappingResponse.status.details ?: "Unknown error")
                    } else {
                        callback.onMappingResult(false, "Unknown response from server")
                    }
                } else {
                    callback.onMappingResult(false, "Failed to map sensor to device")
                }
            }

            override fun onFailure(call: Call<MappingResponse>, t: Throwable) {
                if(!hasInternetConnection){
                    callback.onMappingResult(false,"Network Error.Unable to Connect")

                }else{
                    callback.onMappingResult(false,"Unable to Connect\nServer Error: ")

                }
            }
        })
    }

    fun disconnectSensorFromDevice(
        deviceId: String,
        sensorName: String,
        sensorMacId: String,
        callback: SensorDisconnectCallback,
    ) {
        val apiService = RetrofitClient.instance
        val call = apiService.disconnectSensorFromDevice(deviceId, sensorName, sensorMacId)
        call.enqueue(object : Callback<MappingResponse> {
            override fun onResponse(call: Call<MappingResponse>, response: Response<MappingResponse>) {
                if (response.isSuccessful) {
                    val mappingResponse = response.body()
                    if (mappingResponse != null && mappingResponse.status.message == "Success") {
                        callback.onDisconnectResult(true, mappingResponse.status.details)
                    } else {
                        callback.onDisconnectResult(true, mappingResponse?.status?.details ?: "Unknown error")
                    }
                } else {
                    callback.onDisconnectResult(false, "Failed to disconnect sensor from device")
                }
            }

            override fun onFailure(call: Call<MappingResponse>, t: Throwable) {
                if(!hasInternetConnection){
                    callback.onDisconnectResult(false,"Network Error.Unable to Connect")

                }else{
                    callback.onDisconnectResult(false,"Unable to Connect\nServer Error: ")

                }
            }
        })
    }

}
