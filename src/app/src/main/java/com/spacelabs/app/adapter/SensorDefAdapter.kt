package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.SensorDefTable

class SensorDefAdapter(private var patientList: List<SensorDefTable> = emptyList()) : RecyclerView.Adapter<SensorDefAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_sensor_def, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val sensorDefID: TextView = itemView.findViewById(R.id.SensorDefID)
        private val sensorCategory: TextView = itemView.findViewById(R.id.SensorCategory)
        private val sensorName: TextView = itemView.findViewById(R.id.SensorName)
        private val sensorType: TextView = itemView.findViewById(R.id.SensorType)


        fun bind(patient: SensorDefTable) {
            sensorDefID.text = patient.sensorDefId.toString()
            sensorCategory.text = patient.sensorCategory
            sensorName.text = patient.sensorName
            sensorType.text = patient.sensorType
        }
    }
}

