package com.spacelabs.app.activities.snsActivities.activityUi

import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.spacelabs.app.MainActivity
import com.spacelabs.app.activities.commonActivities.activityUi.ActivityUiController
import com.spacelabs.app.activities.iomtActivities.QrScannerActivity
import com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.iomt.IomtApiManager


class QrCodeActivityUiSns(private val qrCodeActivity: QrScannerActivitySns) :
    ActivityUiController(qrCodeActivity) {
       private val TAG = "UIEVENT_Qr_Code_Activity"
    override fun initUi() {
        TODO("Not yet implemented")
    }

    override fun postInitUiActions() {
        TODO("Not yet implemented")
    }

    override fun uiActionListeners() {
        TODO("Not yet implemented")
    }
    inner class UiEventHandler : UiEventCallback {
        @RequiresApi(Build.VERSION_CODES.P)
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            when (event) {
                UiEventCallback.UiEventType.NavigateToNext -> {
                    navigateToNextActivity(

                        //MainActivity::class.java
                        if (enableSnsApi) {
                            if (SettingDaoHelper.AppSettings.AccountId.getValue().isEmpty()) QrScannerActivitySns::class.java else MainActivity::class.java
                        } else {
                            if (IomtApiManager.ACCOUNT_ID.isEmpty()) QrScannerActivity::class.java else MainActivity::class.java
                        }
                    )
                    Log.d(TAG, "uiEvent: $enableSnsApi -> ${SettingDaoHelper.AppSettings.AccountId.getValue()}. ${event.name}")

                }
                else -> Log.d("UiEvent", "QRCODE_UNKNOWN EVENT -> $event")
            }
        }
    }

}
