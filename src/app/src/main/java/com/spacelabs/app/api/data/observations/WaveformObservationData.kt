package com.spacelabs.app.api.data.observations

import android.util.Log
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.api.data.dataclasses.WaveObservationBean
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.iomt.IomtApiManager
import org.json.JSONObject

class WaveformObservationData: ObservationManager(){

    data class Origin(val value: Int)
    data class ValueSampledData(val origin: Origin, val period: Double, val factor: Double, val lowerLimit: Double, val upperLimit: Double, val dimensions: Int, val data: String)
    data class Coding(val system: String, val code: String, val display: String)
    data class Category(val coding: Array<Coding>) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Category

            if (!coding.contentEquals(other.coding)) return false

            return true
        }

        override fun hashCode(): Int {
            return coding.contentHashCode()
        }
    }

    data class Code(val coding: Array<Coding>) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Code

            if (!coding.contentEquals(other.coding)) return false

            return true
        }

        override fun hashCode(): Int {
            return coding.contentHashCode()
        }
    }

    data class Subject(val reference: String, val display: String)
    data class Performer(val reference: String, val display: String)
    data class Device(val reference: String, val display: String)
    data class Component(val code: Code, val valueSampledData: ValueSampledData)

    data class Observation(val resourceType: String,
                           val id: String,
                           val status: String,
                           val category: Array<Category>,
                           val code: Code,
                           val subject: Subject,
                           val effectiveDateTime: String,
                           val performer: Array<Performer>,
                           val device: Device,
                           val component: Array<Component>) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Observation

            if (resourceType != other.resourceType) return false
            if (id != other.id) return false
            if (status != other.status) return false
            if (!category.contentEquals(other.category)) return false
            if (code != other.code) return false
            if (subject != other.subject) return false
            if (effectiveDateTime != other.effectiveDateTime) return false
            if (!performer.contentEquals(other.performer)) return false
            if (device != other.device) return false
            if (!component.contentEquals(other.component)) return false

            return true
        }

        override fun hashCode(): Int {
            var result = resourceType.hashCode()
            result = 31 * result + id.hashCode()
            result = 31 * result + status.hashCode()
            result = 31 * result + category.contentHashCode()
            result = 31 * result + code.hashCode()
            result = 31 * result + subject.hashCode()
            result = 31 * result + effectiveDateTime.hashCode()
            result = 31 * result + performer.contentHashCode()
            result = 31 * result + device.hashCode()
            result = 31 * result + component.contentHashCode()
            return result
        }
    }

    fun createWaveformObservation(paramData: WaveObservationBean, samples: DoubleArray): String {
        val values = replaceCommaWithSpace(samples)/*scaleValues(paramData, samples)*/
        val origin = Origin(paramData.origin)
        val valueSampledData = ValueSampledData(origin, paramData.period, paramData.factor, paramData.originalLowerLimit, paramData.originalUpperLimit, paramData.dimensions, values)
        val coding = arrayOf(Coding(paramData.categoryCode["system"]!!, paramData.categoryCode["code"]!!, paramData.categoryCode["display"]!!))
        val category = arrayOf(Category(coding))
        val codeCoding = arrayOf(Coding(paramData.code["system"]!!, paramData.code["code"]!!, paramData.code["display"]!!))
        val code = Code(codeCoding)
        //val subject = Subject("Patient/${SnsApiManager.apiResponseData.patient.patientID1}", SnsApiManager.apiResponseData.patientName)
        val subject = Subject("Patient/${CommonDataArea.PATIENT.patientId1}", CommonDataArea.PATIENT.firstName)
        val performer = arrayOf(Performer("Practitioner/f005", "A. Langeveld"))
        //val device = Device("Device/${SnsApiManager.IMEI}", paramData.deviceDisplay)
        val device = Device("Device/${IomtApiManager.DEVICE_UUID}", paramData.deviceDisplay)
        val component = Component(Code(arrayOf(Coding(paramData.componentCodeSystem, paramData.componentCode, paramData.deviceDisplay))), valueSampledData)
        val observation =  Observation("Observation", paramData.uuId, "final", category, code, subject, paramData.timestamp, performer, device, arrayOf(component))
        val gson = GsonBuilder().create()
        return gson.toJson(observation)

    }

    /*private fun scaleValues(paramData: WaveObservationBean, samples: DoubleArray): String{
        val values = mutableListOf<Int>()
        for (s in samples){
            val changeInHighLow = (paramData.apiUpperLimit - paramData.apiLowerLimit) / (paramData.originalUpperLimit - paramData.originalLowerLimit)
            val newVal = paramData.origin + (changeInHighLow * s)
            values.add(newVal.roundToInt())
        }
        return replaceCommaWithSpace(values.toIntArray())
    }*/

    private fun replaceCommaWithSpace(samples: DoubleArray): String{
        val newValues = mutableListOf<Double>()
        for(value in samples) {
            newValues.add(value)
        }
        return newValues.joinToString(" ")
    }
}