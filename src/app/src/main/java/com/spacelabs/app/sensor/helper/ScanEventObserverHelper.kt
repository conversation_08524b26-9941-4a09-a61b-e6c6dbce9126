package com.spacelabs.app.sensor.helper

import android.content.Context
import android.util.Log
import com.spacelabs.app.R
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2BloodPressureMonitor
import com.sibelhealth.bluetooth.sensor.sibel.SibelSensor
import com.sibelhealth.bluetooth.sensor.sibel.chest.ChestSensor
import com.sibelhealth.bluetooth.sensor.sibel.limb.LimbSensor
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.info.ConnectionInfo
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.dialogs.PatchConnectionDialogManager
import com.spacelabs.app.ui.dialogs.dialogHelper.SensorListItems
import kotlinx.coroutines.runBlocking

class ScanEventObserverHelper {

    private val context: Context = CommonDataArea.curActivity!!

    fun onSensorScanStartedAction() {
        CommonDataArea.sensorList.clear()
    }

    fun onSensorDiscovered(sensor: Sensor){
        try {
            if((SibelSensorManager.scanMode == SibelSensorManager.SensorScanMode.NfcDiscovered)&&(SibelSensorManager.sensorToDiscover.isNotEmpty())){
                if(sensor.name == SibelSensorManager.sensorToDiscover) {
                    nfcSensorDiscoveryAndConnection(sensor)
                }
                else return
            }

            val sensorListItem = updateSensorNameWithPrefixOnSensorList(sensor)
            if (!(CommonDataArea.sensorList.contains(sensorListItem))) {
                CommonDataArea.sensorList.add(sensorListItem)
                updateSensorList(sensor)
            }
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorDiscovered, sensorListItem)

            connectObtainedPatch(sensor)
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onSensorDiscovered", "${e.printStackTrace()}")
        }
    }

    private fun nfcSensorDiscoveryAndConnection(sensor: Sensor){
        checkConfigurationAndConnect(sensor)
    }

    private fun checkConfigurationAndConnect(sensor: Sensor){
        val sensorName = SibelSensorManager.getEnrolledSensorNameByType(context, sensor.sensorType)

        if(sensorName?.isNotEmpty() == true && sensorName != sensor.name){
            CommonDataArea.curActivity!!.dialog.getSensorReplaceDialog(sensor, sensorName)
        }else{
            reConfigureSensor(sensor)
        }
    }

    fun reConfigureSensor(sensor: Sensor){
        SibelSensorManager.enrollSensorNameByType(context, sensor.name, sensor.sensorType)
        when(sensor.sensorType){
            SensorType.CHEST -> {
                if(CommonDataArea.chestSensor?.isConnected == true){
                    CommonDataArea.chestSensor?.disconnect(true)
                    CommonDataArea.isUserDisconnectedChestPatch = true
                }
                CommonDataArea.chestSensor = sensor as ChestSensor
            }
            SensorType.LIMB -> {
                if(CommonDataArea.limbSensor?.isConnected == true){
                    CommonDataArea.limbSensor?.disconnect(true)
                    CommonDataArea.isUserDisconnectedLimbPatch = true
                }
                CommonDataArea.limbSensor = sensor as LimbSensor
            }
            else -> Log.d("onSensorReConfigure", "Sensor with unknown type -> ${sensor.sensorType}")
        }
        connectObtainedPatch(sensor)
    }

    private fun connectObtainedPatch(sensor: Sensor){
        try {
            var patientId: Int?
            runBlocking {
                //patientId = CommonDataArea.measurementDao.getActivePatientId()
                patientId = CommonDataArea.PATIENT.patientId.toInt()
            }
            if (patientId != null) {
                val sensorName = SibelSensorManager.getEnrolledSensorNameByType(context, sensor.sensorType)

                if(!sensorName.equals(sensor.name, true))
                    return
            }
            val connectionInfo = ConnectionInfo.Builder()
                .autoConnect(true)
                .timeout(30000)
                .build()

            addObserverAndConnectPatch(sensor, connectionInfo)

        } catch (e: Exception) {
            LogWriter.writeExceptLog("onSensorDiscovered inner", e.message)
        }
    }

    private fun addObserverAndConnectPatch(sensor: Sensor, connectionInfo: ConnectionInfo){
        Log.i(SibelSensorManager.tag, "[${sensor.name}] Sensor is ${sensor.sensorType}.")

        val sibelSensor = when(sensor.sensorType){
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> sensor as BP2BloodPressureMonitor
            else -> sensor as SibelSensor
        }
        val sensorObserver = when(sensor.sensorType){
            SensorType.CHEST -> {
                CommonDataArea.isUserDisconnectedChestPatch = false
                SibelSensorManager.chestSensorConnectionObserver
            }
            SensorType.LIMB -> {
                CommonDataArea.isUserDisconnectedLimbPatch = false
                SibelSensorManager.limbSensorConnectionObserver
            }
            else -> {
                CommonDataArea.isUserDisconnectedBpPatch = false
                SibelSensorManager.bpSensorConnectionObserver
            }
        }
        sibelSensor.addSensorObserver(sensorObserver)
        sibelSensor.connect(connectionInfo)

        PatchConnectionDialogManager.setSensor(sensor)
        LogWriter.writeSensorLog("Patch Connection Requested", sensor.name)
    }

    private fun updateSensorNameWithPrefixOnSensorList(sensor: Sensor): SensorListItems{
        val sensorListItems = SensorListItems()
        var prefix = ""
        var icon = 0

        when(sensor.sensorType){
            SensorType.CHEST -> {
                prefix = "Chest Sensor"
                icon = R.drawable.ic_sensor_chest
            }
            SensorType.LIMB -> {
                prefix = "Limb Sensor"
                icon = R.drawable.ic_sensor_limb
            }
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> {
                prefix = "BP Monitor"
                icon = R.drawable.ic_sensor_bp
            }
            else -> Log.d("updateSensorNameOnList", "Sensor with unknown Type -> ${sensor.sensorType}")
        }
        sensorListItems.setSensorName("$prefix - ${sensor.name}")
        sensorListItems.setIcon(icon)
        sensorListItems.setSensor(sensor)
        if(sensor.sensorType != SensorType.BP2_BLOOD_PRESSURE_MONITOR){
            val sibelSensor = sensor as SibelSensor
            sensorListItems.setBattery(sibelSensor.batteryLevel!!)
        }else{
            sensorListItems.setBattery(10.0)
        }

        return sensorListItems
    }

    private fun updateSensorList(sensor: Sensor){
        val sensorType = sensor.sensorType
        val sensorName = SibelSensorManager.getEnrolledSensorNameByType(context, sensorType)
        if(sensorName?.isNotEmpty() == true)
            removeSensorsBySensorId(sensorType)
    }

    private fun removeSensorsBySensorId(sensorType: SensorType) {
        CommonDataArea.sensorList.removeIf {
            it.getSensor()?.sensorType == sensorType
        }
    }
}