package com.spacelabs.app.adt

import android.content.Context
import android.util.Log
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.MainActivity
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.HospitalName
import com.spacelabs.app.dataManager.Patient
import com.spacelabs.app.database.daoHelper.PatientDaoHelper
import com.spacelabs.app.database.entities.PatientTable
import com.spacelabs.app.iomt.IomtApiManager
import com.spacelabs.app.ui.dialogs.CommonNotificationDialogs
import com.spacelabs.app.ui.dialogs.DialogActionHelper
import kotlinx.coroutines.*
import java.io.IOException
import java.net.SocketException

class AdtAPI (mainActivity: MainActivity) {

    private val apiHandler = ApiHandler()

    private val mainActivity: MainActivity
    private val context: Context

    private val patientDaoHelper: PatientDaoHelper

    private val dialogHelper = DialogActionHelper(mainActivity)
    private var isDischargedOnce = false
    private val notificationDialogs: CommonNotificationDialogs
    init {
        this.mainActivity = mainActivity
        context = mainActivity
        notificationDialogs = CommonNotificationDialogs(context)

        patientDaoHelper = PatientDaoHelper(context)
    }

    enum class PatientStatus(val value: String) {
        NotInUse("NotInUse"),
        InUse("InUse")
    }

    private fun onReceivePatientResponse(responseModel: PatientStatusResponseModel?): Boolean {
        val state = responseModel?.status?.message ?: return false
        return when(PatientStatus.values().find { it.value.equals(state, true) }) {
            PatientStatus.NotInUse -> onDischargeButtonClickAction(responseModel)
            PatientStatus.InUse -> !onPatientUpdateButtonClickAction(responseModel)
            else -> false
        }
    }

    fun startApiCallTimer() {
        mainActivity.ioScope.launch {
            var processDelayInSeconds = 60
            try {
                val androidId = IomtApiManager.DEVICE_UUID
                val response = apiHandler.fetchPatientAdmittedStatus(androidId)

                if (response.isSuccessful) {
                    val yourResponseModel = response.body()
                    isDischargedOnce = onReceivePatientResponse(yourResponseModel)
                }
                processDelayInSeconds = if(isDischargedOnce) 5 else 60
            }catch (e: IOException) {
                Log.e("ApiCall", "Network error during API call: ")
            }
            catch (e: Exception) {
                Log.e("ApiCall", "Exception during API call: ${e.message}")
            }
            catch (e: SocketException) {
                if (e.message?.contains("Network is unreachable") == true) {
                    // Handle the network unreachable error
                    Log.e(
                        "ApiCall",
                        "Network is unreachable. Check your network connection.}"
                    )
                } else {
                    // Handle other socket-related errors
                    Log.e("ApiCall", "Socket exception: ")
                }
            }
            delay(processDelayInSeconds * 1000L)
            startApiCallTimer()
        }
    }

    private fun onPatientUpdateButtonClickAction(responseModel: PatientStatusResponseModel): Boolean {
        val hasPatient = responseModel.patient.isNotEmpty()
        if (hasPatient) {
            val updatedPatientDetails = responseModel.patient[0]

            val dobAndAge = patientDaoHelper.getDobAndAge(updatedPatientDetails.dob)
            HospitalName= updatedPatientDetails.companyName
            val patient = Patient(
                CommonDataArea.PATIENT.patientId,
                CommonDataArea.PATIENT.visitId,
                updatedPatientDetails.patientId,
                null,
                updatedPatientDetails.firstName,
                updatedPatientDetails.lastName,
                dobAndAge.key,
                dobAndAge.value,
                updatedPatientDetails.height.toDouble(),
                updatedPatientDetails.weight.toDouble(),
                updatedPatientDetails.gender,
                updatedPatientDetails.bedNo,
                updatedPatientDetails.wardNo,
                updatedPatientDetails.registeredOn,
                updatedPatientDetails.admittedOn

            )

            if(CommonDataArea.PATIENT != patient) {
                val newPatientData: PatientTable = setupPatientWithDetails(patient)
                patientDaoHelper.updatePatientInfo(newPatientData)
                patientAdmitPopUp(updatedPatientDetails.patientId,updatedPatientDetails.firstName ,updatedPatientDetails.lastName)         // For patient admit alert
                Log.d("OnPatientUpdateButtonClickAction:", "Patient Updated")
            } else {
                Log.d("OnPatientUpdateButtonClickAction:", "Same Patient")
            }
        }
        return hasPatient
    }


    private fun setupPatientWithDetails(patient: Patient): PatientTable {
        return PatientTable(
            patient.patientId.toInt(),
            patient.firstName,
            null,
            patient.lastName,
            patient.patientId1,
            patient.patientId2,
            patient.dob,
            patient.age,
            patient.gender,
            patient.height?.toDouble(),
            patient.weight?.toDouble(),
            patient.bedNo,
            patient.admittedOn,
            patient.wardNo,
            patient.registeredOn,
            "active",
            0
        )
    }

    private fun onDischargeButtonClickAction(responseModel: PatientStatusResponseModel?): Boolean {
        responseModel ?: return false

        if(!isDischargedOnce) {
            resetUiAndSensorOnDischarge(context)
            dialogHelper.onDischargePatient()
            CommonDataArea.tempDisplaySensorType = SensorType.CHEST
            patientDischargePopUp(CommonDataArea.PATIENT.patientId1,CommonDataArea.PATIENT.firstName ,CommonDataArea.PATIENT.lastName)
        }
        return true
    }

    private fun resetUiAndSensorOnDischarge(context: Context){
        if(CommonDataArea.chestSensor == null || CommonDataArea.chestSensor?.isConnected!!) {
            dialogHelper.resetSensorConfiguration(
                context,
                CommonDataArea.chestSensor,
                SensorType.CHEST
            )

        }
        if(CommonDataArea.limbSensor == null || CommonDataArea.limbSensor?.isConnected!!) {
            dialogHelper.resetSensorConfiguration(
                context,
                CommonDataArea.limbSensor,
                SensorType.LIMB
            )
        }
        if(CommonDataArea.bpSensor == null||CommonDataArea.bpSensor?.isConnected!!){
            dialogHelper.resetBpSensorConfiguration(context, CommonDataArea.bpSensor)
        }

    }

    private fun patientAdmitPopUp(patientId: String, firstName: String, lastName: String) {
        mainActivity.uiScope.launch {
            if (firstName!="Temp") {
                notificationDialogs.showPatientAdmitAlert(
                    title = "Patient Status",
                    message = "Patient $patientId-$firstName $lastName assigned to Device",
                    additionalMessage = null,
                    sensorId = "",
                    dismissDelayMillis = 100000L
                )
            }else Log.d("PAtientPOPUP", "patientDischargePopUp: Patient $patientId-$firstName $lastName assigned to Device")

        }
    }

    private fun patientDischargePopUp(patientId: String, firstName: String,lastName: String) {
        mainActivity.uiScope.launch {
            if (firstName!="Temp") {
                notificationDialogs.showPatientDischargeAlert(
                    title = "Patient Status",
                    message = "Patient $patientId-$firstName $lastName unassigned from Device",
                    additionalMessage = null,
                    sensorId = "",
                    dismissDelayMillis = 100000L
                )

            }else Log.d("PAtientPOPUP", "patientDischargePopUp: Patient $patientId-$firstName $lastName unassigned from Device")
        }
    }
}
