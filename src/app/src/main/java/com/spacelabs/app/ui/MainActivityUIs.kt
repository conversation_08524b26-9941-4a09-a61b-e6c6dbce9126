package com.spacelabs.app.ui

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.text.Html
import android.text.Spannable
import android.util.Log
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import com.github.mikephil.charting.charts.Chart
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.utils.Utils
import com.google.android.material.card.MaterialCardView
import com.spacelabs.app.R
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.MainActivity
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.alarms.alarmDAO.ParameterViewsDao
import com.spacelabs.app.charts.ECGDataSource
import com.spacelabs.app.charts.PPGDataSource
import com.spacelabs.app.charts.XYDataSource
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.database.daoHelper.PatientDaoHelper
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.ui.activityUIHelpers.MainActivityBottomBarUi
import com.spacelabs.app.ui.activityUIHelpers.MainActivityToolbarUi
import com.spacelabs.app.ui.dialogs.CommonAlarmsDialogController
import kotlinx.coroutines.*
import java.util.*

class MainActivityUIs(mainActivity: MainActivity) {

    private val mainActivityContext: MainActivity
    private val context: Context
    private val mainActivityUIHelper: MainActivityUIHelper

    val toolbarUis: MainActivityToolbarUi
    private val bottomBarUis: MainActivityBottomBarUi

    init {
        mainActivityContext = mainActivity
        context = mainActivity
        mainActivityUIHelper = MainActivityUIHelper()

        toolbarUis = MainActivityToolbarUi(mainActivity)
        bottomBarUis = MainActivityBottomBarUi(mainActivity)
    }

    private val ecgColor = Color.rgb(122, 201, 67)
    private val respColor = Color.rgb(255, 255, 255)

    lateinit var fullFrameLayout: FrameLayout

    private lateinit var hrCard: MaterialCardView
    private lateinit var rrCard: MaterialCardView
    private lateinit var spo2Card: MaterialCardView

    //Center console

    private lateinit var ecgScale: TextView

    private lateinit var ecgAlarmText: TextView
    private lateinit var ecgAlarmPrefix: TextView
    private lateinit var respAlarmText: TextView
    private lateinit var respAlarmPrefix: TextView
    private lateinit var spo2AlarmText: TextView
    private lateinit var spo2AlarmPrefix: TextView

     lateinit var ecgChart: LineChart
    private lateinit var respChart: LineChart
    private lateinit var ppgIr: LineChart

     lateinit var ecgReferenceLineView:View
     lateinit var ecgReferenceLineTv:TextView

    private var chart = ArrayList<LineChart>(3)

    //End of center console

    private lateinit var spo2val: TextView
    private lateinit var spo2HighVal: TextView
    private lateinit var spo2LowVal: TextView

    private lateinit var respVal: TextView
    private lateinit var respHighVal: TextView
    private lateinit var respLowVal: TextView
    private lateinit var hrVal: TextView
    private lateinit var hrHighVal: TextView
    private lateinit var hrLowVal: TextView

    val noValueText = mainActivityContext.getString(R.string.noValue)
    var restartTestCounter=0

    var uiHangDetector =0

    private var xyDataChart = arrayOf(
        XYDataSource(), XYDataSource(), XYDataSource()
    )

    private var chartUpdateTask = arrayOf(
        ChartUpdateTask(),
        ChartUpdateTask(),
        ChartUpdateTask()
    )

    private fun initHrUi(){
        try{
            ecgChart = mainActivityContext.findViewById(R.id.ecgChart)
            ecgChart.setScaleEnabled(false)

            ecgReferenceLineView = mainActivityContext.findViewById(R.id.ecgReferenceLineView)
            ecgReferenceLineTv = mainActivityContext.findViewById(R.id.ecgReferenceLineTv)
            //mainActivityUIHelper.updateEcgReferenceLine("4 x")
            chart.add(ecgChart)

            fullFrameLayout = mainActivityContext.findViewById(R.id.main_activity_full_frame)
            hrCard = mainActivityContext.findViewById(R.id.hrCard)

            ecgAlarmText = mainActivityContext.findViewById(R.id.ecgAlarmText)
            ecgAlarmPrefix = mainActivityContext.findViewById(R.id.ecgAlarmPrefix)
            val connectionAlert: TextView = mainActivityContext.findViewById(R.id.ecgConnectionAlerts)

            val hrAlarmBox: LinearLayout = mainActivityContext.findViewById(R.id.hrAlarmBox)

            val ecgAlarmSilenceText: TextView = mainActivityContext.findViewById(R.id.ecgAlarmSilenceText)

            hrVal = mainActivityContext.findViewById(R.id.hrValue)
            hrHighVal = mainActivityContext.findViewById(R.id.hrHighVal)
            hrLowVal = mainActivityContext.findViewById(R.id.hrLowVal)

            val ecgChartOuter: MaterialCardView = mainActivityContext.findViewById(R.id.ecgChartOuter)

            val paramViews = ParameterViewsDao(hrVal, hrCard, ecgChartOuter, hrHighVal, hrLowVal, connectionAlert, ecgChart, hrAlarmBox, ecgAlarmText, ecgAlarmPrefix, ecgAlarmSilenceText)
            CommonDataArea.hrAlarmDao.setAlarmViewAndProperties(paramViews)
        } catch(ex: Exception){
            Log.d("onHrUiInit", ex.stackTraceToString())
        }
    }

    private fun initRrUi(){
        respChart = mainActivityContext.findViewById(R.id.respChart)
        respChart.setScaleEnabled(false)

        chart.add(respChart)

        rrCard = mainActivityContext.findViewById(R.id.rrCard)

        respAlarmText = mainActivityContext.findViewById(R.id.respAlarmText)
        respAlarmPrefix = mainActivityContext.findViewById(R.id.respAlarmPrefix)
        val connectionAlert: TextView = mainActivityContext.findViewById(R.id.respConnectionAlerts)

        val rrAlarmBox: LinearLayout = mainActivityContext.findViewById(R.id.rrAlarmBox)
        val respAlarmSilenceText: TextView = mainActivityContext.findViewById(R.id.respAlarmSilenceText)

        respVal = mainActivityContext.findViewById(R.id.respValue)
        respHighVal = mainActivityContext.findViewById(R.id.respHighVal)
        respLowVal = mainActivityContext.findViewById(R.id.respLowVal)

        val respChartOuter: MaterialCardView = mainActivityContext.findViewById(R.id.respChartOuter)

        val paramViews = ParameterViewsDao(respVal, rrCard, respChartOuter, respHighVal, respLowVal, connectionAlert, respChart, rrAlarmBox, respAlarmText, respAlarmPrefix, respAlarmSilenceText)
        CommonDataArea.respAlarmDao.setAlarmViewAndProperties(paramViews)
    }

    private fun initSpo2Ui(){
        ppgIr = mainActivityContext.findViewById(R.id.spo2Chrt)
        ppgIr.setScaleEnabled(false)

        chart.add(ppgIr)

        spo2Card = mainActivityContext.findViewById(R.id.spo2Card)

        spo2AlarmText = mainActivityContext.findViewById(R.id.spo2AlarmText)
        spo2AlarmPrefix = mainActivityContext.findViewById(R.id.spo2AlarmPrefix)
        val connectionAlert: TextView = mainActivityContext.findViewById(R.id.spo2ConnectionAlerts)

        val spo2AlarmBox: LinearLayout = mainActivityContext.findViewById(R.id.spo2AlarmBox)
        val spo2AlarmSilenceText: TextView = mainActivityContext.findViewById(R.id.spo2AlarmSilenceText)

        spo2val = mainActivityContext.findViewById(R.id.spo2value)
        spo2HighVal = mainActivityContext.findViewById(R.id.spo2HighVal)
        spo2LowVal = mainActivityContext.findViewById(R.id.spo2LowVal)

        val spo2ChartOuter: MaterialCardView = mainActivityContext.findViewById(R.id.spo2ChartOuter)

        val paramViews = ParameterViewsDao(spo2val, spo2Card, spo2ChartOuter, spo2HighVal, spo2LowVal, connectionAlert, ppgIr, spo2AlarmBox, spo2AlarmText, spo2AlarmPrefix, spo2AlarmSilenceText)
        CommonDataArea.spo2AlarmDao.setAlarmViewAndProperties(paramViews)

    }

    fun uiInit(){
        toolbarUis.setupUi()
        initHrUi()
        initRrUi()
        initSpo2Ui()
        bottomBarUis.setupUi()
    }

    fun setupUiActionHandlers(){

        ecgScale = mainActivityContext.findViewById(R.id.ecgScale)

        ecgChart.setOnClickListener {
            mainActivityUIHelper.ecgChartOnclickListener(ecgChart, ecgScale)
        }

        hrCard.setOnClickListener {
            mainActivityContext.getFragment(IbsmFragments.Windows.HR_SETTINGS)
            CommonDataArea.resetUpdateStsFlg=true
        }

        rrCard.setOnClickListener {
            mainActivityContext.getFragment(IbsmFragments.Windows.RR_SETTINGS)
        }

        spo2Card.setOnClickListener {
            mainActivityContext.getFragment(IbsmFragments.Windows.SPO2_SETTINGS)
        }
    }

    private fun getValueWithSuperScript(value: String, superScript: String): String{
        return "<span>$value<small><small><small><small><sup><small>$superScript</small></sup></small></small></small></small></span>"
    }

    fun startDisplayWatchDogThread(){
        val thread: Thread = object : Thread() {
            override fun run() {
                try {
                    while (!this.isInterrupted) {
                        try {
                            sleep(1500)
                            ++restartTestCounter
                            ++uiHangDetector
                            if(restartTestCounter%60==0)
                                Log.d("Restart Counter", "Count->$restartTestCounter UI Counter->$uiHangDetector")

                            if(uiHangDetector>10){
                                triggerRebirth(context.applicationContext)
                            }
                            if(LogWriter.exceptionCount>20){
                                triggerRebirth(context.applicationContext)
                                LogWriter.exceptionCount=0
                            }
                        } catch (e: Exception) {
                            Log.e("Display values loop",e.stackTraceToString())
                        }
                    }

                } catch (e: InterruptedException) {
                    Log.e("Interrupted exception",e.stackTraceToString())

                }catch (e: Exception){
                    LogWriter.writeExceptLog("Display values Thread ", e.stackTraceToString())
                }
            }
        }
        thread.start()
    }

    private val allowRestart = false

    fun triggerRebirth(context: Context) {
        if(!allowRestart) return

        val packageManager = context.packageManager
        val intent = packageManager.getLaunchIntentForPackage(context.packageName)
        val componentName = intent!!.component
        val mainIntent = Intent.makeRestartActivityTask(componentName)
        context.startActivity(mainIntent)
        Runtime.getRuntime().exit(0)
    }

    class ChartUpdateTask : TimerTask() {
        lateinit var xyDataChart: XYDataSource
        lateinit var chart: LineChart
        private var color = 0
        override fun run() {
            try {
                if (!xyDataChart.isFilling)
                    synchronized(xyDataChart) {
                        xyDataChart.updateData(chart, color)
                    }
            } catch (e: Exception) {
                Log.e("Error", e.toString())
            }
        }
    }

    private fun setupChart(chartNum: Int, xyDataType: XYDataSource.XYDataType) {
        try {
            val yValues: Pair<Float, Float> = when (xyDataType) {
                XYDataSource.XYDataType.ECG -> {
                    CommonDataArea.ecgXYData = ECGDataSource()
                    xyDataChart[chartNum] = CommonDataArea.ecgXYData!!
                    CommonDataArea.ecgXYData?.initialize(XYDataSource.XYDataType.ECG)
                    xyDataChart[chartNum].setData(chart[chartNum], ecgColor)
                    Pair(7f, -7f)
                }
                XYDataSource.XYDataType.Resp -> {
                    CommonDataArea.respXYData = PPGDataSource()
                    xyDataChart[chartNum] = CommonDataArea.respXYData!!
                    CommonDataArea.respXYData?.initialize(XYDataSource.XYDataType.Resp)
                    xyDataChart[chartNum].setData(chart[chartNum], respColor)
                    Pair(0.5f, -0.5f)
                }
                XYDataSource.XYDataType.PPG_IR -> {
                    CommonDataArea.ppgIRXYData = PPGDataSource()
                    xyDataChart[chartNum] = CommonDataArea.ppgIRXYData!!
                    val ppgColor = ResourcesCompat.getColor(mainActivityContext.resources, R.color.spo2Blue, mainActivityContext.theme)
                    CommonDataArea.ppgIRXYData?.initialize(XYDataSource.XYDataType.PPG_IR)
                    xyDataChart[chartNum].setData(chart[chartNum], ppgColor)
                    Pair(1.85E9f, 1.3E9f)
                }
                else -> {
                    Log.d("onSetUpChart", "Default Block")
                    Pair(0f, 0f)
                }
            }

            xyDataChart[chartNum].setupXAxis(chart[chartNum])
            xyDataChart[chartNum].setupYaxis(chart[chartNum], yValues.first, yValues.second)
            chartUpdateTask[chartNum].xyDataChart = xyDataChart[chartNum]
            chartUpdateTask[chartNum].chart = chart[chartNum]
            xyDataChart[chartNum].hideInfo(chart[chartNum])
        } catch (e: Exception) {
            Log.e("onSetUpChart", "e.message")
        }
    }

    private fun setupChartOnSensorConnecting(sensorType: SensorType){
        if(sensorType == SensorType.CHEST){
            setupChart(0, XYDataSource.XYDataType.ECG)
            setupChart(1, XYDataSource.XYDataType.Resp)
            respChart.getPaint(Chart.PAINT_INFO).textSize = Utils.convertDpToPixel(17f)
            mainActivityUIHelper.updateEcgReferenceLine("4 x")
        } else if (sensorType == SensorType.LIMB) {
            setupChart(2, XYDataSource.XYDataType.PPG_IR)
            ppgIr.getPaint(Chart.PAINT_INFO).textSize = Utils.convertDpToPixel(17f)
        }
    }

    fun updateServerStatus(hasConnection: Boolean) {
        val iconId = if(hasConnection)
            R.drawable.ic_upload
        else
            R.drawable.ic_triangle_exclamation
        mainActivityContext.iconServer?.setIcon(iconId)
    }

    private fun isTypeStreaming(type: StreamDataType): Boolean {
        return CommonDataArea.chestSensor?.streamingDataTypes?.contains(type) == true ||
                CommonDataArea.limbSensor?.streamingDataTypes?.contains(type) == true ||
                CommonDataArea.bpSensor?.streamingDataTypes?.contains(type) == true
    }
    private fun onAccelDataReceived(): Triple<TextView, Any, Class<*>> {
        val textView = if(CommonDataArea.patientMode.equals("bed bound", true))
            bottomBarUis.bodyAngle
        else
            bottomBarUis.activeAngle

        return Triple(textView, CommonDataArea.angle, Int::class.java)
    }

    private fun onVitalReceivedAction(type: StreamDataType) {
        if(!isTypeStreaming(type)) return
        val viewAndData: Triple<TextView, Any, Class<*>> = when (type) {
            StreamDataType.HR -> Triple(hrVal, CommonDataArea.HR, Int::class.java)
            StreamDataType.RR -> Triple(respVal, CommonDataArea.RR, Int::class.java)
            StreamDataType.SPO2 -> Triple(spo2val, CommonDataArea.spo2.toInt(), Int::class.java)
            StreamDataType.TEMP_SKIN -> Triple(bottomBarUis.temp, CommonDataArea.temperature.toFloat(), Float::class.java)
            StreamDataType.ACCEL -> onAccelDataReceived()
            StreamDataType.STEP_COUNT -> Triple(bottomBarUis.stepCount, CommonDataArea.stepCount, Int::class.java)
            StreamDataType.BP_SYS -> Triple(bottomBarUis.bpSys, CommonDataArea.BpSys.toInt(), Int::class.java)
            StreamDataType.BP_DIA -> Triple(bottomBarUis.bpDia, CommonDataArea.BpDia.toInt(), Int::class.java)
            StreamDataType.BODY_POSITION -> {
                bottomBarUis.updateBodyPosition(CommonDataArea.bodyPosition)
                null
            }
            else -> null
        } ?: return

        prepareContentAndUpdateView(viewAndData, type)
    }

    private fun prepareContentAndUpdateView(textViewAndData: Triple<TextView, Any, Class<*>>, type: StreamDataType) {
        val content: String = when(textViewAndData.third) {
            Int::class.java -> {
                val value = textViewAndData.second as Int
                if(value == 0) noValueText else value.toString()
            }
            Float::class.java -> {
                val value = textViewAndData.second as Float
                if(value.isNaN() || value == 0f) noValueText else String.format(Locale.US, "%.1f", value)
            }
            String::class.java -> {
                val text = textViewAndData.second as String
                text.ifEmpty { noValueText }
            }
            else -> null
        } ?: return

        updateTextView(textViewAndData.first, content, type)
    }

    private fun updateTextView(textView: TextView, content: String, type: StreamDataType) {
        val superScript = when(type) {
            StreamDataType.TEMP_SKIN -> context.resources.getString(R.string.fahrenheit)
            StreamDataType.SPO2 -> context.resources.getString(R.string.percentage)
            StreamDataType.ACCEL -> context.resources.getString(R.string.degrees)
            else -> String()
        }

        val finalContent = if(superScript.isNotEmpty() && content != noValueText) getValueWithSuperScript(content, superScript) else content
        textView.text = Html.fromHtml(finalContent, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun updateTextView(textView: TextView, content: String) {
        textView.text = Html.fromHtml(content, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun onAlarmThresholdChange(alarmParam: AlarmParametersDao) {
        val highRangeView = alarmParam.paramViews?.highRangeView ?: return
        val lowRangeView = alarmParam.paramViews?.lowRangeView ?: return

        highRangeView.text =
            Html.fromHtml("${alarmParam.highValue.toInt()}", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        lowRangeView.text =
            Html.fromHtml("${alarmParam.lowValue.toInt()}", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        if(alarmParam.paramViews?.paramView?.isVisible == true) {
            highRangeView.visibility = View.VISIBLE
            lowRangeView.visibility = View.VISIBLE
        }
    }

    private fun onSensorConnecting(type: SensorType) {
        setupChartOnSensorConnecting(type)
        onReceiveDialogPopupRequest(Dialogs.Popups.SensorConnectionUpdate)
    }

    private fun onReceivedAlarmUpdate(alarmParam: AlarmParametersDao) {
        alarmParam.paramViews ?: return
        if(!alarmParam.isAttached) return
        mainActivityUIHelper.updateAlarmStrokesAndText(context, alarmParam)
        blinkParamViewByAlarmState(alarmParam)
    }

    private var blinkAnimation: AlphaAnimation? = null

     private fun getBlinkAnimation(): AlphaAnimation {
        if(blinkAnimation == null) {
            blinkAnimation = AlphaAnimation(0.0f, 1.0f)
            blinkAnimation?.duration = 500
            blinkAnimation?.startOffset = 20
            blinkAnimation?.repeatMode = Animation.REVERSE
            blinkAnimation?.repeatCount = Animation.INFINITE
        }

        return blinkAnimation!!
    }

    private fun blinkParamViewByAlarmState(alarmParamDao: AlarmParametersDao) {
        val textView = alarmParamDao.paramViews?.paramView ?: return
        val hasAlarm = alarmParamDao.isBlinking
        if(hasAlarm) {
            val animation = getBlinkAnimation()
            if(textView.animation == null)
                textView.startAnimation(animation)
        }
         else {
            textView.clearAnimation()
        }
    }

    private fun onSettingsChangedUiUpdates(settings: SettingDaoHelper.AppSettings) {
        val value = settings.getValue()
        when (settings) {
            SettingDaoHelper.AppSettings.PatientMode -> bottomBarUis.onPatientModeChange()
            SettingDaoHelper.AppSettings.FallStream -> {
                updateTextView(bottomBarUis.rollFallCount, value)
                updateTextView(bottomBarUis.activeFallCount, value)
            }
            SettingDaoHelper.AppSettings.BpTimer -> {
                updateTextView(bottomBarUis.bpAutoMeasurementTime, value)
                updateTextView(bottomBarUis.bpAutoMeasurementTimeTv, value)
            }
            SettingDaoHelper.AppSettings.TurnTimer -> updateTextView(bottomBarUis.turnTimeSetting, value)
            else -> return
        }
    }

    private fun setOrResetTurnDue(hasTurnDue: Boolean) {
        if(hasTurnDue) {
            mainActivityContext.dialog.getCommonAlarmDialog(CommonAlarmsDialogController.Alerts.TurnTimer)
        } else {
            bottomBarUis.updateBodyPosition(noValueText)
        }
        bottomBarUis.uiUpdateOnTurnDue(hasTurnDue)
    }

    private fun onFallNotificationReceived(isAcknowledged: Boolean) {
        bottomBarUis.onFallNotified(isAcknowledged)
        if(!isAcknowledged)
            mainActivityContext.dialog.getCommonAlarmDialog(CommonAlarmsDialogController.Alerts.Fall)
    }

    private fun onCompanionTextReceived(type: StreamDataType) {

        if (CommonDataArea.bpTime == "Measuring..."){
            bottomBarUis.bpMeasuringStatusTv.visibility = View.VISIBLE
        }else{
            bottomBarUis.bpMeasuringStatusTv.visibility = View.INVISIBLE
        }

        val  textViewAndText = when(type) {
            StreamDataType.BP -> Pair(bottomBarUis.bpTime, CommonDataArea.bpTime)
            StreamDataType.TEMP_SKIN -> Pair(bottomBarUis.tempSensor, CommonDataArea.currentSensorWithTemperature)
            else -> return
        }

        val text = textViewAndText.second.ifEmpty { noValueText }
        updateTextView(textViewAndText.first, text)
    }

    private fun onReceiveDialogPopupRequest(popup: Dialogs.Popups) {
        when(popup) {
            Dialogs.Popups.SensorConnectionUpdate -> mainActivityContext.dialog.getPatchConnectionDialog()
            else -> return
        }
    }

    private fun onSensorDisconnected(sensor: Sensor) {
        if(sensor.sensorType == SensorType.CHEST) {
            bottomBarUis.onPatientModeChange()
            mainActivityUIHelper.setCurrentEcgZoom(ecgChart, ecgScale, 8f)
            ecgReferenceLineTv.visibility = View.INVISIBLE
            ecgReferenceLineView.visibility = View.INVISIBLE
        }

        onReceiveDialogPopupRequest(Dialogs.Popups.SensorConnectionUpdate)
    }

    inner class UiEventHandler: UiEventCallback {
        @RequiresApi(Build.VERSION_CODES.P)
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            mainActivityContext.uiScope.launch {
                when(event){
                    UiEventCallback.UiEventType.ServerConnectionStatusChanged -> updateServerStatus(eventData as Boolean)
                    UiEventCallback.UiEventType.PatientChange -> toolbarUis.displayCurrentPatientName()
                    UiEventCallback.UiEventType.VitalReceived -> onVitalReceivedAction(eventData as StreamDataType)
                    UiEventCallback.UiEventType.VitalCompanionText -> onCompanionTextReceived(eventData as StreamDataType)
                    UiEventCallback.UiEventType.AlarmThresholdChange -> onAlarmThresholdChange(eventData as AlarmParametersDao)
                    UiEventCallback.UiEventType.SensorConnecting -> onSensorConnecting(eventData as SensorType)
                    UiEventCallback.UiEventType.AlarmStateChange -> onReceivedAlarmUpdate(eventData as AlarmParametersDao)
                    UiEventCallback.UiEventType.StreamVisibilityChange -> mainActivityUIHelper.changeParamVisibilityByParamDao(context, eventData as AlarmParametersDao)
                    UiEventCallback.UiEventType.StreamChartUpdate -> mainActivityUIHelper.updateChartData(eventData as XYDataSource)
                    UiEventCallback.UiEventType.SensorDisconnected -> onSensorDisconnected(eventData as Sensor)
                    UiEventCallback.UiEventType.SensorBatteryChanged -> toolbarUis.updateSensorInfoOnToolBar(eventData as SensorType)
                    UiEventCallback.UiEventType.SettingsChanged -> onSettingsChangedUiUpdates(eventData as SettingDaoHelper.AppSettings)
                    UiEventCallback.UiEventType.SetResetTurnDue -> setOrResetTurnDue(eventData as Boolean)
                    UiEventCallback.UiEventType.FallNotified -> onFallNotificationReceived(eventData as Boolean)
                    UiEventCallback.UiEventType.PopupDialog -> onReceiveDialogPopupRequest(eventData as Dialogs.Popups)
                    else -> Log.d("UiEvent", "UNKNOWN EVENT -> $event")
                }
            }
        }
    }
}