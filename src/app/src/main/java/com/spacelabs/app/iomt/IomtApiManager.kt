package com.spacelabs.app.iomt

import android.annotation.SuppressLint
import android.util.Log
import com.spacelabs.app.api.ApiResponseData
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.iomt.apiHandler.interfaces.TokenCallback
import com.spacelabs.app.iomt.data.dataclasses.LoginRequest
import com.spacelabs.app.iomt.data.dataclasses.LoginResponse
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.net.URI

class IomtApiManager {

    init {
        initCmsApi()
    }

    companion object {
        var ACCOUNT_ID = ""
        var DEVICE_EXIST = ""
        var DEVICE_UUID = ""
        var DEVICE_PASSPHRASE = ""
        var DEVICE_OAUTH_USERNAME = ""
        var DEVICE_OAUTH_PASSWORD = ""
        var DEVICE_WEBSOCKET_URL = ""
        var DEVICE_API_URL = ""
        var DEVICE_API_TOKEN = ""
        var isTokenSaved = false
        @SuppressLint("HardwareIds")
        var apiResponseData = ApiResponseData()
        var webSocketClient: IomtWebSocketClientFhir? = null
        fun connectIomt() {
            try {
                val url = "ws://178.128.165.237:4567/source"
                val headers: MutableMap<String, String> = HashMap()
                headers["Authorization"] = "Bearer " + apiResponseData.jwtToken
                webSocketClient = IomtWebSocketClientFhir(URI(url))
            } catch (ex: InterruptedException) {
                Log.d("connectFhir", ex.stackTraceToString())
            }
        }






    }

    private fun initCmsApi() {


    }
    fun setIomtApiUrl() {



    }

    fun getTokenByOauthLogin(callback: TokenCallback) {
        if (DEVICE_OAUTH_USERNAME.isNotEmpty() && DEVICE_OAUTH_PASSWORD.isNotEmpty()) {
            // Call the API if both are not null or empty
            getToken(DEVICE_OAUTH_USERNAME, DEVICE_OAUTH_PASSWORD, callback)
            RetrofitClient.setApiUrl(SettingDaoHelper.AppSettings.apiUrl.getValue())
        } else {
            // Handle the case when the username or password is null or empty
            // For example, you can call the callback with an error
            callback.onTokenReceived(false ,"","QR code error, username or password is empty")
        }
    }

    private fun getToken(username: String, password: String, callback: TokenCallback) {
        val loginRequest = LoginRequest(username, password)
        val call = RetrofitClient.instance.login(loginRequest)
        call.enqueue(object : Callback<LoginResponse> {
            override fun onResponse(call: Call<LoginResponse>, response: Response<LoginResponse>) {
                if (response.isSuccessful) {
                    val loginResponse = response.body()
                    Log.d("Login Success", "Token: ${response.body()?.accessToken}")

                    if (loginResponse != null) {
                        val token = loginResponse.accessToken
                        if (token != null) {
                            Log.d("Login Success", "Token: $token")
                            RetrofitClient.setApiToken(token)
                            isTokenSaved = true
                            callback.onTokenReceived(true,token,"Login Success")
                        } else {
                            Log.d("Login Failed", "Access token is null")
                            callback.onTokenReceived(false,"","Access token is null")
                        }
                    } else {
                        Log.d("Login Failed", "No response body")
                        callback.onTokenReceived(false,"","No Server Response ")
                    }
                } else {
                    Log.d("Login Failed", "Response code: ${response.code()}")
                    callback.onTokenReceived(false,"","Response code: ${response.code()}")
                }
            }

            override fun onFailure(call: Call<LoginResponse>, t: Throwable) {
                Log.d("Login Failed", t.stackTraceToString())
                callback.onTokenReceived(false,"","Network Failure")
            }
        })
    }

}