package com.spacelabs.app.activities.fragments.fragmenthelpers

import android.graphics.drawable.InsetDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatCheckedTextView
import androidx.recyclerview.widget.RecyclerView
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.R
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.sensorService
import com.spacelabs.app.ui.dialogs.dialogHelper.SensorListItems

class SensorRecyclerViewAdapter2(private val items: MutableList<SensorListItems>): RecyclerView.Adapter<SensorRecyclerViewAdapter2.ViewHolder>() {

    private var filterType = SensorType.CHEST
    private val allItemsDiscovered: MutableList<SensorListItems> = mutableListOf()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.sensor_list_row, parent, false)
        val holder = ViewHolder(view)
        holder.setIsRecyclable(true)
        return holder
    }

    override fun getItemCount() = items.count()

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(items[position])
    }

    fun addItem(item: SensorListItems) {
        if(items.contains(item))
            return

        allItemsDiscovered.add(item)
        if(item.getSensor()?.sensorType == filterType) {
            items.add(item)
            notifyItemInserted(itemCount)
        }
    }

    fun clearCurrentItems() {
        val previousSize = items.size
        items.clear()
        notifyItemRangeRemoved(0, previousSize)

        if(!sensorService.isScanning) {
            allItemsDiscovered.clear()
            sensorService.startScan(30000)
        }
    }

    fun recycleViewByType(sensorType: SensorType) {
        filterType = sensorType
        val filteredItems = allItemsDiscovered.filter { it.getSensor()?.sensorType == sensorType }

        clearCurrentItems()
        items.addAll(filteredItems)
        notifyItemRangeInserted(0, items.size)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        private val sensorIcon: ImageView = itemView.findViewById(R.id.sensorIcon)
        private val sensorName: AppCompatCheckedTextView = itemView.findViewById(R.id.sensorName)

        private fun onItemClickedAction() {
            sensorName.isChecked = !sensorName.isChecked
            val sensor = items[absoluteAdapterPosition].getSensor()
            onTapSensorItem(sensor)
        }

        init {
            sensorName.setOnClickListener {
                onItemClickedAction()
            }

            sensorIcon.setOnClickListener {
                onItemClickedAction()
            }
        }

        fun bind(sensorItem: SensorListItems) {
            sensorIcon.setImageResource(sensorItem.getIcon())
            sensorName.text = sensorItem.getSensorName()
            val drawableBatteryIcon = AppCompatResources.getDrawable(itemView.context, sensorItem.getBatteryIcon())
            drawableBatteryIcon?.let {
                val drawableWithMargin = InsetDrawable(it, 0, 0, 20, 0)
                sensorName.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, drawableWithMargin, null)
            }
        }

        private fun onTapSensorItem(sensor: Sensor?) {
            sensor ?: return
            when (sensor.sensorType) {
                SensorType.CHEST -> {
                    CommonDataArea.chestSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
                    CommonDataArea.chestSensorSelected = ""
                }
                SensorType.LIMB -> {
                    CommonDataArea.limbSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
                    CommonDataArea.limbSensorSelected = ""
                }
                SensorType.BP2_BLOOD_PRESSURE_MONITOR -> CommonDataArea.bpSensorSelected = ""
                else -> return
            }
            CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.EnrollSensor, sensor)
        }
    }
}