package com.spacelabs.app.sensor

import android.content.Context
import android.util.Log
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2BloodPressureMonitor
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2DeviceStatus
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.Package
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.data.ResponseData
import com.sibelhealth.bluetooth.sensor.sibel.SibelSensor
import com.sibelhealth.bluetooth.sensor.sibel.chest.ChestSensor
import com.sibelhealth.bluetooth.sensor.sibel.limb.LimbSensor
import com.sibelhealth.bluetooth.sensorservice.datastream.*
import com.sibelhealth.bluetooth.sensorservice.scan.ScanEventObserver
import com.sibelhealth.core.log.Logger
import com.sibelhealth.core.log.LoggerFactory
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.errorcode.ConnectionErrorCode
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.BuildConfig
import com.spacelabs.app.sensor.helper.*
import com.spacelabs.app.sensorService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SibelSensorManager {

    enum class SensorScanMode {
        NfcDiscovered,
        NormalScan,
        New,
        Reconnecting
    }

    companion object {
        val sensorManagerHelper = SensorManagerHelper()
        var scanMode : SensorScanMode = SensorScanMode.NormalScan
        var sensorToDiscover = ""
        private val sensorJob = Dispatchers.IO + Job()
        private val sensorScope = CoroutineScope(sensorJob)

        fun enrollSensorNameByType(context: Context, sensorName: String, sensorType: SensorType){
            val sharedPreference = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
            val editor = sharedPreference.edit()
            editor.putString(sensorType.name, sensorName)
            editor.apply()
        }

        fun getEnrolledSensorNameByType(context: Context, sensorType: SensorType): String?{
            val sharedPreference = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
            return sharedPreference.getString(sensorType.name, "")
        }

        const val tag = "SibelData"

        fun setSensorScanMode(mode: SensorScanMode){
            scanMode = mode
        }

        fun setSensorToScanFor(sensorName: String){
            sensorToDiscover = sensorName
        }

        fun initSibelSensors(context: Context) {
            sensorService.initialize(context)
            sensorService.addSensorServiceObserver(scanEventObserver)
            sensorService.addSensorServiceObserver(dataStreamObserver)
        }

        // SENSOR LOG (Call this function to Start Logging sensor Logs)

        fun startSensorLog() {
            LoggerFactory.instance = loggerFactory
            if(BuildConfig.DEBUG)
                loggerFactory.level = LoggerFactory.Level.DEBUG
            else
                loggerFactory.level = LoggerFactory.Level.INFO
        }

        private val loggerFactory = object : LoggerFactory() {
            override fun createLog(logTag: String): Logger {
                return AndroidLogger(logTag)
            }

        }

        // END OF SENSOR LOG

        private var scanEventObserver = object : ScanEventObserver() {

            val scanEventObserverHelper = ScanEventObserverHelper()

            override fun onSensorScanStarted() {
                scanEventObserverHelper.onSensorScanStartedAction()
            }

            override fun onSensorScanStopped() {
                // Action to perform when sensor scan has stopped
            }

            override fun onSensorDiscovered(sensor: Sensor) {
                scanEventObserverHelper.onSensorDiscovered(sensor)
            }
        }


        private var dataStreamObserver = object : DataStreamObserver() {

            val dataStreamObserverHelper = DataStreamObserverHelper()

            override fun onHealthDataUpdated(sensor: Sensor, dataPackage: HealthDataPackage) {
                super.onHealthDataUpdated(sensor, dataPackage)
                dataStreamObserverHelper.onHealthDataUpdatedAction(sensor, dataPackage)
            }

            override fun onFallCountDataUpdated(sensor: Sensor, dataPackage: FallCountDataPackage) {
                super.onFallCountDataUpdated(sensor, dataPackage)
                dataStreamObserverHelper.onFallCountDataUpdatedAction(sensor, dataPackage)
            }

            override fun onStepCountDataUpdated(sensor: Sensor, dataPackage: StepCountDataPackage) {
                super.onStepCountDataUpdated(sensor, dataPackage)
                dataStreamObserverHelper.onStepCountDataUpdatedAction(sensor, dataPackage)
            }

            override fun onTempDataUpdated(sensor: Sensor, dataPackage: TempDataPackage) {
                super.onTempDataUpdated(sensor, dataPackage)
                dataStreamObserverHelper.onTempDataUpdatedAction(sensor, dataPackage)
            }

            override fun onEcgDataUpdated(sensor: Sensor, dataPackage: EcgDataPackage) {
                dataStreamObserverHelper.onEcgDataUpdatedAction(sensor, dataPackage)
            }

            /*override fun onRRWaveformDataUpdated(sensor: Sensor, dataPackage: RRDataPackage) {
                dataStreamObserverHelper.onRRWaveformDataUpdated(sensor, dataPackage)
            }*/

            override fun onPpgDataUpdated(sensor: Sensor, dataPackage: PpgDataPackage) {
                dataStreamObserverHelper.onPpgDataUpdatedAction(sensor, dataPackage)
            }

            override fun onBioZDataUpdated(sensor: Sensor, dataPackage: BioZDataPackage) {
                dataStreamObserverHelper.onBioZDataUpdatedAction(sensor, dataPackage)
            }

            override fun onDataUpdated(sensor: Sensor, dataPackage: AttributedDataPackage) {
                dataStreamObserverHelper.onCommonDataPacketReceivedAction(sensor, dataPackage)
            }

            override fun onAccelDataUpdated(sensor: Sensor, dataPackage: AccelDataPackage) {
                super.onAccelDataUpdated(sensor, dataPackage)
                dataStreamObserverHelper.onAccelDataUpdatedAction(sensor, dataPackage)
            }
        }

        //connectionEventObserver
        val chestSensorConnectionObserver = object : ChestSensor.Observer() {

            val chestSensorObserverHelper = ChestSensorObserverHelper()
            override fun onConnectionStatusUpdated(
                sensor: Sensor,
                connectionStatus: ConnectionStatus,
                isConnected: Boolean
            ) {
                chestSensorObserverHelper.onConnectionStatusUpdatedAction(connectionStatus, sensor)
            }

            override fun onConnectionFailed(sensor: Sensor, errorCode: ConnectionErrorCode) {
                Log.i(tag, "[${sensor.name}] Sensor connection has failed with error ${errorCode}.")
            }

            override fun onBatteryLevelUpdated(sibelSensor: SibelSensor, batteryLevel: Double) {
                chestSensorObserverHelper.onBatteryLevelUpdatedAction(sibelSensor, batteryLevel)
            }

            override fun onLeadOffStatusUpdated(chestSensor: ChestSensor, isLeadOff: Boolean) {
                if(chestSensor.isConnected){
                    sensorScope.launch {
                        delay(2000)
                        val stillLeadOff = chestSensor.isLeadOff ?: return@launch
                        chestSensorObserverHelper.onLeadOffStatusUpdatedAction(chestSensor, stillLeadOff)
                        chestSensorObserverHelper.resetPointerAndMasterStreamForLeadOff(stillLeadOff)
                    }
                }
            }
        }

        //Limb Sensor Observer
        val limbSensorConnectionObserver = object : LimbSensor.Observer() {

            val limbSensorObserverHelper = LimbSensorObserverHelper()

            override fun onConnectionStatusUpdated(
                sensor: Sensor,
                connectionStatus: ConnectionStatus,
                isConnected: Boolean
            ) {
                limbSensorObserverHelper.onConnectionStatusUpdatedAction(connectionStatus, sensor)
            }

            override fun onConnectionFailed(sensor: Sensor, errorCode: ConnectionErrorCode) {
                Log.i(tag, "[${sensor.name}] Sensor connection has failed with error ${errorCode}.")
            }

            override fun onBatteryLevelUpdated(sibelSensor: SibelSensor, batteryLevel: Double) {
                limbSensorObserverHelper.onBatteryLevelUpdatedAction(sibelSensor, batteryLevel)
            }

            override fun onPoorSkinContactStatusUpdated(limbSensor: LimbSensor, isPoorSkinContact: Boolean) {
                if(limbSensor.isConnected) {
                    limbSensorObserverHelper.onPoorSkinContactStatusUpdatedAction(isPoorSkinContact, limbSensor)
                    limbSensorObserverHelper.resetPointerAndMasterStreamForPoorSkinContact(isPoorSkinContact)
                }
            }
        }

        //Bp Sensor Observer
        val bpSensorConnectionObserver = object: BP2BloodPressureMonitor.Observer(){

            val bpSensorObserverHelper = BpSensorObserverHelper()

            override fun onDeviceStatusUpdated(bloodPressureMonitor: BP2BloodPressureMonitor, deviceStatus: BP2DeviceStatus) {
                bpSensorObserverHelper.onDeviceStatusUpdatedAction(deviceStatus,bloodPressureMonitor)
            }

            override fun onPackageReceived(bloodPressureMonitor: BP2BloodPressureMonitor, pkg: Package<ResponseData>) {
                super.onPackageReceived(bloodPressureMonitor, pkg)
                bpSensorObserverHelper.onPackageReceivedAction(bloodPressureMonitor, pkg)
            }
            override fun onConnectionStatusUpdated(sensor: Sensor, connectionStatus: ConnectionStatus, isConnected: Boolean) {
                bpSensorObserverHelper.onConnectionStatusUpdatedAction(connectionStatus, sensor)
            }

            override fun onBatteryLevelUpdated(
                bloodPressureMonitor: BP2BloodPressureMonitor,
                batteryLevel: Double
            ) {
                super.onBatteryLevelUpdated(bloodPressureMonitor, batteryLevel)
                bpSensorObserverHelper.onBatteryLevelUpdatedAction(bloodPressureMonitor, batteryLevel)
            }
        }
    }
}