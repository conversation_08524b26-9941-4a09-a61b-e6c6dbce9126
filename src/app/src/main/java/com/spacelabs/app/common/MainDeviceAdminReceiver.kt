package com.spacelabs.app.common

import android.app.admin.DeviceAdminReceiver
import android.app.admin.DevicePolicyManager
import android.content.Context
import android.content.Intent

class MainDeviceAdminReceiver: DeviceAdminReceiver() {

    private val APP_PACKAGE = "com.spacelabs.app"
    private val APP_PACKAGES = arrayOf(APP_PACKAGE)
    override fun onEnabled(context: Context, intent: Intent) {
        super.onEnabled(context, intent)
        val dpm = context.getSystemService(Context.DEVICE_POLICY_SERVICE)
                as DevicePolicyManager
        /*val adminName
        dpm.setLockTaskPackages(adminName, APP_PACKAGES)*/
    }

    override fun onDisabled(context: Context, intent: Intent) {
        super.onDisabled(context, intent)
    }
}