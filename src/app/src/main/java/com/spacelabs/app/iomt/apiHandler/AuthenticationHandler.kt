package com.spacelabs.app.iomt.apiHandler


import android.util.Log
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.cmsAccessToken
import com.spacelabs.app.common.CommonDataArea.Companion.isAuthenticated
import com.spacelabs.app.iomt.CmsClient
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import com.spacelabs.app.iomt.RetrofitClient
import com.spacelabs.app.iomt.RetrofitClientTemp
import com.spacelabs.app.iomt.data.dataclasses.MappingResponse
import okhttp3.ResponseBody
import org.json.JSONObject

class AuthenticationHandler {

    fun authenticateDevice(deviceId: String, token: String, callback: (Boolean,String) -> Unit) {
        val apiService = RetrofitClient.instance
        val call = apiService.authenticateDevice(deviceId, token)

        call.enqueue(object : Callback<Boolean> {
            override fun onResponse(call: Call<Boolean>, response: Response<Boolean>) {
                if (response.isSuccessful) {
                    isAuthenticated = response.body() ?: false
                    val message = if (isAuthenticated) "-" else "Device authentication failed."

                    callback(isAuthenticated,message)
                } else {

                    callback(isAuthenticated,"Api Response failed")
                }
            }

            override fun onFailure(call: Call<Boolean>, t: Throwable) {
                callback(isAuthenticated,"Network Failure")
                Log.e(
                    "onFailure",
                    "onFailure:isAuthenticated: $isAuthenticated, ${t.stackTraceToString()} ",
                )
            }
        })
    }




    fun mapDeviceToCompany(
        deviceId: String,
        companyID: String,
        tenantID: String,
        callback: (Boolean, String) -> Unit,
    ) {
        val apiService = RetrofitClient.instance
        val call = apiService.mapDeviceToCompany(deviceId, companyID, tenantID)

        call.enqueue(object : Callback<MappingResponse> {
            override fun onResponse(
                call: Call<MappingResponse>,
                response: Response<MappingResponse>,
            ) {
                if (response.isSuccessful) {
                    val mappingResponse = response.body()
                    if (mappingResponse != null && mappingResponse.status.message == "Success") {
                        callback(true, "Device linked to company successfully.")
                    } else if (mappingResponse != null && mappingResponse.status.message == "Error") {
                        val errorMessage = mappingResponse.status.details
                            ?: "Device is already linked to the company."
                        callback(true, "Failed to link device to company. $errorMessage")
                        Log.d("Not True", "errorMessage: $errorMessage")
                    }
                } else {
                    val errorDetails = getErrorDetails(response.errorBody())
                    callback(false, errorDetails)
                    Log.d(errorDetails, "onResponse: $errorDetails")
                }
            }

            override fun onFailure(call: Call<MappingResponse>, t: Throwable) {
                callback(false, "Network Failure")
                Log.e(
                    "onFailure",
                    "onFailure:isAuthenticated: $isAuthenticated, ${t.stackTraceToString()} ",
                )
            }
        })
    }

    fun deviceToCompanyMapping(
        deviceId: String,
        companyID: String,
        callback: (Boolean, String) -> Unit,
    ) {
        val apiService = RetrofitClient.instance
        val call = apiService.deviceToCompanyMapping(deviceId, companyID)

        call.enqueue(object : Callback<MappingResponse> {
            override fun onResponse(
                call: Call<MappingResponse>,
                response: Response<MappingResponse>,
            ) {
                if (response.isSuccessful) {
                    val mappingResponse = response.body()
                    if (mappingResponse != null && mappingResponse.status.message == "Success") {
                        callback(true, "Device linked to company successfully.")
                    } else if (mappingResponse != null && mappingResponse.status.message == "Error") {
                        val errorMessage = mappingResponse.status.details
                            ?: "Device is already linked to the company."
                        callback(true, "Failed to link device to company. $errorMessage")
                        Log.d("Not True", "errorMessage: $errorMessage")
                    }
                } else {
                    val errorDetails = getErrorDetails(response.errorBody())
                    callback(false, errorDetails)
                    Log.d(errorDetails, "onResponse: $errorDetails")
                }
            }

            override fun onFailure(call: Call<MappingResponse>, t: Throwable) {
                callback(false, "Network Failure")
                Log.e(
                    "onFailure",
                    "onFailure:isAuthenticated: $isAuthenticated, ${t.stackTraceToString()} ",
                )
            }
        })
    }

    private fun getErrorDetails(errorBody: ResponseBody?): String {
        return try {
            val errorJson = errorBody?.string()
            val jsonObject = JSONObject(errorJson.toString())
            jsonObject.getJSONObject("status").getString("details")
        } catch (e: Exception) {
            "Error parsing error details"
        }
    }

    fun getAccessToken(deviceId: String, callback: (String?) -> Unit) {
        val apiService = RetrofitClient.instance
        val call = apiService.getthetoken(deviceId)

        call.enqueue(object : Callback<MappingResponse> {
            override fun onResponse(
                call: Call<MappingResponse>,
                response: Response<MappingResponse>,
            ) {
                if (response.isSuccessful) {
                    val mappingResponse = response.body()
                    //Log.d("accessToken", "onResponse: ${response.body()}")
                    if (mappingResponse != null) {
                        val accessToken = mappingResponse.accesstoken
                        cmsAccessToken = accessToken

                        callback(accessToken)
                        // Save the accessToken in CommonDataArea.CmsAccessToken
                        //Log.d("accessToken", "onResponse: $accessToken")
                    } else {
                        callback(response.toString())
                    }
                } else {

                    callback(response.toString())
                }
            }

            override fun onFailure(call: Call<MappingResponse>, t: Throwable) {
                callback("Network Failure")
                Log.e("getAccessToken", " Server Error: ${t.message.toString()}")
            }
        })
    }

    fun getAccessTokenforCms() {
        getAccessToken(CommonDataArea.ANDROID_ID) { accessToken ->
            if (cmsAccessToken != null) {
               // Log.d("accessToken", "getAccessTokenforCms: $accessToken")
                val cmsClient = CmsClient()
                cmsClient.cmsConnect()
            } else {

              //  Log.d("accessToken", "accessToken_is_NULL: $cmsAccessToken ")
            }
        }
    }

    fun isDeviceRegistered(deviceId: String, callback: (Boolean, String) -> Unit) {
        val apiService = RetrofitClientTemp.getApiService()
        val call = apiService.isDeviceRegistered(deviceId)

        call.enqueue(object : retrofit2.Callback<Boolean> {
            override fun onResponse(call: retrofit2.Call<Boolean>, response: retrofit2.Response<Boolean>) {
                if (response.isSuccessful) {
                    val isRegistered = response.body() ?: false
                    if (isRegistered) {
                        callback(true, "-")
                    } else {
                        retryWithTestUrl(deviceId, callback)
                    }
                } else {
                    // If the response is not successful, try with the test URL
                    retryWithTestUrl(deviceId, callback)
                }
            }

            override fun onFailure(call: Call<Boolean>, t: Throwable) {
                // If the first call fails due to network error, try with the test URL
                retryWithTestUrl(deviceId, callback)
            }
        })
    }

    private fun retryWithTestUrl(deviceId: String, callback: (Boolean, String) -> Unit) {
        val BASE_URL_TEST = "https://test.iorbit.health:8445/api/v1/"
        val apiServiceTest = RetrofitClientTemp.getApiService(BASE_URL_TEST)
        val callTest = apiServiceTest.isDeviceRegistered(deviceId)

        callTest.enqueue(object : retrofit2.Callback<Boolean> {
            override fun onResponse(call: retrofit2.Call<Boolean>, response: retrofit2.Response<Boolean>) {
                if (response.isSuccessful) {
                    val isRegistered = response.body() ?: false
                    val message = if (isRegistered) "-" else "Web App Registration is Not Completed"
                    callback(isRegistered, message)
                } else {
                    callback(false, "Api Response Failure")
                }
            }

            override fun onFailure(call: retrofit2.Call<Boolean>, t: Throwable) {
                callback(false, "Network Failure")
                Log.e("isDeviceRegistered", "Server Error: ${t.message.toString()}")
            }
        })
    }


}