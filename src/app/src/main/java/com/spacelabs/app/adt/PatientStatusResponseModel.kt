package com.spacelabs.app.adt

data class PatientStatusResponseModel(
    val patient: List<Patient>,
    val status: StatusMessage
)

data class StatusMessage(
    val message: String
)

data class Patient(
    val patientuuid: String,
    val patientId: String,
    val firstName: String,
    val lastName: String,
    val gender: String,
    val dob: String,
    val companyName:String,
    val height: String,
    val weight: String,
    val bedNo: String,
    val wardNo: String,
    val registeredOn: String,
    val admittedOn: String,

)

