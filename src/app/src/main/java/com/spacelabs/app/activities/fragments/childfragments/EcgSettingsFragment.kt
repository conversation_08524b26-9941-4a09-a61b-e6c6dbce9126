package com.spacelabs.app.activities.fragments.childfragments

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.widget.EditText
import android.widget.Switch
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.AlarmDaoHelper
import java.util.AbstractMap

class EcgSettingsFragment(private val paramSettingsHelper: ParamSettingsHelper): Fragment(R.layout.settings_dialog_ecg) {
    //UI variables
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private lateinit var ecgAlarmSwitch: Switch

    private lateinit var ecgHighHr: EditText
    private lateinit var highIncButton: AppCompatButton
    private lateinit var highDecButton: AppCompatButton

    private lateinit var ecgExtremeHighHr: EditText
    private lateinit var exHighIncButton: AppCompatButton
    private lateinit var exHighDecButton: AppCompatButton

    private lateinit var ecgLowHr: EditText
    private lateinit var lowIncButton: AppCompatButton
    private lateinit var lowDecButton: AppCompatButton

    private lateinit var ecgExtremeLowHr: EditText
    private lateinit var exLowIncButton: AppCompatButton
    private lateinit var exLowDecButton: AppCompatButton

    //Normal variables
    private lateinit var incButtonsMap: Map<AppCompatButton, EditText>
    private lateinit var decButtonsMap: Map<AppCompatButton, EditText>
    private lateinit var alarmParamsDao: AlarmParametersDao
    private lateinit var alarmDaoHelper: AlarmDaoHelper

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        alarmDaoHelper = context?.let { AlarmDaoHelper(it) }!!
        alarmParamsDao = CommonDataArea.hrAlarmDao

        ecgAlarmSwitch = view.findViewById(R.id.ecgAlarmSwitch)
        ecgHighHr = view.findViewById(R.id.ehHr)
        highIncButton = view.findViewById(R.id.highInc)
        highDecButton = view.findViewById(R.id.highDec)

        ecgLowHr = view.findViewById(R.id.elHr)
        lowIncButton = view.findViewById(R.id.lowInc)
        lowDecButton = view.findViewById(R.id.lowDec)

        ecgExtremeHighHr = view.findViewById(R.id.eehHr)
        exHighIncButton = view.findViewById(R.id.exHighInc)
        exHighDecButton = view.findViewById(R.id.exHighDec)

        ecgExtremeLowHr = view.findViewById(R.id.exLowHr)
        exLowIncButton = view.findViewById(R.id.exLowInc)
        exLowDecButton = view.findViewById(R.id.exLowDec)

        postUiInitActions()
        uiActionListeners()
    }

    fun postUiInitActions() {
        ecgAlarmSwitch.isChecked = alarmParamsDao.alarmStatus

        if(alarmParamsDao.alarmStatus){
            ecgAlarmSwitch.thumbTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
            ecgAlarmSwitch.trackTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
        }else{
            ecgAlarmSwitch.thumbTintList =
                context?.resources?.getColorStateList(R.color.white, context!!.theme)
            ecgAlarmSwitch.trackTintList =
                context?.resources?.getColorStateList(R.color.white, context!!.theme)
        }

        incButtonsMap = mapOf(highIncButton to ecgHighHr, lowIncButton to ecgLowHr, exHighIncButton to ecgExtremeHighHr, exLowIncButton to ecgExtremeLowHr)
        decButtonsMap = mapOf(highDecButton to ecgHighHr, lowDecButton to ecgLowHr, exHighDecButton to ecgExtremeHighHr, exLowDecButton to ecgExtremeLowHr)

        ecgHighHr.setText("${alarmParamsDao.highValue.toInt()}")
        ecgLowHr.setText("${alarmParamsDao.lowValue.toInt()}")
        ecgExtremeHighHr.setText("${alarmParamsDao.extremeHighValue.toInt()}")
        ecgExtremeLowHr.setText("${alarmParamsDao.extremeLowValue.toInt()}")
    }

    fun uiActionListeners() {
        ecgAlarmSwitch.setOnCheckedChangeListener { _, isChecked ->
            val streamTypes = arrayOf(StreamDataType.HR, StreamDataType.ECG)
            val hasActionCompleted = paramSettingsHelper.alarmSwitchActionListener(ecgAlarmSwitch, isChecked, streamTypes, CommonDataArea.chestSensor as Sensor?)
            if(hasActionCompleted) {
                alarmParamsDao.alarmStatus = isChecked
                alarmDaoHelper.updatePatientAlarmToDb(alarmParamsDao)
                paramSettingsHelper.uiEventOnSwitchAction(alarmParamsDao)
            } else {
                ecgAlarmSwitch.isChecked = !isChecked
                Toast.makeText(context, "Action Not Allowed, No Sensor Found", Toast.LENGTH_SHORT).show()
            }
        }

        paramSettingsHelper.incDecButtonsActionListeners(incButtonsMap, decButtonsMap, 5)

        /*ecgVolume.addOnChangeListener(Slider.OnChangeListener { slider, value, _ ->
            paramSettingsHelper.onVolumeSliderChangeAction(value, alarmParamsDao)
            alarmDaoHelper.updatePatientAlarmToDb(alarmParamsDao)
            slider.value = (alarmParamsDao.alarmSound / 15.0).toFloat()
        })*/

        setupTextChangedListeners(ecgExtremeHighHr, ParamSettingsHelper.SettingsType.ExtremeHigh)
        setupTextChangedListeners(ecgHighHr, ParamSettingsHelper.SettingsType.High)
        setupTextChangedListeners(ecgLowHr, ParamSettingsHelper.SettingsType.Low)
        setupTextChangedListeners(ecgExtremeLowHr, ParamSettingsHelper.SettingsType.ExtremeLow)
    }

    private fun setupTextChangedListeners(editText: EditText, type: ParamSettingsHelper.SettingsType){
        val editTextAndType: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(editText, type)
        paramSettingsHelper.settingsValueTextChangedActionListener(editTextAndType, alarmParamsDao, alarmDaoHelper)
    }
}