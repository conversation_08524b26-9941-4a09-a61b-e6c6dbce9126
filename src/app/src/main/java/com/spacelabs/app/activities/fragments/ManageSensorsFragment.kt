package com.spacelabs.app.activities.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.SwitchCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.ManageSensorsFragmentHelper
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.sensorService
import com.spacelabs.app.ui.dialogs.dialogHelper.SensorListItems
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class ManageSensorsFragment(private val fragmentHolder: FrameLayout): Fragment(R.layout.fragment_sensor_list) {

    //UI Variables
    private lateinit var sensorList: RecyclerView

    private lateinit var saveBtn: AppCompatButton

    private lateinit var cancelBtn: Button
    private lateinit var closeBtn: Button

    private lateinit var noSensorText: TextView

    private lateinit var sensorAuthenticationCheck: SwitchCompat

    //Normal Variables
    private lateinit var uiScope: CoroutineScope
    private lateinit var mngSensorHelper: ManageSensorsFragmentHelper

    companion object {
        var IsActive = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        IsActive = true
        val view = super.onCreateView(inflater, container, savedInstanceState)
        view?.setOnTouchListener(fun(_: View, _: MotionEvent): Boolean {
            return true
        })

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        uiScope = CoroutineScope(Dispatchers.Main)
        mngSensorHelper = ManageSensorsFragmentHelper(context)

        sensorList = view.findViewById(R.id.sensorsList)
        saveBtn = view.findViewById(R.id.connectBtn)
        cancelBtn = view.findViewById(R.id.cancel)

        mngSensorHelper.initMngSensorUi(view)

        noSensorText = view.findViewById(R.id.noSensorText)
        closeBtn = view.findViewById(R.id.closeBtn)
        sensorAuthenticationCheck = view.findViewById(R.id.sensorAuthenticationCheck)

        sensorList.layoutManager = LinearLayoutManager(context)
        sensorList.adapter = mngSensorHelper.adapter
        sensorList.visibility = View.VISIBLE
        sensorList.isEnabled = true
        closeBtn.setOnClickListener {
            closeFragment()
        }
    }

    override fun onStart() {
        super.onStart()
        fragmentHolder.visibility = View.VISIBLE
        CommonEventHandler.registerUiEventCallback(ManageSensorUiHandler(), this::class.java)
        sensorService.startScan(30000)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        closeFragment()
    }

    private fun closeFragment(){
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
        uiScope.cancel()
        parentFragmentManager.beginTransaction().remove(this).commit()
        fragmentHolder.visibility = View.GONE
    }

    inner class ManageSensorUiHandler: UiEventCallback {
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            uiScope.launch {
                when(event) {
                    UiEventCallback.UiEventType.SensorDiscovered -> mngSensorHelper.adapter.addItem(eventData as SensorListItems)
                    UiEventCallback.UiEventType.SensorConnecting -> mngSensorHelper.onSensorConnecting(eventData as SensorType)
                    else -> return@launch
                }
            }
        }
    }
}