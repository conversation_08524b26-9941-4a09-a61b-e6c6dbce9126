package com.spacelabs.app.sensor

import android.util.Log
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2BloodPressureMonitor
import com.sibelhealth.bluetooth.sensor.sibel.SibelSensor
import com.sibelhealth.bluetooth.sensor.sibel.chest.ChestSensor
import com.sibelhealth.bluetooth.sensor.sibel.limb.LimbSensor
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.info.ConnectionInfo
import com.spacelabs.app.charts.XYDataSource
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.ecgXYData
import com.spacelabs.app.common.CommonDataArea.Companion.ppgIRXYData
import com.spacelabs.app.common.CommonDataArea.Companion.respXYData
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.interfaces.UiEventCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.Exception

class SensorManagerHelper {

    private val tempCoroutine = CoroutineScope(Dispatchers.IO)
    private var tempJob: Job? = null

    fun reconnectWhenInRange(sensor: Sensor) {
        try {
            val connectionInfo = ConnectionInfo.Builder()
                .autoConnect(true)
                .timeout(30000)
                .build()

            val sensorAndUserDisconnectionFlag = when (sensor.sensorType) {
                SensorType.CHEST -> sensor as ChestSensor to CommonDataArea.isUserDisconnectedChestPatch
                SensorType.LIMB -> sensor as LimbSensor to CommonDataArea.isUserDisconnectedLimbPatch
                else -> sensor as BP2BloodPressureMonitor to CommonDataArea.isUserDisconnectedBpPatch
            }

            val obtainedSensor = sensorAndUserDisconnectionFlag.first
            val isUserDisconnected = sensorAndUserDisconnectionFlag.second

            if (!(sensor.isConnected) && !isUserDisconnected)
                obtainedSensor.connect(connectionInfo)
        } catch (e: Exception) {
            LogWriter.writeExceptLog("Exception", e.stackTraceToString())
        }
    }

    fun measurementDataStreamController(isEnabled: Boolean, sensor: Sensor, types: Array<StreamDataType>) {
        try {
            when (sensor.sensorType) {
                SensorType.CHEST, SensorType.LIMB -> {
                    val sibelSensor = sensor as SibelSensor
                    for (type in types) {
                        if(isEnabled) {
                            startStreamByStreamType(sibelSensor, type)
                            resetPointers(type)
                        } else {
                            sibelSensor.stopStream(type)
                            updatingWaveAndSetMasterStream(sibelSensor,type)
                        }
                    }
                }

                SensorType.BP2_BLOOD_PRESSURE_MONITOR -> {
                    val bpSensor = sensor as BP2BloodPressureMonitor
                    for (type in types) {
                        if (isEnabled) {
                            bpSensor.startStream(type)
//                            CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.StreamTypeStart, type)
                        }
                        else
                            bpSensor.stopStream(type)
                    }
                }

                else ->
                    Log.d("DataStreamController", "Unknown Sensor type")

            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("measurementDataStreamController", ex.stackTraceToString())
            Log.e("measurementDataStreamController", ex.stackTraceToString())
        }
    }

    private fun startStreamByStreamType(sensor: SibelSensor, type: StreamDataType) {
        when (type) {
            StreamDataType.TEMP_SKIN -> {
                tempJob?.cancel()
                tempJob = tempCoroutine.launch {
                    CommonDataArea.currentSensorWithTemperature = "Calibrating"
                    CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalCompanionText, StreamDataType.TEMP_SKIN)
                    delay(300000)
                    sensor.startStream(type)
                }
            }
            StreamDataType.FALL_COUNT -> {
                if (CommonDataArea.fallStream.equals("ON", true))
                    sensor.startStream(type)
            }

            else -> sensor.startStream(type)
        }
        CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.StreamTypeStart, type)
    }

    private fun resetPointers(type: StreamDataType) {
        when (type) {
            StreamDataType.ECG -> {
                ecgXYData?.writePtr = 0
                ecgXYData?.calculateFillingPosition()

            }

            StreamDataType.RR_WAVEFORM -> {

                respXYData?.writePtr = 0
                respXYData?.calculateFillingPosition()

            }

            StreamDataType.PPG_IR -> {
                ppgIRXYData?.writePtr = 0
                ppgIRXYData?.calculateFillingPosition()

            }

            else -> {}
        }
    }

    private fun updatingWaveAndSetMasterStream(sensor: SibelSensor, type: StreamDataType) {
        when (type) {
            StreamDataType.ECG -> {
                CommonDataArea.isECGWritePtrUpdated =false
                if (sensor.isStreaming && CommonDataArea.limbSensor!=null) {
                    CommonDataArea.masterStream = XYDataSource.XYDataType.PPG_IR.name
                }else if (sensor.isStreaming && CommonDataArea.chestSensor!=null){
                    CommonDataArea.masterStream = XYDataSource.XYDataType.Resp.name

                }
                CommonDataArea.masterStream = XYDataSource.XYDataType.PPG_IR.name
            }

            StreamDataType.RR_WAVEFORM -> {
                CommonDataArea.isRESPWritePtrUpdated =false
                if (sensor.isStreaming && CommonDataArea.chestSensor!=null) {
                    CommonDataArea.masterStream = XYDataSource.XYDataType.ECG.name
                }else if (sensor.isStreaming && CommonDataArea.limbSensor!=null){
                    CommonDataArea.masterStream = XYDataSource.XYDataType.PPG_IR.name

                }
            }

            StreamDataType.PPG_IR -> {
                CommonDataArea.isPPGWritePtrUpdated =false
                if (sensor.isStreaming && CommonDataArea.chestSensor!=null) {
                    CommonDataArea.masterStream = XYDataSource.XYDataType.ECG.name
                }else if (sensor.isStreaming && CommonDataArea.chestSensor!=null){
                    CommonDataArea.masterStream = XYDataSource.XYDataType.Resp.name

                }
            }

            else -> {}
        }
    }
}