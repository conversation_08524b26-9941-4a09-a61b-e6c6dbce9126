package com.spacelabs.app.ui.activityUIHelpers

import android.content.Context

abstract class ActivityUiManager(context: Context) {
    protected val context: Context
    init {
        this.context = context
    }

    fun setupUi(){
        beforeUiInit()
        initUi()
        postUiInitAction()
        uiActionListeners()
    }

    abstract fun beforeUiInit()

    abstract fun initUi()

    abstract fun postUiInitLooperAction()

    abstract fun postUiInitAction()

    abstract fun uiActionListeners()

}