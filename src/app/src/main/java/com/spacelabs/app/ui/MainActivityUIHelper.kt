package com.spacelabs.app.ui

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.text.Html
import android.text.Spannable
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.charts.LineChart
import com.google.android.material.card.MaterialCardView
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.R
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.MainActivity
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.charts.XYDataSource
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.ui.dialogs.CommonNotificationDialogs

class MainActivityUIHelper {

    companion object {
        var ecgScale = .25f
    }

    private val grayBg = R.color.gray
    private val componentsGray = R.color.components_gray
    private val cyan = R.color.lowBlue
    private val mainActivityContext: MainActivity = CommonDataArea.curActivity!!
    private val noValueText: String = mainActivityContext.resources.getString(R.string.noValue)
    private val commonNotification = CommonNotificationDialogs(mainActivityContext)

    @SuppressLint("SuspiciousIndentation")
    fun updateDrawableByBatteryStatus(textView: TextView, sensorType: SensorType) {
        val batteryAndSensor: Pair<Double?, Sensor?> = when (sensorType) {
            SensorType.CHEST -> Pair(
                CommonDataArea.chestSensor?.batteryLevel,
                CommonDataArea.chestSensor
            )

            SensorType.LIMB -> Pair(
                CommonDataArea.limbSensor?.batteryLevel,
                CommonDataArea.limbSensor
            )

            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> Pair(
                CommonDataArea.bpSensor?.batteryLevel,
                CommonDataArea.bpSensor
            )

            else -> Pair(null, null)
        }
        val batteryValue =
            if (batteryAndSensor.second?.connectionStatus == ConnectionStatus.CONNECTION_LOST) null else batteryAndSensor.first
        if (batteryValue == null || batteryAndSensor == Pair(null, null)) {
            Log.d("updateDrawableByBatteryStatus", "Battery value or sensor is null for sensor type: $sensorType")
            return
        }

        /*if (CommonDataArea.isToolBarCoroutineRunning) {
            criticalAndLowBatteryStatusPopupForSensor(batteryValue, batteryAndSensor.second!!.name)
        }*/
        val batteryIcon = when (batteryValue) {
            in 0.6..1.0 -> R.drawable.ic_battery_full_solid
            in 0.3..0.5 -> R.drawable.ic_battery_intermediate_solid
            in 0.1..0.2 -> R.drawable.ic_battery_verylow_solid
            in 0.0..0.1 -> R.drawable.battery_warning
            else -> return
        }
        textView.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, batteryIcon, 0)
        textView.compoundDrawablePadding = 16
    }
    private fun criticalAndLowBatteryStatusPopupForSensor(batteryValue: Double, sensorName: String, ) {
        when (batteryValue) {
            in 0.0..0.1 -> {
                CommonDataArea.CriticalBatteryAlarmDao.isAttached = false
                CommonDataArea.CriticalBatteryAlarmDao.alarmStatus = true
                CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.CriticalBatteryAlarm, null)
                commonNotification.showAlert(mainActivityContext.getString(R.string.critical_battery), mainActivityContext.getString(R.string.replace_sensor), null, sensorName, R.drawable.battery_warning, 5000)
                CommonDataArea.CriticalBatteryAlarmDao.isAttached = true
                CommonDataArea.CriticalBatteryAlarmDao.alarmStatus = false
                CommonDataArea.isToolBarCoroutineRunning = false
                CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.NoAlarm, null)
            }
            in 0.1..0.2 -> {
                commonNotification.showAlert(mainActivityContext.getString(R.string.low_battery), mainActivityContext.getString(R.string.replace_sensor), null, sensorName, R.drawable.ic_battery_verylow_solid, 3000)
                CommonDataArea.isToolBarCoroutineRunning = false
            }
        }
    }

    fun setCurrentEcgZoom(chart: LineChart, scaleText: TextView, currentZoom: Float) {
        updateEcgReferenceLine(scaleText.text.toString())
        ecgScale = when (currentZoom) {
            2f -> 1f
            4f -> 0.5f
            8f -> 0.25f
            0.5f -> 4f
            1f -> 2f
            else -> 0f
        }
        chart.axisLeft.axisMaximum = currentZoom
        chart.axisLeft.axisMinimum = -currentZoom

        scaleText.text = Html.fromHtml("<span>${if (ecgScale.toInt() == 0) ecgScale else ecgScale.toInt()} x</span>", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    fun updateEcgReferenceLine(scale: String) {
        mainActivityContext.mainActivityUIs.ecgReferenceLineTv.visibility = View.VISIBLE
        mainActivityContext.mainActivityUIs.ecgReferenceLineView.visibility = View.VISIBLE

        val graphHeight = mainActivityContext.mainActivityUIs.ecgChart.height
        val refLineHeight = when (scale) {
            "0.25 x" -> graphHeight * 0.125
            "0.5 x" -> graphHeight * 0.25
            "1 x" -> graphHeight * 0.5
            "2 x" -> graphHeight * 1.0
            "4 x" -> graphHeight * 0.0625
            else -> graphHeight * 0.0625
        }
        mainActivityContext.mainActivityUIs.ecgReferenceLineView.layoutParams.height = (refLineHeight * 1.6).toInt()
    }


    fun ecgChartOnclickListener(chart: LineChart, scaleText: TextView) {
        when (ecgScale) {
            0.25f -> setCurrentEcgZoom(chart, scaleText, 4f)
            0.5f -> setCurrentEcgZoom(chart, scaleText, 2f)
            1f -> setCurrentEcgZoom(chart, scaleText, 1f)
            2f -> setCurrentEcgZoom(chart, scaleText, 0.5f)
            4f -> setCurrentEcgZoom(chart, scaleText, 8f)
        }
    }

    fun updateAlarmStrokesAndText(context: Context, alarmParam: AlarmParametersDao) {
        val paramViews = alarmParam.paramViews
        val paramOuter = paramViews?.paramOuter
        val chartOuter = paramViews?.paramChartOuter
        val alarmBox = paramViews?.alarmBox
        val alarmPrefix = paramViews?.alarmPrefixView
        val alarmTextView = paramViews?.alarmTextView
        val alarmSilence = paramViews?.timerTextView

        alarmBox?.visibility = if (alarmParam.isBlinking) View.VISIBLE else View.GONE
        alarmBox?.backgroundTintList =
            context.resources.getColorStateList(alarmParam.alarmBgColor, context.theme)
        paramOuter?.strokeColor = context.resources.getColor(alarmParam.alarmBgColor, context.theme)
        chartOuter?.strokeColor = context.resources.getColor(alarmParam.alarmBgColor, context.theme)

        alarmTextView?.setTextColor(
            context.resources.getColor(alarmParam.alarmTextColor, context.theme)
        )
        alarmPrefix?.setTextColor(
            context.resources.getColor(alarmParam.alarmTextColor, context.theme)
        )
        alarmSilence?.setTextColor(
            context.resources.getColor(alarmParam.alarmTextColor, context.theme)
        )

        alarmTextView?.text =
            Html.fromHtml(alarmParam.alarmText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        alarmPrefix?.text =
            Html.fromHtml(alarmParam.alarmPrefix, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        if (!alarmParam.isAcknowledged)
            alarmSilence?.text = Html.fromHtml(String(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun onBpParamVisibilityChange(context: Context, alarmParamDao: AlarmParametersDao) {
        val paramViews = alarmParamDao.paramViews ?: return
        val isStreaming = alarmParamDao.alarmStatus && alarmParamDao.isAttached
        val errorCard = paramViews.paramChartOuter
        val paramOuter = paramViews.paramOuter
        val connectionAlertText = paramViews.connectionAlertTextView

        CommonDataArea.bpSysAlarmDao.paramViews?.paramOuter?.visibility =
            if (isStreaming) View.VISIBLE else View.GONE
        errorCard?.visibility = if (isStreaming) View.GONE else View.VISIBLE

        val strokeColorAndTextBg = when (alarmParamDao.alarmText) {
            context.getString(R.string.no_bp_sensor_connected), context.getString(R.string.monitorOff), -> Pair(grayBg, componentsGray)

            else -> Pair(cyan, cyan)
        }
        if (alarmParamDao.isBlinking && isStreaming) {
            paramOuter?.strokeColor =
                context.resources.getColor(alarmParamDao.alarmBgColor, context.theme)
            errorCard?.strokeColor =
                context.resources.getColor(alarmParamDao.alarmBgColor, context.theme)

        } else {
            paramViews.paramView.clearAnimation()
            paramOuter?.strokeColor = context.resources.getColor(grayBg, context.theme)
            errorCard?.strokeColor =
                context.resources.getColor(strokeColorAndTextBg.first, context.theme)

        }
        val connectionTextViewColor = when (alarmParamDao.alarmText) {
            context.getString(R.string.no_bp_sensor_connected), context.getString(R.string.monitorOff), -> R.color.white
            else -> R.color.black
        }
        connectionAlertText?.text =
            Html.fromHtml(alarmParamDao.alarmText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        connectionAlertText?.setTextColor(context.resources.getColor(connectionTextViewColor, context.theme)
        )
        connectionAlertText?.backgroundTintList = context.resources.getColorStateList(strokeColorAndTextBg.second, context.theme)
        bpTextVisibility()
        paramOuter?.elevation = 0f
    }

    private fun bpTextVisibility() {
        val bpSys = CommonDataArea.bpSysAlarmDao.paramViews
        val bpDia = CommonDataArea.bpDiaAlarmDao.paramViews

        if (CommonDataArea.BpSys.isNaN() && CommonDataArea.BpDia.isNaN() && CommonDataArea.bpTime.isEmpty()) {
            bpSys?.paramView?.text = Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            bpDia?.paramView?.text = Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

            bpSys?.currentTimeTextView?.text =
                Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            bpDia?.currentTimeTextView?.text =
                Html.fromHtml(noValueText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    fun changeParamVisibilityByParamDao(context: Context, alarmParamDao: AlarmParametersDao) {
        try {
            when (alarmParamDao) {
                CommonDataArea.bpSysAlarmDao, CommonDataArea.bpDiaAlarmDao -> {
                    onBpParamVisibilityChange(context, alarmParamDao)
                    return
                }
            }
            val paramViews = alarmParamDao.paramViews ?: return

            val isStreaming = alarmParamDao.alarmStatus && alarmParamDao.isAttached
            val paramChartOuter = paramViews.paramChartOuter
            val paramOuter = paramViews.paramOuter
            val connectionAlertText = paramViews.connectionAlertTextView

            paramViews.alarmBox?.visibility = View.GONE
            paramViews.paramView.clearAnimation()
            val visibility = if (isStreaming) View.VISIBLE else View.GONE
            val paramVisibility = if (visibility == View.GONE) View.INVISIBLE else View.VISIBLE
            val connectionTextVisibility = if (isStreaming) View.GONE else View.VISIBLE
            paramViews.chart?.visibility = visibility
            paramViews.paramView.visibility = paramVisibility
            paramViews.highRangeView?.visibility = paramVisibility
            paramViews.lowRangeView?.visibility = paramVisibility
            connectionAlertText?.visibility = connectionTextVisibility

            setupConnectivityAlarmArea(context, connectionAlertText, paramChartOuter, alarmParamDao)
            paramOuter?.strokeColor = context.resources.getColor(grayBg, context.theme)
        } catch (ex: Exception) {
            Log.e("ChangeParamVisibilityByParamDao", ex.stackTraceToString())
        }
    }
    private fun setupConnectivityAlarmArea(
        context: Context,
        connectionTextView: TextView?,
        chartOuter: MaterialCardView?,
        alarmParam: AlarmParametersDao,
    ) {
        val bgColor = when {
            !alarmParam.isAttached -> alarmParam.alarmBgColor
            !alarmParam.alarmStatus -> R.color.components_gray
            else -> R.color.components_gray
        }
        val chartOuterColor = if (bgColor == componentsGray) grayBg else bgColor
        val textBg = if (bgColor == grayBg) componentsGray else bgColor
        connectionTextView?.text =
            Html.fromHtml(alarmParam.alarmText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        connectionTextView?.backgroundTintList =
            context.resources.getColorStateList(textBg, context.theme)
        chartOuter?.strokeColor = context.resources.getColor(chartOuterColor, context.theme)
    }
    fun updateChartData(xyPlot: XYDataSource) {
        try {
            val chart: LineChart = when (xyPlot) {
                CommonDataArea.ecgXYData -> CommonDataArea.hrAlarmDao.paramViews?.chart
                CommonDataArea.respXYData -> CommonDataArea.respAlarmDao.paramViews?.chart
                CommonDataArea.ppgIRXYData -> CommonDataArea.spo2AlarmDao.paramViews?.chart
                else -> null
            } ?: return
            xyPlot.updateData(chart, 0)
        } catch (ex: Exception) {
            Log.e("chartUpdate", ex.stackTraceToString())
        }
    }
}