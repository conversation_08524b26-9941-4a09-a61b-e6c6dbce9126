package com.spacelabs.app.iomt

import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object RetrofitClientTemp {

    private var BASE_URL_TEST = "https://test.iorbit.health:8445/api/v1/"
    private var BASE_URL =  "https://www.iorbit-tech.com:8443/api/v1/"

    private var TOKEN = ""

    private fun createOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader("Authorization", "Bearer $TOKEN")
                    .build()
                chain.proceed(request)
            }
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            })
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build()
    }

    private fun createRetrofitInstance(baseUrl: String): Retrofit {
        return Retrofit.Builder()
            .baseUrl(baseUrl)
            .addConverterFactory(GsonConverterFactory.create())
            .client(createOkHttpClient())
            .build()
    }

    fun getApiService(baseUrl: String = BASE_URL): ApiService {
        return createRetrofitInstance(baseUrl).create(ApiService::class.java)
    }

    fun setApiToken(token: String) {
        TOKEN = token
    }

    fun setApiUrl(apiUrl: String) {
        BASE_URL = apiUrl
    }

    fun getApiToken(): String {
        return TOKEN
    }
}
