package com.spacelabs.app.api.data.dataclasses




data class VitalObservationBean(
    val resourceType: String = "Observation",
    val status: String = "final",
    val categoryCode: String = "vital-signs",
    val categoryDisplay: String = "Vital Signs",
    val subjectReference: String = "Patient",
    val deviceReference: String = "Device",
    var timestamp: String,
    var uuId: String = "",
    var components: List<Component>
)
/*data class VitalObservationBean(
    val resourceType: String = "Observation",
    val status: String = "final",
    val categoryCode: String = "vital-signs",
    val categoryDisplay: String = "Vital Signs",
    var vitalCode: String,
    var vitalDisplay: String,
    var value: String,
    var valueUnit: String,
    val subjectReference: String = "Patient",
    val deviceReference: String = "Device",
    val valueQuantitySystem: String = "http://unitsofmeasure.org",
    val valueQuantityCode: String = "/min",
    var timestamp: String,
    var uuId: String = String()
)*/
data class Component(
    val system: String,
    val code: String,
    val display: String,
    val value: String,
    val unit: String,
    val valueQuantitySystem: String = "http://unitsofmeasure.org",
    val valueQuantityCode: String = "/min"
)
