package com.spacelabs.app.database.relationships

import androidx.room.Embedded
import androidx.room.Relation
import com.spacelabs.app.database.entities.ParametersTable
import com.spacelabs.app.database.entities.PatientAlarmTable

data class ParameterWithPatientAlarm(
    @Embedded
    val parameter: ParametersTable,
    @Relation(
        parentColumn = "paramId",
        entityColumn = "paramId"
    )
    val patientAlarms: List<PatientAlarmTable>?
)