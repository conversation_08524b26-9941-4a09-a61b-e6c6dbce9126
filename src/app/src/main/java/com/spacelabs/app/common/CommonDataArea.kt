package com.spacelabs.app.common

import android.annotation.SuppressLint
import android.media.MediaPlayer
import android.util.Log
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2BloodPressureMonitor
import com.sibelhealth.bluetooth.sensor.sibel.chest.ChestSensor
import com.sibelhealth.bluetooth.sensor.sibel.limb.LimbSensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.MainActivity
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.alarms.SibelAlarmManager
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.charts.XYDataSource
import com.spacelabs.app.dataManager.Patient
import com.spacelabs.app.database.dao.*
import com.spacelabs.app.iomt.CmsClient
import com.spacelabs.app.connectivity.NfcDiscoveryHelper
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.dialogs.dialogHelper.SensorListItems
import kotlin.math.roundToInt

class CommonDataArea {
    @SuppressLint("StaticFieldLeak")
    companion object {

        val PATIENT: Patient = Patient()

        var biozData = FloatArray(32 * 10)
        var biozWritePointer = 0
        var RR = 0
        var HR = 0
        var PI = 0.0
        var spo2 = 0.0
        var PR = 0.0
        var BpSys = 0.0
        var BpDia = 0.0
        var temperature = 0.0
        var currentSensorWithTemperature = ""
        var bodyPosition = ""
        var angle = 0
        var stepCount = 0
        var fallCount = 0
        var fallStream = "OFF"
        var chestBattery: Double = 0.0
        var limbBattery: Double = 0.0
        var bpMonitorError: String? = null
        var autoBpMode: Boolean = true
        var isAlertShowing: Boolean = false
        var isSensorRemoved:Boolean=false
        var SelectedSensorName:String="Chest"
        var isBatteryReceiverRunning:Boolean=false
        var SSID = String()
        var wifiPassword = String()

        var curActivity: MainActivity? = null

        var runModeEmulator = false

        lateinit var ANDROID_ID: String

        @SuppressLint("StaticFieldLeak")
        lateinit var keystoreManager: KeystoreManager

        //ALARM VALUE
        var hrAlarmDao = AlarmParametersDao()
        var respAlarmDao = AlarmParametersDao()
        var spo2AlarmDao = AlarmParametersDao()
        var tempAlarmDao = AlarmParametersDao()
        var bpSysAlarmDao = AlarmParametersDao()
        var bpDiaAlarmDao = AlarmParametersDao()
        var connectivityAlarmDao = AlarmParametersDao(
            paramName = CurrentAlarmParameter.ConnectivityAlarm.name,
            alarmStatus = true,
            alarmSound = 15
        )
        var CriticalBatteryAlarmDao=AlarmParametersDao(
            paramName = CurrentAlarmParameter.CriticalBatteryAlarm.name,
            alarmStatus = true,
            alarmSound = 60,
            alarmInterval = 60
        )
        var CriticalDeviceBatteryAlarmDao=AlarmParametersDao(
            paramName = CurrentAlarmParameter.CriticalDeviceBatteryAlarm.name,
            alarmStatus = true,
            alarmSound = 60,
            alarmInterval = 60
        )

        var fallAlarmDao = AlarmParametersDao(alarmStatus = true, alarmSound = 15)

        const val EXTREME_HIGH_VALUE = "extremeHighValue"
        const val EXTREME_LOW_VALUE = "extremeLowValue"
        const val HIGH_VALUE = "highValue"
        const val LOW_VALUE = "lowValue"

        var mediaPlayer: MediaPlayer? = null

        var tempDisplaySensorType = SensorType.CHEST

        var currentAlarmPriority = SibelAlarmManager.NO_ALARM

        var tempTime = ""
        var bpTime = ""

        var ecgXYData: XYDataSource? = null
        var respXYData: XYDataSource? = null
        var ppgIRXYData: XYDataSource? = null

        var chestSensor: ChestSensor? = null
        var limbSensor: LimbSensor? = null
        var bpSensor: BP2BloodPressureMonitor? = null

        var chestSensorSelected: String = ""
        var limbSensorSelected: String = ""
        var bpSensorSelected: String = ""

        var bpMeasurementPeriodicTime: String = "1h"
        var patientMode = SettingDaoHelper.AppSettings.PatientMode.getValue()
        var patientModePeriodicTime: String = "15m"
        var TurnTimerTimestampDbValue: String = ""
        var bpTimerTimestampDbValue: String = ""
        var dataDeletionTimer: String = "14d"

        var criticalVolume: Int = 0
        var highVolume: Int = 0
        var mediumVolume: Int = 0
        var lowVolume: Int = 0

        var sensorList = ArrayList<SensorListItems>()
        var sensorTypeList:String=""

        var chestSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
        var limbSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
        var bpSensorConnectingStatus = SibelSensorManager.SensorScanMode.New

        lateinit var measurementDao: MeasurementDataDao

        var currentChestSensorId: Int = 0
        var currentLimbSensorId: Int = 0
        var currentBpSensorId: Int = 0

        var isBluetoothEnabled = false

        var isUserDisconnectedChestPatch = false
        var isUserDisconnectedLimbPatch = false
        var isUserDisconnectedBpPatch = false

        var runConnectionStatusUpdateUiHandler = true

        var hasInternetConnection = false

        @SuppressLint("StaticFieldLeak")
        lateinit var nfcHelper: NfcDiscoveryHelper

        var isTurnDialogShowing = false

        var resetUpdateStsFlg = false

        // waveform synchronization
        var currentFillingTimePos: Float = 0f  // Current filling position in seconds
        var masterStream: String? = null

        // Master stream type dictating the filling position
        var windowSizeInSeconds: Int = 10       // Window size for display in seconds

        var isECGWritePtrUpdated = false
        var isRESPWritePtrUpdated = false
        var isPPGWritePtrUpdated = false
        var isToolBarCoroutineRunning=false

        /*IOMT*/
        var isAuthenticated = false
        var companyID = ""

        var url = SettingDaoHelper.AppSettings.websocketUrl.getValue()

        var cmsAccessToken: String? = null

        var chestSensorAddress: String = ""
        var limbSensorAddress: String = ""
        var bpSensorAddress: String = ""

        lateinit var cmsClient: CmsClient

        var iscmsClientIntialized = false
        var HospitalName:String="Unknown"


        var manualReconnect: Boolean = false

        var isServerOk: Boolean = false

        //End of CMS

        /*SnsApiManager*/
        var enableSnsApi = true

        /*Static Test Data*/
        var useStaticTestData = true

        fun setAlarmVolume(volume: String) {
            val currentVolume = (volume.toFloat()/100) * 15
            criticalVolume = currentVolume.roundToInt()
            /*highVolume = calculateVolumeByPercentage(0.8, currentVolume)
            mediumVolume = calculateVolumeByPercentage(0.6, currentVolume)
            lowVolume = calculateVolumeByPercentage(0.5, currentVolume)*/

            criticalVolume = currentVolume.roundToInt()
            highVolume = currentVolume.roundToInt()
            mediumVolume = currentVolume.roundToInt() /*calculateVolumeByPercentage(0.9, currentVolume)*/
            lowVolume = currentVolume.roundToInt() /*calculateVolumeByPercentage(0.8, currentVolume)*/
        }

        private fun calculateVolumeByPercentage(percentage: Double, volume: Float): Int {
            val minimumVolumeThreshold = 8
            val calculatedVolume = (volume * percentage).roundToInt()
            return if(calculatedVolume < minimumVolumeThreshold)
                minimumVolumeThreshold
            else
                calculatedVolume
        }
    }
}