package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.EventListTable

class EventListAdapter(private var patientList: List<EventListTable> = emptyList()) : RecyclerView.Adapter<EventListAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_event_list, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val eventId: TextView = itemView.findViewById(R.id.event_id)
        private val eventName: TextView = itemView.findViewById(R.id.event_name)
        private val eventType: TextView = itemView.findViewById(R.id.event_type)



        fun bind(patient: EventListTable) {
            eventId.text = patient.eventId.toString()
            eventName.text = patient.eventName
            eventType.text = patient.eventType

        }
    }
}

