package com.spacelabs.app.ui.dialogs

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import com.spacelabs.app.R
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler

class CommonNotificationDialogs (private val context: Context){
    private val handler: Handler = Handler(Looper.getMainLooper())

    @SuppressLint("MissingInflatedId")
    fun showAlert(title: String, message: String, additionalMessage: String?,sensorId: String,iconResId: Int, dismissDelayMillis: Long) {
        // Inflate the custom layout
        val customLayout: View = LayoutInflater.from(context).inflate(R.layout.custom_notification_dialog, null)

        val titleView = customLayout.findViewById<TextView>(R.id.alertTitle)
        val messageView = customLayout.findViewById<TextView>(R.id.alertMessage)
        val additionalMessageView = customLayout.findViewById<TextView>(R.id.additionalMessage) // New TextView for BP
        val sensorIdView = customLayout.findViewById<TextView>(R.id.sensorIdTextView)
        val iconView = customLayout.findViewById<ImageView>(R.id.alertIcon)
        val okButton = customLayout.findViewById<AppCompatButton>(R.id.alertOkButton)
        val closeButton = customLayout.findViewById<Button>(R.id.closeBtn)

        titleView.text = title
        messageView.text = message
        sensorIdView.text = sensorId
        iconView.setImageResource(iconResId)

        additionalMessageView.apply {
            if (additionalMessage.isNullOrBlank()) {
                visibility = View.GONE
            } else {
                text = additionalMessage
                visibility = View.VISIBLE
            }
        }
        sensorIdView.apply {
            if (sensorId.isNullOrBlank()) {
                visibility = View.GONE
            } else {
                text = sensorId
                visibility = View.VISIBLE
            }
        }

        val builder = AlertDialog.Builder(context)
        val alert = builder.create()
        alert.setView(customLayout)

        okButton.setOnClickListener {
            CommonDataArea.isAlertShowing = false
            alert.dismiss()
        }

        closeButton.setOnClickListener {
            CommonDataArea.isAlertShowing = false

            alert.dismiss()
        }

        if (!CommonDataArea.isAlertShowing) {
            alert.show()
            CommonDataArea.isAlertShowing = true
            handler.postDelayed({
                if (alert.isShowing) {
                    CommonDataArea.isAlertShowing = false
                    alert.dismiss()
                }
            }, dismissDelayMillis)

        }

        alert.window?.setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        alert.window?.setBackgroundDrawableResource(android.R.color.transparent)
        alert.setCancelable(false)
    }

    fun showChestAlert(title: String, message: String,additionalMessage: String?,sensorId: String ,dismissDelayMillis: Long) {
        showAlert(title, message,additionalMessage,sensorId, R.drawable.ic_sensor_chest, dismissDelayMillis)
    }

    fun showLimbAlert(title: String, message: String,additionalMessage: String?,sensorId: String, dismissDelayMillis: Long) {
        showAlert(title, message,additionalMessage,sensorId, R.drawable.ic_sensor_limb, dismissDelayMillis)
    }

    fun showBpAlert(title: String, message: String,additionalMessage: String?, sensorId: String,dismissDelayMillis: Long) {
        showAlert(title, message,additionalMessage,sensorId, R.drawable.ic_sensor_bp, dismissDelayMillis)
    }

    fun showPatientAdmitAlert(title: String, message: String, additionalMessage: String?, sensorId: String, dismissDelayMillis: Long) {
        showAlert(title, message, additionalMessage, sensorId, R.drawable.ic_patient_admitted, dismissDelayMillis)
    }

    fun showPatientDischargeAlert(title: String, message: String, additionalMessage: String?, sensorId: String, dismissDelayMillis: Long) {
        showAlert(title, message, additionalMessage, sensorId, R.drawable.ic_patient_discharge, dismissDelayMillis)
    }

}