package com.spacelabs.app.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.util.Log
import android.view.Window
import com.spacelabs.app.R
import com.spacelabs.app.MainActivity
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.ui.Dialogs

abstract class DialogManager(mainActivity: MainActivity) {

    val mainActivityContext: MainActivity
    val context: Context
    val dialogHelper: DialogActionHelper

    val extremeHighValue: String
    val extremeLowValue: String
    val highValue: String
    val lowValue: String

    private val alertConfirmDialogs: AlertConfirmDialogs

    init {
        mainActivityContext = mainActivity
        context = mainActivity
        dialogHelper = DialogActionHelper(mainActivityContext)
        alertConfirmDialogs = AlertConfirmDialogs(mainActivityContext.applicationContext)

        extremeHighValue = CommonDataArea.EXTREME_HIGH_VALUE
        extremeLowValue = CommonDataArea.EXTREME_LOW_VALUE
        highValue = CommonDataArea.HIGH_VALUE
        lowValue = CommonDataArea.LOW_VALUE
    }

    companion object {
        var SimpleDialog: Dialog? = null
    }

    fun createDialog(dialog: Dialog?, layout: Int): Dialog{
        if(dialog != null)
            return dialog

        val newDialog = Dialog(context, android.R.style.Theme_Black_NoTitleBar_Fullscreen)
        setupDialog(newDialog, layout)
        return newDialog
    }

    fun createWrapContentDialogs(layout: Int): Dialog {
        val dialog = Dialog(context)
        try {
            dialog.setContentView(layout)
            dialog.window?.setBackgroundDrawableResource(R.color.fullTransparency)
            dialog.setCancelable(false)
            // use DialogLeftToRightAnimation from styles for animation

            initUiIfDialogIsShowing(dialog, layout)
        } catch(ex: Exception) {
            LogWriter.writeExceptLog("CreateWrapContentDialogs", ex.stackTraceToString())
            Log.d("CreateWrapContentDialogs", ex.stackTraceToString())
        }

        return dialog
    }

    abstract fun onCreateDialogActions(context: Context, dialog: Dialog, layout: Int)

    abstract fun initUIs(dialog: Dialog)

    abstract fun postUiInitActions(context: Context)

    abstract fun uiActionListeners(context: Context, dialog: Dialog)

    private fun setupDialog(dialog: Dialog, layout: Int){
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(layout)
        dialog.window?.setBackgroundDrawableResource(R.color.SibelTransparency)
        // use DialogUpAndDownAnimation from styles for animation

        initUiIfDialogIsShowing(dialog, layout)
    }

    private fun initUiIfDialogIsShowing(dialog: Dialog, layout: Int){
        if(!dialog.isShowing || !Dialogs.ACTIVE_DIALOGS.contains(dialog)) {
            onCreateDialogActions(context, dialog, layout)
            initUIs(dialog)
            postUiInitActions(context)
            uiActionListeners(context, dialog)
            dialog.show()
            Dialogs.ACTIVE_DIALOGS.add(dialog)
        }
    }

    protected fun closeDialog(dialog: Dialog){
        if(Dialogs.ACTIVE_DIALOGS.contains(dialog)){
            Dialogs.ACTIVE_DIALOGS.remove(dialog)
        }

        SimpleDialog = null
        dialog.dismiss()
    }

}