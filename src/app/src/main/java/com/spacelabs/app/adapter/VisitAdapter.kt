package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.VisitTable

class VisitAdapter(private var patientList: List<VisitTable> = emptyList()) : RecyclerView.Adapter<VisitAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_visit, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
       private val visitID: TextView = itemView.findViewById(R.id.VisiID)
        private val patientId: TextView = itemView.findViewById(R.id.PatientId)
        private val patientID2: TextView = itemView.findViewById(R.id.PatientID2)
        private val visitDate: TextView = itemView.findViewById(R.id.VisitDate)
        private val admitDateTime: TextView = itemView.findViewById(R.id.AdmitDateTime)

        fun bind(patient: VisitTable) {
            visitID.text = patient.visitId.toString()
            patientId.text = patient.patientId.toString()
            patientID2.text = patient.patientID2
            visitDate.text = patient.visitDate
            admitDateTime.text = patient.admitDateTime
        }
    }
}

