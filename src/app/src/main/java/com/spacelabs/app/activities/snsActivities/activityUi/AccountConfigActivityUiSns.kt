package com.spacelabs.app.activities.snsActivities.activityUi

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.os.Build
import android.util.Log
import android.widget.ImageView
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatButton
import com.google.zxing.BarcodeFormat
import com.google.zxing.WriterException
import com.journeyapps.barcodescanner.BarcodeEncoder
import com.spacelabs.app.MainActivity
import com.spacelabs.app.R
import com.spacelabs.app.activities.commonActivities.activityUi.ActivityUiController
import com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
import com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.encryptWithPublicKey
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.generateQRCode
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.generateRandomPassphrase
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.generateRandomUUID
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils.loadPublicKeyFromAssets
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.interfaces.UiEventCallback
import java.security.PublicKey

class AccountConfigActivityUiSns(accountConfigActivity: AccountConfigActivitySns): ActivityUiController(accountConfigActivity) {



    private lateinit var qrCodeImageView: ImageView

    private lateinit var nextBtn: AppCompatButton

    private var publicKey: PublicKey? = null

    private val accountConfigActivity: AccountConfigActivitySns
    init {
        this.accountConfigActivity = accountConfigActivity
    }

    override fun initUi() {


        qrCodeImageView = accountConfigActivity.findViewById(R.id.qrCodeImageView)

        nextBtn = accountConfigActivity.findViewById(R.id.nextButton_sns_api)

        publicKey = loadPublicKeyFromAssets()
    }

    override fun postInitUiActions() {
        Log.d("SNS_QR_DEBUG", "postInitUiActions called - enableSnsApi: $enableSnsApi")
        if (enableSnsApi) {
            Log.d("SNS_QR_DEBUG", "Calling generateAndDisplayQRCode() for SNS flow")
            generateAndDisplayQRCode()
        } else {
            Log.d("SNS_QR_DEBUG", "SNS API disabled, not generating QR code")
        }
    }

    override fun uiActionListeners() {
        nextBtn.setOnClickListener{
            onNextButtonClickedAction()
        }


    }

    private fun onNextButtonClickedAction(){

        if (enableSnsApi) { accountConfigActivity.accountConfigHelper.saveAccountIdSettingsAndNavigateToNext("true") }
        Log.d("SNS", "onNextButtonClickedAction: $enableSnsApi ${SettingDaoHelper.AppSettings.HasAuthenticated.key}")
    }



    @SuppressLint("HardwareIds")
    private fun generateAndDisplayBarcode() {
        try {
            // Get device details
      /*      val androidId = android.provider.Settings.Secure.getString(
                accountConfigActivity.contentResolver,
                android.provider.Settings.Secure.ANDROID_ID
            )*/
            /*SAVE RANDOM DEVICE UUID*/  // val deviceUuid = if (SettingDaoHelper.AppSettings.DeviceUuid.getValue().length != 36) generateRandomUUID() else SettingDaoHelper.AppSettings.DeviceUuid.getValue()

            val deviceUuid = android.provider.Settings.Secure.getString(accountConfigActivity.contentResolver, android.provider.Settings.Secure.ANDROID_ID)
            accountConfigActivity.accountConfigHelper.saveDeviceValues(0, deviceUuid)
            Log.d("deviceUuid", "generateAndDisplayBarcode: $deviceUuid")

            if (deviceUuid.isNotBlank()) {
                // Generate and display barcode
               /* val barcodeBitmap = createBarcodeBitmap(
                    barcodeValue = deviceUuid,
                    barcodeColor = android.graphics.Color.BLACK,
                    backgroundColor = android.graphics.Color.WHITE,
                    widthPixels = 500,
                    heightPixels = 205
                )*/
                val barcodeBitmap=generateBarcode(deviceUuid)
                qrCodeImageView.setImageBitmap(barcodeBitmap)
            } else {
                Log.e("Barcode", "Empty device UUID for barcode generation")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("Barcode", "Error generating barcode: ${e.message}")
        }
    }

  /*  private fun generateBarcode(text: String): Bitmap? {
        return try {
            val barcodeEncoder = BarcodeEncoder()
            barcodeEncoder.encodeBitmap(text, BarcodeFormat.CODE_128, 400, 150)
        } catch (e: WriterException) {
            e.printStackTrace()
            null
        }
    }
*/
    private fun generateBarcode(text: String): Bitmap? {
        return try {
            val barcodeEncoder = BarcodeEncoder()
            val barcodeBitmap = barcodeEncoder.encodeBitmap(text, BarcodeFormat.CODE_128, 400, 150)

            // Create a new bitmap with space for the text
            val padding = 20
            val bitmapWithText = Bitmap.createBitmap(barcodeBitmap.width, barcodeBitmap.height + padding * 2, Bitmap.Config.ARGB_8888)

            // Draw the barcode and text onto the new bitmap
            val canvas = Canvas(bitmapWithText)
            canvas.drawColor(Color.WHITE)
            canvas.drawBitmap(barcodeBitmap, 0f, 0f, null)

            // Draw the text
            val paint = Paint()
            paint.color = Color.BLACK
            paint.textSize = 20f
            paint.textAlign = Paint.Align.CENTER
            paint.typeface = Typeface.DEFAULT_BOLD

            val textBounds = Rect()
            paint.getTextBounds(text, 0, text.length, textBounds)
            val x = barcodeBitmap.width / 2f
            val y = barcodeBitmap.height + padding + textBounds.height()

            canvas.drawText(text, x, y.toFloat(), paint)

            bitmapWithText
        } catch (e: WriterException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * Generate and display QR code for SNS flow (copied from IOMT flow)
     * This method replaces barcode generation when enableSnsApi is true
     */
    @SuppressLint("HardwareIds")
    private fun generateAndDisplayQRCode() {
        try {
            // Get device details
            val androidId = android.provider.Settings.Secure.getString(
                accountConfigActivity.contentResolver,
                android.provider.Settings.Secure.ANDROID_ID
            )
            val deviceUuid =  if (SettingDaoHelper.AppSettings.DeviceUuid.getValue().length!=36)  generateRandomUUID() else SettingDaoHelper.AppSettings.DeviceUuid.getValue()
            accountConfigActivity.accountConfigHelper.saveDeviceValues(0, deviceUuid)
            val devicePassPhrase =  if (SettingDaoHelper.AppSettings.DevicePass.getValue().length!=16) generateRandomPassphrase(16) else SettingDaoHelper.AppSettings.DevicePass.getValue()
            accountConfigActivity.accountConfigHelper.saveDeviceValues(1, devicePassPhrase)

            CommonDataArea.ANDROID_ID = androidId
            val osVersion = Build.VERSION.RELEASE
            val deviceConfig = Build.MANUFACTURER + " " + Build.MODEL

            val deviceDetails =
                "Android ID: $androidId\nOS Version: $osVersion \nDevice: $deviceConfig\nUUID: $deviceUuid\nPassphrase: $devicePassPhrase"
            /*val deviceDetails =
                "Android ID: $androidId\nOS Version: $osVersion\nDevice: $deviceConfig"*//*Old way of display Qr */
            if (deviceDetails.isNotBlank()) {
                // Generate and display QR code
                val encryptedDevice = encryptWithPublicKey(publicKey, deviceDetails)
                Log.d("deviceDetails", "generateAndDisplayQRCode: $deviceDetails")
                Log.d("deviceDetails", "generateAndDisplayQRCode: $encryptedDevice")
                val qrCodeBitmap: Bitmap? = generateQRCode(encryptedDevice)
                qrCodeImageView.setImageBitmap(qrCodeBitmap)

                // Notify the activity that QR code has been displayed
                accountConfigActivity.onQrCodeDisplayed()
            } else {
                Log.e("QRcode", "Empty device details for QR code generation")
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("QRcode", "Error generating QR code: ${e.message}")
        }
    }


    inner class UiEventHandler : UiEventCallback {
        @RequiresApi(Build.VERSION_CODES.P)
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            Log.d("SNS_NAVIGATION_DEBUG", "UiEventHandler.uiEvent: Received event $event")
            when (event) {
                UiEventCallback.UiEventType.NavigateToNext -> {
                    val hasAuthenticatedValue = SettingDaoHelper.AppSettings.HasAuthenticated.getValue()
                    val accountIdValue = SettingDaoHelper.AppSettings.AccountId.getValue()

                    Log.d("SNS_NAVIGATION_DEBUG", "NavigateToNext: HasAuthenticated = '$hasAuthenticatedValue'")
                    Log.d("SNS_NAVIGATION_DEBUG", "NavigateToNext: AccountId = '$accountIdValue'")

                    val targetActivity = if (hasAuthenticatedValue.isEmpty()) {
                        Log.d("SNS_NAVIGATION_DEBUG", "NavigateToNext: Staying on AccountConfigActivitySns (HasAuthenticated is empty)")
                        AccountConfigActivitySns::class.java
                    } else if (accountIdValue.isEmpty()) {
                        Log.d("SNS_NAVIGATION_DEBUG", "NavigateToNext: Going to QrScannerActivitySns (AccountId is empty)")
                        QrScannerActivitySns::class.java
                    } else {
                        Log.d("SNS_NAVIGATION_DEBUG", "NavigateToNext: Going to MainActivity (both values set)")
                        MainActivity::class.java
                    }

                    Log.d("SNS_NAVIGATION_DEBUG", "NavigateToNext: Target activity = ${targetActivity.simpleName}")
                    navigateToNextActivity(targetActivity)
                }

                else -> Log.d("UiEvent", "ACCOUNT_ACTIVITY_UNKNOWN EVENT -> $event")
            }
        }
    }

   /* inner class UiEventHandler: UiEventCallback {
        @RequiresApi(Build.VERSION_CODES.P)
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            when(event){
                UiEventCallback.UiEventType.NavigateToNext -> navigateToNextActivity(MainActivity::class.java)
                else -> Log.d("UiEvent", "SNS_ACCOUNT_ACTIVITY_UNKNOWN EVENT -> $event")
            }
        }
    }*/
}