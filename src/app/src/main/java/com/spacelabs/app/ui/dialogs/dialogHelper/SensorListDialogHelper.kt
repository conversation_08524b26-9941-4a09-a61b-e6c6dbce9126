package com.spacelabs.app.ui.dialogs.dialogHelper

import android.content.Context
import android.widget.Toast
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.iomt.apiHandler.SensorApiHandler
import com.spacelabs.app.iomt.apiHandler.interfaces.SensorAuthenticationCallback
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.sensorService
import com.spacelabs.app.ui.dialogs.ProgressDialogHandler

class SensorListDialogHelper(private val context: Context) {

    private val progressDialogHandler = ProgressDialogHandler(context)

    fun startScanWithSns() {
        if (CommonDataArea.chestSensorSelected.isNotEmpty()) {
            SibelSensorManager.enrollSensorNameByType(
                context,
                CommonDataArea.chestSensorSelected,
                SensorType.CHEST
            )
            CommonDataArea.chestSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
            CommonDataArea.chestSensorSelected = ""
        }
        if (CommonDataArea.limbSensorSelected.isNotEmpty()) {
            SibelSensorManager.enrollSensorNameByType(
                context,
                CommonDataArea.limbSensorSelected,
                SensorType.LIMB
            )
            CommonDataArea.limbSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
            CommonDataArea.limbSensorSelected = ""
        }
        if (CommonDataArea.bpSensorSelected.isNotEmpty()) {
            SibelSensorManager.enrollSensorNameByType(
                context,
                CommonDataArea.bpSensorSelected,
                SensorType.BP2_BLOOD_PRESSURE_MONITOR
            )
            CommonDataArea.bpSensorSelected = ""
        }
        sensorService.startScan(10000)
    }

     fun startScanWithIomt(){

        progressDialogHandler.showProgressDialog("Checking Sensor Authentication...")
        val sensorsToAuthenticate = mutableListOf<SensorType>()

        if (CommonDataArea.chestSensorSelected.isNotEmpty()) {
            sensorsToAuthenticate.add(SensorType.CHEST)
        }
        if (CommonDataArea.limbSensorSelected.isNotEmpty()) {
            sensorsToAuthenticate.add(SensorType.LIMB)
        }
        if (CommonDataArea.bpSensorSelected.isNotEmpty()) {
            sensorsToAuthenticate.add(SensorType.BP2_BLOOD_PRESSURE_MONITOR)
        }

        authenticateAndStartScan( sensorsToAuthenticate)
    }

    private fun authenticateAndStartScan(sensorsToAuthenticate: List<SensorType>) {


        var authenticatedSensorsCount = 0

        sensorsToAuthenticate.forEach { sensorType ->
            val sensorApiHandler = SensorApiHandler()

            val sensorSelected = when (sensorType) {
                SensorType.CHEST -> CommonDataArea.chestSensorSelected
                SensorType.LIMB -> CommonDataArea.limbSensorSelected
                SensorType.BP2_BLOOD_PRESSURE_MONITOR -> CommonDataArea.bpSensorSelected
                else -> return@forEach // or handle this case according to your logic
            }
            sensorApiHandler.checkSensorAuthentication(sensorSelected, getSensorAddressByType(sensorType), object :
                SensorAuthenticationCallback {
                override fun onAuthenticationResult(sensorName: String, isSuccess: Boolean, message: String) {

                    if (isSuccess) {
                        if (!message.contains("Allowing Connection")) {
                            showToast(message)
                        }
                        if (message.contains("$sensorName Sensor Not Registered - Connection not allowed")) {
                            sensorService.stopScan()
                        }
                        when (sensorType) {
                            SensorType.CHEST -> {
                                CommonDataArea.chestSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
                                CommonDataArea.chestSensorSelected = ""
                                SibelSensorManager.enrollSensorNameByType(context, sensorName, SensorType.CHEST)


                            }
                            SensorType.LIMB -> {
                                CommonDataArea.limbSensorConnectingStatus = SibelSensorManager.SensorScanMode.New
                                CommonDataArea.limbSensorSelected = ""
                                SibelSensorManager.enrollSensorNameByType(context, sensorName, SensorType.LIMB)

                            }
                            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> {
                                CommonDataArea.bpSensorSelected = ""
                                SibelSensorManager.enrollSensorNameByType(context, sensorName, SensorType.BP2_BLOOD_PRESSURE_MONITOR)

                            }
                            else -> TODO()

                        }
                        authenticatedSensorsCount++

                        if (authenticatedSensorsCount == sensorsToAuthenticate.size) {
                            startScan(context)
                            progressDialogHandler.dismissProgressDialog()
                        }
                    } else {
                        startScan(context)
                        progressDialogHandler.dismissProgressDialog()
                        showToast(message)
                        when (sensorType) {
                            SensorType.CHEST -> {
                                CommonDataArea.chestSensorSelected = ""
                            }

                            SensorType.LIMB -> {
                                CommonDataArea.limbSensorSelected = ""
                            }

                            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> {
                                CommonDataArea.bpSensorSelected = ""
                            }

                            else -> TODO()
                        }
                    }
                }
            })
        }
    }

    private fun startScan(context: Context) {
        // Start the scan logic here
        sensorService.startScan(10000)
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }

    private fun getSensorAddressByType(sensorType: SensorType): String {
        return when (sensorType) {
            SensorType.CHEST -> CommonDataArea.chestSensorAddress
            SensorType.LIMB -> CommonDataArea.limbSensorAddress
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> CommonDataArea.bpSensorAddress
            // Add branches for other SensorType values
            else -> TODO()
        }
    }
}