package com.spacelabs.app.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblMeasurementData")
data class MeasurementDataTable(
    @PrimaryKey(autoGenerate = true)
    val measurementId: Int?,
    @ColumnInfo(defaultValue = "")
    val measurementUuid: String,
    val visitId: Int,
    val arrayForWebSocket: FloatArray?,
    val cmsId: String?,
    val sensorId: Int?,
    val paramId: Int,
    val patientID1: String?,
    val valueType: String?,
    val paramName: String,
    val value: Double?,
    val measurementData: ByteArray?,
    val numberOfSamples: Int?,
    val timestamp: String,
    @ColumnInfo(defaultValue = 0.toString())
    var uploadStatus: Int?,
    var uploadTimestamp: String?,
    @ColumnInfo(defaultValue = 0.toString())
    var retryCount: Int?,
    var tickValue: Long?,
    val slno: Long,
    var sentToWebSocket: Boolean = false
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MeasurementDataTable

        if (measurementId != other.measurementId) return false
        if (visitId != other.visitId) return false
        if (!arrayForWebSocket.contentEquals(other.arrayForWebSocket)) return false
        if (!cmsId.contentEquals(other.cmsId)) return false
        if (sensorId != other.sensorId) return false
        if (paramId != other.paramId) return false
        if (patientID1 != other.patientID1) return false
        if (valueType != other.valueType) return false
        if (paramName != other.paramName) return false
        if (value != other.value) return false
        if (measurementData != null) {
            if (other.measurementData == null) return false
            if (!measurementData.contentEquals(other.measurementData)) return false
        } else if (other.measurementData != null) return false
        if (numberOfSamples != other.numberOfSamples) return false
        if (timestamp != other.timestamp) return false
        if (uploadStatus != other.uploadStatus) return false
        if (uploadTimestamp != other.uploadTimestamp) return false
        if(tickValue!=other.tickValue)return false

        return true
    }

    override fun hashCode(): Int {
        var result = measurementId ?: 0
        result = 31 * result + visitId
        result = 31 * result + (arrayForWebSocket.hashCode() ?:0)
        result = 31 * result + (cmsId.hashCode() ?:0)
        result = 31 * result + (sensorId ?: 0)
        result = 31 * result + paramId
        result = 31 * result + (patientID1?.hashCode() ?: 0)
        result = 31 * result + (valueType?.hashCode() ?: 0)
        result = 31 * result + paramName.hashCode()
        result = 31 * result + (value?.hashCode() ?: 0)
        result = 31 * result + (measurementData?.contentHashCode() ?: 0)
        result = 31 * result + (numberOfSamples ?: 0)
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + (uploadStatus ?: 0)
        result = 31 * result + (uploadTimestamp?.hashCode() ?: 0)
        return result
    }
}