package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.encryption.EncryptionConverter
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter

class UnsendMeasureDataAdapter(private var measureList: List<MeasurementData> = emptyList()) : RecyclerView.Adapter<UnsendMeasureDataAdapter.PatientViewHolder>() {
    private val encryptionConverter = EncryptionConverter()
    private val encryptionVitalConverter = EncryptionVitalConverter()
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_unsend_measurement_data, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = measureList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return measureList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        private val measurementId: TextView = itemView.findViewById(R.id.MeasurementId)
        private val uuid: TextView = itemView.findViewById(R.id.uuid)
        private val visitId: TextView = itemView.findViewById(R.id.VisitId)
        private val sensorId: TextView = itemView.findViewById(R.id.SensorId)
        private val paramId: TextView = itemView.findViewById(R.id.ParamId)
        private val patientID1: TextView = itemView.findViewById(R.id.PatientID1)
        private val valueType: TextView = itemView.findViewById(R.id.ValueType)
        private val paramName: TextView = itemView.findViewById(R.id.ParamName)
        private val value: TextView = itemView.findViewById(R.id.Value)
        private val measurementData: TextView = itemView.findViewById(R.id.MeasurementData)
        private val numberOfSample: TextView = itemView.findViewById(R.id.NumberOfSample)
        private val timeStamp: TextView = itemView.findViewById(R.id.TimeStamp)
        private val uploadStatus: TextView = itemView.findViewById(R.id.UploadStatus)
        private val uploadTimeStamp: TextView = itemView.findViewById(R.id.UploadTimeStamp)
        private val retryCount: TextView = itemView.findViewById(R.id.retryCount)
        private val tickValue:TextView=itemView.findViewById(R.id.TickValue)

        fun bind(measureData: MeasurementData) {
            measurementId.text = measureData.measurementId.toString()
            uuid.text = measureData.measurementUuid
            visitId.text = measureData.visitId.toString()
            sensorId.text = measureData.sensorId.toString()
            paramId.text = String()
            patientID1.text = measureData.patientID1
            valueType.text = measureData.valueType
            paramName.text = measureData.paramName
            value.text = if (measureData.value==null)(measureData.value).toString() else encryptionVitalConverter.convertToEntityProperty(measureData.value).toString()
            measurementData.text = measureData.measurementData.toString()
            numberOfSample.text = measureData.numberOfSamples.toString()
            timeStamp.text = measureData.timestamp
            uploadStatus.text = measureData.uploadStatus.toString()
            uploadTimeStamp.text = measureData.uploadTimestamp
            retryCount.text = measureData.retryCount.toString()
            tickValue.text=measureData.tickValue.toString()
        }
    }
}

