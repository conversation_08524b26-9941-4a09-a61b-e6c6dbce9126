package com.spacelabs.app.database.dao

import androidx.room.*
import com.spacelabs.app.database.entities.EventListTable
import com.spacelabs.app.database.entities.EventsLogTable

@Dao
interface EventsDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEvent(event: EventListTable): Long?

    @Transaction
    @Query("SELECT * FROM tblEventList")
    suspend fun getAllEvents(): List<EventListTable>?

    @Transaction
    @Query("SELECT * FROM tblEventList ORDER BY eventId DESC")
    suspend fun getAllEventsDescOrder(): List<EventListTable>

    @Transaction
    @Query("SELECT * FROM tblEventList WHERE eventName = :eventName")
    suspend fun getEventByEventName(eventName: String): EventListTable?

    @Transaction
    @Query("SELECT * FROM tblEventsLog ORDER BY eventLogId DESC")
    suspend fun getAllEventLog(): List<EventsLogTable>

    @Query("DELETE FROM tblEventsLog")
    suspend fun deleteAllEventLog(): Int

    @Query("DELETE FROM tblEventList")
    suspend fun deleteEventList():Int
}