package com.spacelabs.app.alarms

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import android.util.Log
import com.google.android.gms.common.internal.service.Common
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.interfaces.AlarmEventCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

class SibelAlarmManager(private val context: Context) {

    private val audioManager: AudioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager

    companion object {
        const val CRITICAL = 500
        const val HIGH = 400
        const val MEDIUM = 300
        const val LOW = 200
        const val NO_ALARM = 0

        var pauseAlarms = false
        private val paramsAndAlarms = HashMap<String, AlarmParametersDao>()

        private val alarmCoroutine = CoroutineScope(Dispatchers.IO + Job())
        private val audioCoroutine = CoroutineScope(Dispatchers.IO)

        private var currentCriticality = NO_ALARM
    }

    private fun getHighAlarmParameter(): AlarmParametersDao? {
        var highestCriticality = NO_ALARM
        var alarmParam: AlarmParametersDao? = null
        val alarms = paramsAndAlarms.values
        alarms.removeIf { !it.alarmStatus }
        for(alarm in alarms) {
            if(!alarm.alarmStatus) {
                paramsAndAlarms.remove(alarm.paramName)
                continue
            }


            if(alarm.isAcknowledged)
                continue

            if(alarm.alarmCriticality > highestCriticality) {
                highestCriticality = alarm.alarmCriticality
                alarmParam = alarm
            }
        }
        return alarmParam
    }

    private var playerJob: Job? = null

    private fun startPlayAlarmsThread() {
        val alarmParamsDao = getHighAlarmParameter()
        if(alarmParamsDao == null || pauseAlarms){
            currentCriticality = NO_ALARM
            playerJob?.cancel()
            return
        }
        if(alarmParamsDao.alarmCriticality != currentCriticality ) {
            playerJob?.cancel()
            currentCriticality = alarmParamsDao.alarmCriticality
            if(isPlaying())
                CommonDataArea.mediaPlayer?.stop()

            CommonDataArea.mediaPlayer = createMediaPLayer(alarmParamsDao.alarmTone)
            playerJob = audioCoroutine.launch {
                while(currentCriticality == alarmParamsDao.alarmCriticality && alarmParamsDao.alarmStatus  ) {
                    if(!paramsAndAlarms.containsValue(alarmParamsDao))
                        break

                    startMediaPlayer(alarmParamsDao)
                    CommonDataArea.CriticalBatteryAlarmDao.alarmInterval=50
                    CommonDataArea.CriticalDeviceBatteryAlarmDao.alarmInterval=60
                    delay(alarmParamsDao.alarmInterval * 1000)
                }
                if(!alarmParamsDao.alarmStatus) {
                    currentCriticality = NO_ALARM
                    playerJob?.cancel()
                }
            }
        }
    }

    private fun startMediaPlayer(alarmParamsDao: AlarmParametersDao){
        if(pauseAlarms) return

        val alarmVolume = getAlarmVolume()
        val mediaPlayer = CommonDataArea.mediaPlayer ?: createMediaPLayer(alarmParamsDao.alarmTone)
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, alarmVolume, 0)
        mediaPlayer.start()
    }

    private fun getAlarmVolume(): Int {
        return when(currentCriticality) {
            CRITICAL -> CommonDataArea.criticalVolume
            HIGH -> CommonDataArea.highVolume
            MEDIUM -> CommonDataArea.mediumVolume
            LOW -> CommonDataArea.lowVolume
            else -> 0
        }
    }

    private fun isPlaying() :Boolean {
        return CommonDataArea.mediaPlayer?.isPlaying ?: false
    }

    private fun createMediaPLayer(resource: Int): MediaPlayer {
        resource.let {
            if (it == 0) {
                Log.d("CreateMediaPLayer:", "Resource not found")
            }
            return MediaPlayer.create(context, it)
        }
    }

    private fun checkForAlarm(parameter: CurrentAlarmParameter, value: Double){
        try{
            when(parameter){
                CurrentAlarmParameter.Ecg -> checkForEcgAlarm(CommonDataArea.hrAlarmDao, value)
                CurrentAlarmParameter.BpSys, CurrentAlarmParameter.BpDia -> checkForBpAlarm(parameter, value)
                CurrentAlarmParameter.Fall -> checkForFallAlarm(CommonDataArea.fallAlarmDao)
                CurrentAlarmParameter.Spo2 -> checkForSpo2Alarms(CommonDataArea.spo2AlarmDao, value)
                CurrentAlarmParameter.Resp -> checkForRespAlarms(CommonDataArea.respAlarmDao, value)
                CurrentAlarmParameter.Temp -> checkForTempAlarm(CommonDataArea.tempAlarmDao, value)
                CurrentAlarmParameter.ConnectivityAlarm -> checkForConnectivityAlarms(CommonDataArea.connectivityAlarmDao)
                CurrentAlarmParameter.CriticalBatteryAlarm ->checkForCriticalBatteryAlarm(CommonDataArea.CriticalBatteryAlarmDao)
                CurrentAlarmParameter.CriticalDeviceBatteryAlarm ->checkForCriticalDeviceBatteryAlarm(CommonDataArea.CriticalDeviceBatteryAlarmDao)
                else -> null
            } ?: return
            startPlayAlarmsThread()
        }catch (ex: Exception){
            LogWriter.writeExceptLog("start alarm", "${ex.printStackTrace()}")
        }
    }

    private fun getBpAlarmDao(currentParameter: CurrentAlarmParameter): AlarmParametersDao {
        return if(currentParameter == CurrentAlarmParameter.BpSys)
            CommonDataArea.bpSysAlarmDao
        else
            CommonDataArea.bpDiaAlarmDao
    }

    private fun checkForFallAlarm(alarmParamsDao: AlarmParametersDao){
        if(!alarmParamsDao.isAttached)
            setAlarmProperty(alarmParamsDao, CurrentAlarmParameter.HighAlarm)
        else
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
    }

    private fun checkForConnectivityAlarms(alarmParamsDao: AlarmParametersDao){
        if(!alarmParamsDao.isAttached)
            setAlarmProperty(alarmParamsDao, CurrentAlarmParameter.ConnectivityAlarm)
        else{
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }
    private fun checkForCriticalBatteryAlarm(alarmParamsDao: AlarmParametersDao){
        if(!alarmParamsDao.isAttached)
            setAlarmProperty(alarmParamsDao, CurrentAlarmParameter.CriticalBatteryAlarm)
        else{
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }
    private fun checkForCriticalDeviceBatteryAlarm(alarmParamsDao: AlarmParametersDao){
        if(!alarmParamsDao.isAttached)
            setAlarmProperty(alarmParamsDao, CurrentAlarmParameter.CriticalDeviceBatteryAlarm)
        else{
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }

    private fun checkForEcgAlarm(alarmParamsDao: AlarmParametersDao, paramValue: Double){
        val intValue = paramValue.toInt()
        val currentAlarmParameter = when {
            intValue >= alarmParamsDao.extremeHighValue -> CurrentAlarmParameter.HighExtremeAlarm
            intValue >= alarmParamsDao.highValue -> CurrentAlarmParameter.HighHighAlarm
            intValue <= alarmParamsDao.lowValue && intValue > alarmParamsDao.extremeLowValue -> CurrentAlarmParameter.LowHighAlarm
            intValue <= alarmParamsDao.extremeLowValue && intValue > 0 -> CurrentAlarmParameter.LowExtremeAlarm
            else -> null
        }

        if(currentAlarmParameter != null && intValue != 0)
            setAlarmProperty(alarmParamsDao, currentAlarmParameter)
        else{
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }

    private fun checkForRespAlarms(alarmParamsDao: AlarmParametersDao, paramValue: Double){
        val intValue = paramValue.toInt()
        val currentAlarmParameter = when {
            intValue >= alarmParamsDao.highValue -> CurrentAlarmParameter.HighMediumAlarm
            intValue <= alarmParamsDao.lowValue && paramValue > 0 -> CurrentAlarmParameter.LowMediumAlarm
            else -> null
        }

        if(currentAlarmParameter != null && intValue != 0)
            setAlarmProperty(alarmParamsDao, currentAlarmParameter)
        else{
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }

    private fun checkForSpo2Alarms(alarmParamsDao: AlarmParametersDao, paramValue: Double){
        val intValue = paramValue.toInt()
        val currentAlarmParameter = when {
            intValue >= alarmParamsDao.highValue -> CurrentAlarmParameter.HighHighAlarm
            intValue <= alarmParamsDao.lowValue && paramValue > 0 -> CurrentAlarmParameter.LowMediumAlarm
            else -> null
        }

        if(currentAlarmParameter != null && intValue != 0)
            setAlarmProperty(alarmParamsDao, currentAlarmParameter)
        else{
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }

    private fun checkForTempAlarm(alarmParamsDao: AlarmParametersDao, paramValue: Double){
        if (paramValue.isNaN()) {
            // Handle the NaN value appropriately, perhaps log an error or set a default value
            // For example, log an error and return to avoid further processing
            Log.e("SibelAlarmManager", "paramValue is NaN $paramValue, cannot proceed with checkForTempAlarm")
            return
        }
        val intValue = paramValue.roundToInt()
        val currentAlarmParameter = when {
            intValue >= alarmParamsDao.highValue -> CurrentAlarmParameter.HighLowAlarm
            intValue <= alarmParamsDao.lowValue && paramValue > 0 -> CurrentAlarmParameter.LowLowAlarm
            else -> null
        }
        if(currentAlarmParameter != null && intValue != 0) {
            setAlarmProperty(alarmParamsDao, currentAlarmParameter)
        }
        else{
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }

    private fun checkForBpAlarm(parameter: CurrentAlarmParameter, paramValue: Double){
        val alarmParamsDao = getBpAlarmDao(parameter)
        val intValue = if(paramValue.isNaN()) 0 else paramValue.toInt()
        val currentAlarmParameter = when {
            intValue >= alarmParamsDao.highValue -> CurrentAlarmParameter.HighHighAlarm
            intValue <= alarmParamsDao.lowValue && paramValue > 0 -> CurrentAlarmParameter.LowHighAlarm
            else -> null
        }
        if(currentAlarmParameter != null && intValue != 0)
            setAlarmProperty(alarmParamsDao, currentAlarmParameter)
        else {
            changeCurrentAlarmValueOnPriorityChange(alarmParamsDao)
        }
    }

    private fun setAlarmProperty(alarmParamsDao: AlarmParametersDao, currentParameter: CurrentAlarmParameter){
        alarmParamsDao.setAlarmParameter(currentParameter)
        alarmStartingAndPropertySetting(alarmParamsDao)
    }

    private fun changeCurrentAlarmValueOnPriorityChange(alarmParam: AlarmParametersDao){
        if (alarmParam.isBlinking) {
            paramsAndAlarms.remove(alarmParam.paramName)
            alarmParam.setAlarmParameter(CurrentAlarmParameter.NoAlarm)
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.AlarmStateChange, alarmParam)
        }
    }

    private fun alarmStartingAndPropertySetting(alarmParamsDao: AlarmParametersDao) {
        val paramName = alarmParamsDao.paramName
        if(!alarmParamsDao.isAcknowledged && !paramsAndAlarms.contains(paramName))
            paramsAndAlarms[paramName] = alarmParamsDao
        else if(alarmParamsDao.isAcknowledged)
            paramsAndAlarms.remove(alarmParamsDao.paramName)

        CommonEventHandler.postUiEvent(
            UiEventCallback.UiEventType.AlarmStateChange,
            alarmParamsDao
        )
    }

    private fun onReceiveEndBpAlarm() {
        paramsAndAlarms.remove(CommonDataArea.bpSysAlarmDao.paramName)
        paramsAndAlarms.remove(CommonDataArea.bpDiaAlarmDao.paramName)
    }

    inner class AlarmEventHandler: AlarmEventCallback {
        override fun alarmEvent(param: CurrentAlarmParameter, eventData: Any?) {
            alarmCoroutine.launch {
                val data = eventData ?: 0.0
                when(param) {
                    CurrentAlarmParameter.EndBpAlarms -> onReceiveEndBpAlarm()
                    else -> checkForAlarm(param, data as Double)
                }
            }
        }
    }
}