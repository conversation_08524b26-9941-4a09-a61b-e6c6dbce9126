package com.spacelabs.app.activities.snsActivities

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.util.Size
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.OptIn
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.common.InputImage
import com.spacelabs.app.R
import com.spacelabs.app.activities.snsActivities.activityUi.QrCodeActivityUiSns
import com.spacelabs.app.activities.snsActivities.helpers.CameraActivity
import com.spacelabs.app.activities.snsActivities.helpers.QrCodeAnalyzer
import com.spacelabs.app.activities.snsActivities.helpers.QrCodeConfigHelperSns
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.iomt.NetworkUtils
import com.spacelabs.app.ui.dialogs.ProgressDialogHandler
import java.util.concurrent.Executors

class QrScannerActivitySns : AppCompatActivity() {

    private val TAG = "Qr_Scanner_Activity_Sns"
    private lateinit var btnScan: Button
    private lateinit var uuidTextView: TextView

    private lateinit var content_layout: LinearLayout
    private lateinit var footer_layout: LinearLayout
    private lateinit var header_layout: LinearLayout

    private val qrConfigHelper = QrCodeConfigHelperSns(this)
    private val qrCodeActivityUi = QrCodeActivityUiSns(this)
    private val progressDialogHandler = ProgressDialogHandler(this)
    private lateinit var qrCodeAnalyzer: QrCodeAnalyzer

    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startCameraForResult()
        } else {
            Toast.makeText(this, "Camera permission is required", Toast.LENGTH_SHORT).show()
        }
    }
    private val scanResultLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            handleActivityResult(result.resultCode, result.data)
        }
    @SuppressLint("MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_qr_scanner_sns)
        uuidTextView = findViewById(R.id.uuidEditText)
        btnScan = findViewById(R.id.btnScan)
        header_layout = findViewById(R.id.header_layout)
        content_layout = findViewById(R.id.content_layout)
        footer_layout = findViewById(R.id.footer_layout)



        uiActionListeners()

    }
    fun uiActionListeners() {
        // qrCodeAnalyzer = QrCodeAnalyzer(this,qrConfigHelper,progressDialogHandler)/*Old way of scanning and handle data */
        qrCodeAnalyzer = QrCodeAnalyzer(this, qrConfigHelper, progressDialogHandler)

        handleUUIDVisibility()

        btnScan.setOnClickListener {
            header_layout.visibility = View.GONE
            content_layout.visibility = View.GONE
            footer_layout.visibility = View.GONE
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            } else {
                startCameraForResult()
            }
        }

    }



    private fun startCameraForResult() {
        val intent = Intent(this, CameraActivity::class.java)
        scanResultLauncher.launch(intent)
    }




    private fun handleActivityResult(resultCode: Int, data: Intent?) {
        if (resultCode == RESULT_OK && data != null) {
            val result = data.getStringExtra("SCAN_RESULT")
            if (result != null) {
                Log.d(TAG, "Scanned QR Code content: $result")
                progressDialogHandler.showProgressDialog("Loading...")
                qrCodeAnalyzer.handleScannedData(result)
            } else {
                Toast.makeText(this, "Cancelled", Toast.LENGTH_SHORT).show()
            }
        } else if (resultCode == RESULT_CANCELED) {
            // Camera activity was cancelled or failed
            Log.w(TAG, "Camera activity cancelled or failed")
            Toast.makeText(this, "Camera not available. Please configure cameras in emulator AVD settings.", Toast.LENGTH_LONG).show()

            // Show UI elements again since scan failed
            header_layout.visibility = View.VISIBLE
            content_layout.visibility = View.VISIBLE
            footer_layout.visibility = View.VISIBLE
        }
    }

  /*  private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        cameraProviderFuture.addListener(Runnable {
            val cameraProvider = cameraProviderFuture.get()
            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }

            val imageAnalysis = ImageAnalysis.Builder()
                .setTargetResolution(Size(1280, 720))
                .build()
                .also {
                    it.setAnalyzer(Executors.newSingleThreadExecutor(), BarcodeAnalyzer())
                }

            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

            try {
                cameraProvider.unbindAll()
                cameraProvider.bindToLifecycle(
                    this as LifecycleOwner, cameraSelector, preview, imageAnalysis)
            } catch (e: Exception) {
                Log.e(TAG, "Use case binding failed", e)
            }
        }, ContextCompat.getMainExecutor(this))
    }

    private inner class BarcodeAnalyzer : ImageAnalysis.Analyzer {
        @OptIn(ExperimentalGetImage::class)
        override fun analyze(imageProxy: ImageProxy) {
            val mediaImage = imageProxy.image
            if (mediaImage != null) {
                val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
                val scanner: BarcodeScanner = BarcodeScanning.getClient()
                scanner.process(image)
                    .addOnSuccessListener { barcodes ->
                        for (barcode in barcodes) {
                            val rawValue = barcode.rawValue
                            val displayValue = barcode.displayValue
                           // uuidTextView.text = displayValue ?: rawValue
                            Log.d(TAG, "Barcode detected: $rawValue")
                            (rawValue ?: displayValue)?.let { qrCodeAnalyzer.handleScannedData(it) }
                        }
                    }
                    .addOnFailureListener { e ->
                        Log.e(TAG, "Barcode scanning failed", e)
                    }
                    .addOnCompleteListener {
                        imageProxy.close()
                    }
            }
        }
    }*/

    private fun handleUUIDVisibility() {
        val deviceUuid = SettingDaoHelper.AppSettings.DeviceUuid.getValue()
        val uuidParts = deviceUuid.split("-")
        val firstPart = uuidParts.first().take(4) + "x".repeat(uuidParts.first().length - 4)
        val lastPart = "x".repeat(uuidParts.last().length - 6) + uuidParts.last().takeLast(6)
        // Replace inner parts with 'x'
        val modifiedParts = uuidParts.mapIndexed { index, part ->
            when (index) {
                0 -> firstPart
                uuidParts.size - 1 -> lastPart
                else -> "x".repeat(part.length)
            }
        }
        val modifiedUuid = modifiedParts.joinToString("-")
        // Display the modified UUID
        uuidTextView.text = modifiedUuid
    }

    override fun onStart() {
        super.onStart()
        CommonEventHandler.registerUiEventCallback(
            qrCodeActivityUi.UiEventHandler(),
            this::class.java
        )    }

    override fun onResume() {
        super.onResume()
        qrConfigHelper.navigateIfAccountIdExists()

    }

    override fun onStop() {
        super.onStop()
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)

    }

    private fun showToast(message: String) {
        Toast.makeText(applicationContext, message, Toast.LENGTH_SHORT).show()
    }



}
