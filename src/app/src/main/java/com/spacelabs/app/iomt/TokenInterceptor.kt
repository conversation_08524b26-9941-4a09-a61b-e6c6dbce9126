package com.spacelabs.app.iomt

import android.util.Log
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.iomt.apiHandler.interfaces.TokenCallback
import okhttp3.Interceptor
import okhttp3.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

class TokenInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val originalResponse = chain.proceed(originalRequest)

        try {
            if (originalResponse.code == 401 || originalResponse.code == 403) { // Token is expired
                synchronized(this) {
                    val latch = CountDownLatch(1)
                    var newToken: String? = null

                    val iomtApiManager = IomtApiManager()
                    iomtApiManager.getTokenByOauthLogin(object : TokenCallback {
                        override fun onTokenReceived(success: Boolean, token: String, message: String) {
                            if (success && IomtApiManager.isTokenSaved && token.isNotEmpty()) {
                                newToken = token
                                RetrofitClient.setApiToken(token)
                                CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.ApiTokenUpdate, token)
                                // Save token to SettingDaoHelper
                            } else {
                                Log.d("Token Error", "onTokenReceived: $message")
                            }
                            latch.countDown()
                        }
                    })

                    latch.await(30, TimeUnit.SECONDS) // Wait for the token to be received

                    newToken?.let {
                        val newRequest = originalRequest.newBuilder()
                            .removeHeader("Authorization")
                            .addHeader("Authorization", "Bearer $it")
                            .build()
                        originalResponse.close() // Close the original response before making a new request
                        return chain.proceed(newRequest)
                    }
                }
            }
            return originalResponse
        } catch (e: Exception) {
            originalResponse.close() // Ensure the response is closed in case of an exception
            throw e
        }
    }
}
