package com.spacelabs.app.ui.dialogs

import android.app.AlertDialog
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.WindowManager.LayoutParams
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import com.spacelabs.app.R
import com.spacelabs.app.common.CommonDataArea



class AlertConfirmDialogs(private val context: Context) {

    private val handler: Handler = Handler(Looper.getMainLooper())

    fun getAlertDialog(title: String, message: String, dismissDelayMillis: Long) {
        val currentActivity = CommonDataArea.curActivity
        val builder = AlertDialog.Builder(context)
        val customLayout = LayoutInflater.from(context).inflate(
            R.layout.custom_alert_dialog,
            null
        )

        // Set the title and message in the custom layout
        val titleView = customLayout.findViewById<TextView>(R.id.alertTitle)
        val messageView = customLayout.findViewById<TextView>(R.id.alertMessage)
        val okButton = customLayout.findViewById<AppCompatButton>(R.id.alertOkButton)
        val closeButton=customLayout.findViewById<Button>(R.id.closeBtn)
        titleView.text = title
        messageView.text = message

        val alert = builder.create()
        alert.setView(customLayout)

        okButton.setOnClickListener {
            CommonDataArea.isAlertShowing = false
            alert.dismiss()
        }
        closeButton.setOnClickListener{
            CommonDataArea.isAlertShowing = false
            alert.dismiss()
        }

        if (!CommonDataArea.isAlertShowing) {
            alert.show()
            CommonDataArea.isAlertShowing = true
            handler.postDelayed({
                if (alert.isShowing) {
                    CommonDataArea.isAlertShowing = false
                    alert.dismiss()
                    handler.removeCallbacksAndMessages(null)
                }
            }, dismissDelayMillis)
        }

        alert.window?.setLayout(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        alert.window?.setBackgroundDrawableResource(android.R.color.transparent)
        alert.setCancelable(false)
    }
}