package com.spacelabs.app.database.objectbox.dao

import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.dataManager.ObjectBoxManager
import com.spacelabs.app.database.objectbox.boxes.AlarmData
import com.spacelabs.app.database.objectbox.boxes.AlarmData_
import com.spacelabs.app.database.objectbox.boxes.AlarmEventData
import com.spacelabs.app.database.objectbox.boxes.AlarmEventData_
import com.spacelabs.app.database.objectbox.boxes.MeasurementData_
import io.objectbox.kotlin.notEqual
import io.objectbox.query.OrderFlags
import io.objectbox.query.QueryBuilder

class AlarmBoxDao {

    fun insertOrUpdateAlarmData(alarmData: AlarmData): Long {
        val box = ObjectBoxManager.getAlarmBox()
        return box.put(alarmData)
    }

    fun insertOrUpdateAlarmEventData(alarmEvent: AlarmEventData): Long {
        val box = ObjectBoxManager.getAlarmEventBox()
        return box.put(alarmEvent)
    }

    fun getAlarmById(alarmId: Long): AlarmData {
        return ObjectBoxManager.getAlarmBox().get(alarmId)
    }

    fun getAlarmEventByUuId(uuId: String): AlarmEventData? {
        return ObjectBoxManager.getAlarmEventBox().query()
            .equal(AlarmEventData_.alarmEventUUID, uuId, QueryBuilder.StringOrder.CASE_INSENSITIVE)
            .build()
            .findFirst()
    }

    fun getLastEnteredAlarmIdByAlarmName(alarmName: String): Long {
        return ObjectBoxManager.getAlarmBox().query()
            .equal(AlarmData_.alarmName, alarmName, QueryBuilder.StringOrder.CASE_INSENSITIVE)
            .build()
            .property(AlarmData_.alarmId)
            .max()
    }

    fun getLastEnteredEventValueByAlarmId(alarmId: Long): Double {
        return ObjectBoxManager.getAlarmEventBox().query()
            .equal(AlarmEventData_.alarmId, alarmId)
            .orderDesc(AlarmEventData_.alarmEventId)
            .build()
            .property(AlarmEventData_.value)
            .findDouble()
    }

    fun getLastEnteredAlarmByAlarmName(alarmName: String): AlarmData? {
        return ObjectBoxManager.getAlarmBox().query()
            .equal(AlarmData_.alarmName, alarmName, QueryBuilder.StringOrder.CASE_INSENSITIVE)
            .orderDesc(AlarmData_.alarmId)
            .build()
            .findFirst()
    }

    fun getAllAlarmsOfPatient(patientId: Long): List<AlarmData> {
        return ObjectBoxManager.getAlarmBox().query()
            .equal(AlarmData_.patientId, patientId)
            .order(AlarmData_.alarmId, OrderFlags.DESCENDING)
            .build()
            .find()
    }

    fun getAllAlarmEventOfPatient(): List<AlarmEventData> {
        return ObjectBoxManager.getAlarmEventBox().query()
            .order(AlarmEventData_.alarmEventId, OrderFlags.DESCENDING)
            .build()
            .find()
    }

    fun getAlarmsAndEventsForPatientWithPendingUpload(): List<AlarmDataWithEvents> {
        val dataWithIn = 60 * 60 * 1000
        val alarms = ObjectBoxManager.getAlarmBox().query()
            .equal(AlarmData_.patientId, CommonDataArea.PATIENT.patientId)
            .equal(AlarmData_.uploadStatus, 0)
            .greater(AlarmData_.uploadTimeMillis, TimestampUtils.getCurrentTimeMillis() - dataWithIn)
            .build()
            .find()

        val alarmsWithEvents = mutableListOf<AlarmDataWithEvents>()

        for (alarm in alarms) {
            val events = ObjectBoxManager.getAlarmEventBox().query()
                .equal(AlarmEventData_.alarmId, alarm.alarmId!!)
                .equal(AlarmEventData_.uploadStatus, 0)
                .build()
                .find()

            alarmsWithEvents.add(AlarmDataWithEvents(alarm, events))
        }

        return alarmsWithEvents
    }
    fun deleteAlarmEventOnDischarge() {
        val alarmBox = ObjectBoxManager.getAlarmEventBox()

        // Find entities with uploadStatus 1
        val queryToDelete = alarmBox.query().greaterOrEqual(AlarmEventData_.uploadStatus, 1).build()
        val entitiesToDelete = queryToDelete.find()

        // Remove entities with uploadStatus 1
        alarmBox.remove(entitiesToDelete)
        queryToDelete.close()

    }

    fun deleteAlarmEventData(tickValue: Long) {
        val measurementBox = ObjectBoxManager.getAlarmEventBox()

        // Find entities with uploadStatus 1 and tick value should be less than equal to the given value
        val queryToDelete =
            measurementBox.query().lessOrEqual(AlarmEventData_.tickValue, tickValue).and()
                .greaterOrEqual(AlarmEventData_.uploadStatus, 1).build()
        val entitiesToDelete = queryToDelete.find()

        // Remove entities with uploadStatus 1 and tick value should be less than equal to the given value
        measurementBox.remove(entitiesToDelete)
        queryToDelete.close()
    }

    fun updateAlarmEventUploadStatus(alarmEvent: AlarmEventData, status: Int) {
        if(alarmEvent.alarmStatus.equals("end", true))
            updateStatusAsUploadedByAlarmId(alarmEvent.alarmId, status)

        insertOrUpdateAlarmEventData(alarmEvent)
    }
    fun deleteAlarmOnDischarge() {
        val alarmBox = ObjectBoxManager.getAlarmBox()

        // Find entities with uploadStatus 1
        val queryToDelete = alarmBox.query().greaterOrEqual(AlarmData_.uploadStatus, 1).build()
        val entitiesToDelete = queryToDelete.find()

        // Remove entities with uploadStatus 1
        alarmBox.remove(entitiesToDelete)
        queryToDelete.close()

    }
    fun deleteAlarmData(patientId: Int) {
        val alarmBox = ObjectBoxManager.getAlarmBox()

        val queryToDelete =
            alarmBox.query().notEqual(AlarmData_.patientId, patientId).and()
                .greaterOrEqual(AlarmData_.uploadStatus, 1).build()
        val entitiesToDelete = queryToDelete.find()

        alarmBox.remove(entitiesToDelete)
        queryToDelete.close()
    }

    private fun updateStatusAsUploadedByAlarmId(alarmId: Long, status: Int) {
        val alarm = ObjectBoxManager.getAlarmBox().query()
            .equal(AlarmData_.alarmId, alarmId)
            .build()
            .findFirst() ?: return

        alarm.uploadStatus = status
        insertOrUpdateAlarmData(alarm)
    }

    data class AlarmDataWithEvents(
        val alarmData: AlarmData,
        val alarmEvents: List<AlarmEventData>
    )
}