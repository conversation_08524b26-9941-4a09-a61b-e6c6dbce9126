package com.spacelabs.app.database.dao

import androidx.room.*
import com.spacelabs.app.database.entities.*
import com.spacelabs.app.database.relationships.PatientWithVisitAndMeasurementData

@Dao
interface MeasurementDataDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSensor(sensor: SensorTable): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMeasurementData(measurementDataTable: MeasurementDataTable): Long

    @Query("SELECT patientId AS id FROM tblPatient WHERE status = 'active'")
    suspend fun getActivePatientId(): Int?

    @Query("SELECT patientId AS id FROM tblPatient WHERE status = 'inactive'")
    suspend fun getInActivePatientId(): List<Int>

    @Query("SELECT * FROM tblSensors WHERE patientId = :patientId AND sensorName = :sensorName")
    suspend fun checkIfSensorExist(patientId: Int, sensorName: String): SensorTable?

    @Update
    suspend fun updateSensor(sensor: SensorTable)

    @Query("UPDATE tblSensors SET pairingStatus = 'disconnected' WHERE patientId = :patientId AND sensorType = :sensorType")
    suspend fun setPreviousPatchStatusesOfPatientToDisconnect(patientId: Int, sensorType: String)

    @Query("SELECT visitId FROM tblVisit WHERE patientId = (SELECT patientId FROM tblPatient WHERE status = 'active') AND visitId = (SELECT MAX(visitId) FROM tblVisit)")
    suspend fun getLastVisitIdOfCurrentPatient(): Int?

    @Query("SELECT paramId FROM tblParameters WHERE paramName = :paramName")
    suspend fun getParameterIdByName(paramName: String): Int

    @Query("SELECT * FROM tblMeasurementData ORDER BY measurementId Desc")
    suspend fun getAllMeasurementData(): List<MeasurementDataTable>

    @Query("SELECT * FROM tblMeasurementData WHERE paramName = :param ORDER BY tickValue ASC")
    suspend fun getAllMeasurementDataByParamName(param: String): List<MeasurementDataTable>?

    @Query("DELETE  FROM tblMeasurementData WHERE tickValue  <= :tick AND uploadStatus = 1 ")
    suspend fun deleteAllMeasurementData(tick:Long):Int
    @Query("DELETE  FROM tblMeasurementData WHERE  uploadStatus = 1")
    suspend fun deleteMeasurementDataOnDischarge():Int

    @Transaction
    @Query("SELECT * FROM tblMeasurementData md INNER JOIN tblVisit vs ON md.visitId = vs.visitId INNER JOIN tblPatient pt ON pt.patientId = vs.patientId WHERE pt.patientId = (SELECT patientId FROM tblPatient WHERE status = 'active' ) AND md.uploadStatus = 0 AND md.paramName = :paramName  ORDER BY md.tickValue ASC")
    suspend fun getAllPendingMeasurementOfPatient(paramName: String): PatientWithVisitAndMeasurementData?

    @Query("SELECT * FROM tblMeasurementData WHERE paramName = 'ECG' AND uploadStatus = 0 ORDER BY measurementId ASC")
    suspend fun getPendingECGMeasurements(): List<MeasurementDataTable>?

    @Query("UPDATE tblMeasurementData SET uploadStatus = 1 WHERE measurementId = :measurementId")
    suspend fun updateUploadStatusToUploaded(measurementId: Int): Int

    @Update
    suspend fun updateMeasurementData(measurementDataTable: MeasurementDataTable): Int

    @Query("SELECT COUNT(*) FROM tblMeasurementData")
    suspend fun getCountOfMeasurementData(): Int

    @Query("UPDATE tblMeasurementData SET uploadStatus = 1, uploadTimestamp = :uploadTime WHERE measurementId = :measurementId")
    suspend fun updateUploadStatusToUploadedById(measurementId: Int, uploadTime: String)

    @Query("UPDATE tblMeasurementData SET uploadStatus = 0 WHERE paramName = 'ECG'")
    suspend fun updateUploadStatusToZeroForECG()

    suspend fun insertOrUpdateSensor(sensor: SensorTable): Int{
        val sensorName = sensor.sensorName
        val patientId = sensor.patientId
        val newSensor = checkIfSensorExist(patientId, sensorName)
        return if(newSensor != null){
            val upSensor = SensorTable(newSensor.sensorId, sensor.sensorDefId, sensor.patientId, sensor.sensorType,sensor.sensorName,sensor.sensorAddress,sensor.registerTimestamp,sensor.pairingStatus)
            updateSensor(upSensor)
            newSensor.sensorId!!
        }else {
            insertSensor(sensor).toInt()
        }
    }
}