package com.spacelabs.app.database.objectbox.encryption

import io.objectbox.converter.PropertyConverter

class EncryptionConverter : PropertyConverter<ByteArray, ByteArray> {

    override fun convertToDatabaseValue(entityProperty: ByteArray): ByteArray? {
        return AESCrypt.encrypt( entityProperty)
    }

    override fun convertToEntityProperty(databaseValue: ByteArray?): ByteArray? {
        return if (databaseValue != null ) {
            AESCrypt.decrypt( databaseValue)
        } else {
            null
        }
    }
}
