package com.spacelabs.app.api

import android.annotation.SuppressLint
import android.util.Log
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.iomt.IomtApiManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.*
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.URI
import java.net.URL
import java.net.URLEncoder
import java.util.*

class SnsApiManager {

    private val okHttpClient = OkHttpClient()

    private val apiJob = Job()
    private val apiScope = CoroutineScope(Dispatchers.IO + apiJob)

    init {
        extractLoginDetails()
        initSnsApi()
    }

    companion object{
        var LOGIN_API_URL=""
        var LOGIN_API_TOKEN=""
        var DEVICE_OAUTH_USERNAME=""
        var DEVICE_OAUTH_PASSWORD=""
        var ORIGIN=""
        var BODY_TOKEN=""
        var BASE_URI = ""
        const val API_URL = "https://rpm.510.test.safensound.io/api/v1/associate/"
        private const val FHIR_URL = "wss://rpm.510.test.safensound.io/api/v1/fhir/"

        var ACCOUNT_ID = ""
        var DEVICE_UUID = ""
        @SuppressLint("HardwareIds")
        val IMEI = "f63324200444899d"/*CommonDataArea.ANDROID_ID*/ //"f63324200444899d"   //"***************"
        var DeviceId= IomtApiManager.DEVICE_UUID
        var apiResponseData = ApiResponseData()
        var webSocketClient: WebSocketClientFhir? = null
        private val tag = "SNS_ApiManager"

        fun connectFhir(){
            //New updated URL
            try{
                //val url = "$FHIR_URL$ACCOUNT_ID/$IMEI/store"
                val url = "$FHIR_URL$ACCOUNT_ID/${IomtApiManager.DEVICE_UUID}/store"
                val headers: MutableMap<String, String> = HashMap()
                headers["Authorization"] = "Bearer " + apiResponseData.jwtToken
                webSocketClient = WebSocketClientFhir(URI.create(url), headers, apiResponseData)
                Log.d(tag,"connectFhir URl : ${ url} ,jwtToken : ${apiResponseData.jwtToken}")

                webSocketClient?.connect()
            } catch(ex: InterruptedException){
                Log.d(tag,"connectFhir ${ ex.stackTraceToString() }")
            }
        }
        fun connectFhirWithRefreshToken(){
            //New updated URL
            try{
                //val url = "$FHIR_URL$ACCOUNT_ID/$IMEI/store"
                val url = "$FHIR_URL$ACCOUNT_ID/${IomtApiManager.DEVICE_UUID}/store"
                val headers: MutableMap<String, String> = HashMap()
                headers["Authorization"] = "Bearer " + apiResponseData.refreshToken
                webSocketClient = WebSocketClientFhir(URI.create(url), headers, apiResponseData)
                Log.d(tag,"connectFhir URl with RefreshToken: ${ url} ,refreshToken : ${apiResponseData.refreshToken}")

                webSocketClient?.connect()
            } catch(ex: InterruptedException){
                Log.d(tag,"connectFhir ${ ex.stackTraceToString() }")
            }
        }
    }



    private fun initSnsApi(){
        apiScope.launch {
            delay(1000)
            if(/*hasDeviceAuthenticated() &&*/ getJwtToken()) {
                connectFhir()
            }
        }
    }


    /*Old Way for Connecting To Sns Server to receive login url and Token*/
    private fun hasDeviceAuthenticated(): Boolean{
        var hasAuthenticated = false
        val url = "$API_URL${apiResponseData.accountNumber}/${IomtApiManager.ACCOUNT_ID}"
        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        try {
            val response: Response = okHttpClient.newCall(request).execute()
            Log.d(tag,"AuthResponse, ${response.toString()}")
            hasAuthenticated =  processResponse(response)
        } catch (e: IOException) {
            Log.e(tag, "Device Authentication Exception: " + e.stackTraceToString())
        }
        return hasAuthenticated
    }

    private fun processResponse(response: Response): Boolean{
        return if (!response.isSuccessful) {
            Log.e(tag, "Error getting response: $response")
            false
        } else {
            val responseBody = JSONObject(Objects.requireNonNull(response.body!!).string())
            val isSuccess = responseBody.getString("errorMessage").equals("null", ignoreCase = true)
            try {
                if(isSuccess)
                    parseAuthenticationResponse(responseBody)
            } catch (e: JSONException) {
                Log.e(tag, "Error parsing response: " + e.message)
            }
            isSuccess
        }
    }

    private fun parseAuthenticationResponse(json: JSONObject){
        try {
            setApiResponseData("basicAuthToken", json.getString("basicAuthToken"))
            val authUrl = json.getString("oauthLoginURL")
            val splitAuthUrl = authUrl.split("\\?".toRegex()).dropLastWhile { it.isEmpty() }
                .toTypedArray()
            setApiResponseData("oauthLoginURL", "${splitAuthUrl[0]}?")
            val splitUrlParameters =
                splitAuthUrl[1].split("&".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
            for (s in splitUrlParameters) {
                val splits = s.split("=".toRegex()).dropLastWhile { it.isEmpty() }
                    .toTypedArray()
                setApiResponseData(splits[0], splits[1])
            }
        } catch (ex: JSONException) {
            Log.d(tag,"ParseAuthJsonEx ${ex.message!!}")
        }
    }

    private fun getJwtToken(retries: Int = 3): Boolean {
        var isJwtTokenReceived = false
        val authorization = "Basic ${apiResponseData.authToken}"
        Log.i(tag, "Authorization  $authorization")

       // val encodedToken =
        val requestBody: RequestBody = FormBody.Builder()
            .addEncoded("accountNumber", apiResponseData.accountNumber)
            .addEncoded("username", apiResponseData.userName)
            .addEncoded("Origin", apiResponseData.origin)
            .addEncoded("Token",URLEncoder.encode(apiResponseData.token, "UTF-8"))
            .addEncoded("password", apiResponseData.password)
            .build()

        Log.d(tag, "onGetJwtToken ${apiResponseData.url}")

        val request: Request = Request.Builder()
            .url(apiResponseData.url)
            .header("Authorization", authorization)
            .post(requestBody)
            .build()

        // Convert the requestBody to a String for logging
        val buffer = okio.Buffer()
        requestBody.writeTo(buffer)
        val requestBodyString = buffer.readUtf8()

        Log.d(tag, "Request Body: $requestBodyString")
        Log.i(tag, "JWT token: $request")

        var attempt = 0
       // while (attempt < retries) {
            try {
                val response: Response = okHttpClient.newCall(request).execute()
                isJwtTokenReceived = processJwtResponse(response)
               // if (isJwtTokenReceived) break
            } catch (e: SocketTimeoutException) {
                Log.e(tag, "Socket timeout, retrying... ($attempt)")
                attempt++
            } catch (e: IOException) {
                Log.e(tag, "Error getting JWT token: " + e.stackTraceToString())
               // break
            }
        //}

        return isJwtTokenReceived
    }


    private fun processJwtResponse(response: Response): Boolean{
        return if(!response.isSuccessful) {
            Log.d(tag,"ErrorOnResponse $response")
            false
        } else{
            val json = JSONObject(response.body!!.string())
            Log.d(tag,"JwtJson ${json.toString()}")
            parseJwtTokenResponse(json)
            true
        }
    }

    private fun parseJwtTokenResponse(json: JSONObject){
        try{
            setApiResponseData("access_token", json.getString("access_token"))
        } catch(ex: JSONException){
            Log.d(tag,"onParseJwt ${ex.stackTraceToString()}")
        }
    }

    private fun setApiResponseData(key: String, value: String){
        when (key) {
            "basicAuthToken" -> apiResponseData.authToken = value
            "oauthLoginURL" -> apiResponseData.url = value
            "accountNumber" -> apiResponseData.accountNumber = value
            "username" -> apiResponseData.userName = value
            "Origin" -> apiResponseData.origin = value
            "Token" -> apiResponseData.token = value
            "password" -> apiResponseData.password = value
            "access_token" -> apiResponseData.jwtToken = value
            "refresh_token" -> apiResponseData.refreshToken = value
        }
        Log.d(tag, "setApiResponseData:${key}__${value} ")
    }

    fun extractLoginDetails() {
        try {
            // Parse the LOGIN_API_URL
            val url = URL(LOGIN_API_URL)
            Log.d("extractLoginURl", "extractLoginURl: ${url.toString()} ")
           // val query = url.query

         //   if (query != null) {
                // Split the query parameters into a map
              /*  val params = query.split("&")
                    .map { it.split("=") }
                    .associate { it[0] to it[1] }*/

                // Assign extracted values to the apiResponseData object
                apiResponseData.accountNumber = SettingDaoHelper.AppSettings.AccountId.getValue()/*?:"0510"*//* params["accountNumber"] ?:*/  // Default to 0510 if missing
                apiResponseData.userName = SettingDaoHelper.AppSettings.OauthUsername.getValue() ?: "hubapi"
                apiResponseData.origin = SettingDaoHelper.AppSettings.Origin.getValue()?: "EMR"
                apiResponseData.token =SettingDaoHelper.AppSettings.BodyToken.getValue()?: "aE8%24tH8%7DwT5%40eD0%3AtT0%24yP5%29tL5%2BpU0%24"
                apiResponseData.password = SettingDaoHelper.AppSettings.OauthPassword.getValue()?: "foo"
                apiResponseData.authToken =SettingDaoHelper.AppSettings.apiToken.getValue()?: LOGIN_API_TOKEN
                apiResponseData.url ="${SettingDaoHelper.AppSettings.apiUrl.getValue() }/oauth/token" /*"${url.protocol}://${url.host}${url.path}" *//*" https://rpm.510.test.safensound.io/oauth/token"*/

                // Log the extracted values
                Log.d(tag, "extractLoginURl: Account Number: ${apiResponseData.accountNumber}, Username: ${apiResponseData.userName}, Origin: ${apiResponseData.origin}, Token: ${apiResponseData.token}, Password: ${apiResponseData.password}, Auth Token: ${apiResponseData.authToken}")
                Log.d(tag, "apiResponseData.url : ${SettingDaoHelper.AppSettings.apiUrl.getValue() }")
          //  }
        } catch (e: Exception) {
            Log.e(tag, "Error extracting login URL parameters: ${e.message}")
        }
    }
}