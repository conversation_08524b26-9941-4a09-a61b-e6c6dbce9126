package com.spacelabs.app.api.data.dataclasses


data class BpObservationBean(
    val resourceType: String = "Observation",
    val deviceDisplay: String = "Blood Pressure Monitor",
    val status: String = "final",
    val categoryCode: String = "vital-signs",
    val categoryDisplay: String = "Vital Signs",
    val codeText: String = "Blood pressure systolic and diastolic",
    val bodySiteSystem: String = "http://snomed.info/sct",
    val bodySiteCode: String = "368209003",
    val bodySiteDisplay: String = "Right arm",
    val valQuantitySys: String = "http://unitsofmeasure.org",
    val valQuantityCode: String = "mm[Hg]",
    var timestamp: String,
    var bpSys: Int,
    var bpDia: Int,
    var uuId: String
)