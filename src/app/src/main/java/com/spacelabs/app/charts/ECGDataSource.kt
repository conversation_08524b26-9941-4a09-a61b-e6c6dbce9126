package com.spacelabs.app.charts

import android.graphics.Color
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.utils.ColorTemplate
import com.spacelabs.app.common.LogWriter

class ECGDataSource : XYDataSource() {
    override val highlightPixels = 11 //todo use a percentage for hardcoded num pixels
    override fun updateData(chart: LineChart, color: Int) {
        try {
            val data = chart.data
            if (data != null) {
                val set = data.getDataSetByIndex(0) as? LineDataSet
                if (set != null && values.isNotEmpty()) {
                    set.values = values
                    set.notifyDataSetChanged()
                    set.calcMinMax()

                    val writePtrTemp = writePtr
                    if (writePtrTemp < maxData - highlightPixels) {
                        chart.highlightValue(0.0f, -1)
                        val valuesCloned = values.toMutableList()
                        for (i in writePtrTemp + 1..writePtrTemp + (highlightPixels - 1)) {
                            if (i < valuesCloned.size) {
                                val valueEntry = valuesCloned[i]
                                valueEntry.x = i.toFloat()
                                val highlight = Highlight(valueEntry.x, valueEntry.y, 0)
                                highlight.dataIndex = 0
                                chart.highlightValue(highlight, false)
                                set.highlightLineWidth = 10f
                                set.setDrawHorizontalHighlightIndicator(false)
                                set.highLightColor = highlighterColor // Todo: Use a pre-defined value instead of hard-coded
                            }
                        }
                    }

                    chart.data.notifyDataChanged()
                    chart.notifyDataSetChanged()
                    chart.invalidate()
                }
            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("ECGDataSource Update Chart->"+xyDataType.name, "Exception->"+ex.printStackTrace())
        }
    }


    override fun setupYaxis(chart: LineChart, yMax: Float, yMin: Float) {
        chart.isAutoScaleMinMaxEnabled = false
        val leftAxis: YAxis = chart.axisLeft
        leftAxis.typeface = tfLight
        leftAxis.textColor = ColorTemplate.getHoloBlue()
        leftAxis.axisMaximum = yMax
        leftAxis.axisMinimum = yMin
        leftAxis.setDrawGridLines(false)
        leftAxis.isGranularityEnabled = true

        val rightAxis: YAxis = chart.axisRight
        rightAxis.typeface = tfLight
        rightAxis.textColor = Color.RED
        rightAxis.axisMaximum = yMax
        rightAxis.axisMinimum = yMin
        rightAxis.setDrawGridLines(false)
        rightAxis.setDrawZeroLine(false)
        rightAxis.isGranularityEnabled = false
    }

    override fun setupXAxis(chart: LineChart){
        val xAxis: XAxis = chart.xAxis
        xAxis.typeface = tfLight
        xAxis.textSize = 11f
        xAxis.textColor = Color.WHITE
        xAxis.setDrawGridLines(false)
        xAxis.setDrawAxisLine(false)
    }
}