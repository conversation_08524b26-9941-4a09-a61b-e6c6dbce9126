package com.spacelabs.app.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import com.spacelabs.app.R
import com.spacelabs.app.MainActivity
import com.spacelabs.app.ui.dialogs.dialogHelper.CommonAlarmDialogsHelper

class CommonAlarmsDialogController(mainActivity: MainActivity, alert: Alerts): DialogManager(mainActivity) {

    private val alert: Alerts
    init {
        this.alert = alert
    }

    private lateinit var dialog: Dialog
    private lateinit var alarmTitle: TextView
    private lateinit var alarmText: TextView

    private lateinit var alarmIcon: ImageView

    private lateinit var acknowledgeBtn: AppCompatButton

    private lateinit var commonAlarmDialogsHelper: CommonAlarmDialogsHelper

    override fun onCreateDialogActions(context: Context, dialog: Dialog, layout: Int) {
        this.dialog = dialog
    }

    override fun initUIs(dialog: Dialog) {
        alarmTitle = dialog.findViewById(R.id.alarmTitle)
        alarmText = dialog.findViewById(R.id.alarmText)
        alarmIcon = dialog.findViewById(R.id.alarmIcon)
        acknowledgeBtn = dialog.findViewById(R.id.acknowledgeBtn)
    }

    override fun postUiInitActions(context: Context) {
        commonAlarmDialogsHelper = CommonAlarmDialogsHelper(alert, context)
        alarmTitle.text = commonAlarmDialogsHelper.alertTitle
        alarmIcon.setImageDrawable(commonAlarmDialogsHelper.imgSrc)
        acknowledgeBtn.text = commonAlarmDialogsHelper.btnText
        commonAlarmDialogsHelper.updateAlarmTextView(alarmText, dialog)
    }

    override fun uiActionListeners(context: Context, dialog: Dialog) {
        acknowledgeBtn.setOnClickListener {
            commonAlarmDialogsHelper.onAcknowledgeBtnClick(dialog)
        }
    }

    enum class Alerts{
        TurnTimer,
        Fall,
        PauseAlarm
    }

}