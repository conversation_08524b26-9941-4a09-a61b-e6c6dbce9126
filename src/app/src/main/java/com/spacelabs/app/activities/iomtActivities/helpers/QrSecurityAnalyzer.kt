package com.spacelabs.app.activities.iomtActivities.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.spacelabs.app.activities.commonActivities.helpers.QRCodeUtils
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.iomt.IomtApiManager
import com.spacelabs.app.iomt.apiHandler.AuthenticationHandler
import com.spacelabs.app.iomt.apiHandler.interfaces.TokenCallback
import com.spacelabs.app.ui.dialogs.ProgressDialogHandler
import org.json.JSONObject

class QrSecurityAnalyzer(
    private val context: Context,
    private val qrConfigHelper: QrCodeConfigHelper,
    private val progressDialogHandler: ProgressDialogHandler
) {
    private val authenticationHandler = AuthenticationHandler()

    fun handleEncScannedData(data: String) {
        try {
            val jsonObject = JSONObject(data)

            if (jsonObject.has("qrtoken")) {
                val qrData = jsonObject.getString("qrtoken")
                val decryptedString = QRCodeUtils.decryptWithPassphrase(
                    qrData,
                    SettingDaoHelper.AppSettings.DevicePass.getValue()
                )
                Log.d("Decrypted String:", "Data : $decryptedString")
                extractDetails(decryptedString)
            } else {
                handleError("Error processing QR code. Please scan a valid QR code.")
            }
        } catch (e: Exception) {
            handleError("Error processing QR code. Please scan a valid QR code.")
            Log.e("QRCodeProcessing", "Error processing QR code: ${e.message}")
        }
    }

    private fun extractDetails(decryptedString: String) {
        try {
            val jsonObject = JSONObject(decryptedString)

            val companyId = jsonObject.getString("companyId")
            val username = jsonObject.getString("username")
            val password = jsonObject.getString("password")
            val websocketUrl = jsonObject.getString("websocketurl")
            val apiUrl = jsonObject.getString("APIUrl")
            val sensorAuthentication = jsonObject.getString("SensorAuthentication")

            Log.d("Extracted Data", "Company ID: $companyId")
            Log.d("Extracted Data", "Username: $username")
            Log.d("Extracted Data", "Password: $password")
            Log.d("Extracted Data", "WebSocket URL: $websocketUrl")
            Log.d("Extracted Data", "API URL: $apiUrl")
            Log.d("Extracted Data", "SensorAuthentication: $sensorAuthentication")

            if (qrConfigHelper.saveDeviceValues(0, username) &&
                qrConfigHelper.saveDeviceValues(1, password) &&
                qrConfigHelper.saveDeviceValues(2, websocketUrl) &&
                qrConfigHelper.saveDeviceValues(3, apiUrl)) {

                deviceLogin(companyId)
            } else {
                handleError("Error saving device values")
            }
        } catch (e: Exception) {
            handleError("Error processing QR code. Please scan a valid QR code.")
            Log.e("handleEncScannedData", "Error processing QR code: ${e.message}")
        }
    }

    private fun deviceLogin(companyID: String) {
        val iomtApiManager = IomtApiManager()

        iomtApiManager.getTokenByOauthLogin(object : TokenCallback {
            override fun onTokenReceived(success: Boolean, token: String, message: String) {
                if (success && IomtApiManager.isTokenSaved && token.isNotEmpty()) {
                    authenticateDevice(IomtApiManager.DEVICE_UUID, companyID, token)
                } else {
                    logDeviceDetails()
                    handleError(message)
                }
            }
        })
    }

    private fun authenticateDevice(deviceId: String, companyID: String, token: String) {
        authenticationHandler.deviceToCompanyMapping(deviceId, companyID) { success, message ->
            if (success) {
                qrConfigHelper.saveDeviceValues(4, token)
                qrConfigHelper.saveAccountIdSettingsAndNavigateToNext(companyID)
                progressDialogHandler.dismissProgressDialog()
            } else {
                handleError(message)
            }
        }
    }

    private fun handleError(message: String) {
        showToast(message)
        progressDialogHandler.dismissProgressDialog()
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun logDeviceDetails() {
        Log.d("deviceLogin", "AccountId : ${SettingDaoHelper.AppSettings.AccountId.getValue()}")
        Log.d("deviceLogin", "DevicePass : ${SettingDaoHelper.AppSettings.DevicePass.getValue()}")
        Log.d("deviceLogin", "DeviceUuid : ${SettingDaoHelper.AppSettings.DeviceUuid.getValue()}")
        Log.d("deviceLogin", "OauthPassword : ${SettingDaoHelper.AppSettings.OauthPassword.getValue()}")
        Log.d("deviceLogin", "DeviceUuid : ${SettingDaoHelper.AppSettings.OauthUsername.getValue()}")
        Log.d("deviceLogin", "apiUrl : ${SettingDaoHelper.AppSettings.apiUrl.getValue()}")
        Log.d("deviceLogin", "websocketUrl : ${SettingDaoHelper.AppSettings.websocketUrl.getValue()}")
        Log.d("deviceLogin", "HasAuthenticated : ${SettingDaoHelper.AppSettings.HasAuthenticated.key}")
    }
}
