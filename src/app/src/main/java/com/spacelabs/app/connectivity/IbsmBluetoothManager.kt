package com.spacelabs.app.connectivity

import android.Manifest
import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.interfaces.IOEventCallback

class IbsmBluetoothManager(val activity: AppCompatActivity) {

    private val context: Context
    private val bluetoothManager: BluetoothManager
    private val bluetoothAdapter: BluetoothAdapter

    init {
        this.context = activity
        bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        bluetoothAdapter = bluetoothManager.adapter
    }

    companion object {
        private const val REQUEST_ENABLE_BT = 1
    }

    private val bluetoothReceiver = object: BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            intent ?: return
            when (intent.action) {
                BluetoothDevice.ACTION_FOUND -> {
                    val device = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                        } else {
                            intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                        }

                    if (ActivityCompat.checkSelfPermission(
                            activity,
                            Manifest.permission.BLUETOOTH_CONNECT
                        ) == PackageManager.PERMISSION_GRANTED
                    ) {
                        Log.d("BluetoothDevice", "Found device: ${device?.name}, ${device?.address}")
                        device?.createBond()
                    }
                }
                BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                    Log.d("BluetoothDevice", "Discovery finished")
                }
            }
        }
    }

    fun registerReceiver() {
        val filter = IntentFilter().apply {
            addAction(BluetoothDevice.ACTION_FOUND)
            addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
        }

        activity.registerReceiver(bluetoothReceiver, filter)
    }

    fun unregisterReceiver() {
        activity.unregisterReceiver(bluetoothReceiver)
    }

    @RequiresApi(Build.VERSION_CODES.S)
    fun turnOnBluetoothIfIsOff() {
        try {
            if (!bluetoothAdapter.isEnabled) {
                val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                if (ActivityCompat.checkSelfPermission(
                        context,
                        Manifest.permission.BLUETOOTH_CONNECT
                    ) != PackageManager.PERMISSION_GRANTED
                ) {
                    CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.AskPermission, Manifest.permission.BLUETOOTH_CONNECT)
                }
                bluetoothEnableLauncher.launch(enableBtIntent)
            }
        } catch (ex: Exception) {
            Log.d("EnableBluetoothIfTurnedOff", ex.stackTraceToString())
        }
    }

    private val bluetoothEnableLauncher = activity.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result: ActivityResult ->
        CommonDataArea.isBluetoothEnabled = result.resultCode == Activity.RESULT_OK
    }

    @RequiresApi(Build.VERSION_CODES.S)
    fun unpairSensor(sensor: Sensor) {
        try {
            val sensorToRemove = if (ActivityCompat.checkSelfPermission(
                    context,
                    Manifest.permission.BLUETOOTH_CONNECT
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.AskPermission, Manifest.permission.BLUETOOTH_CONNECT)
                null
            } else {
                bluetoothAdapter.bondedDevices.firstOrNull { it.address == sensor.address }
            }

            sensorToRemove ?: return

            val method = sensorToRemove.javaClass.getMethod("removeBond")
            method.invoke(sensorToRemove)

        } catch (ex: Exception) {
            Log.d("UnpairSensor", ex.stackTraceToString())
        }
    }

    fun pairSensor(sensor: Sensor) {
        if (!bluetoothAdapter.isEnabled) {
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            if (ActivityCompat.checkSelfPermission(
                    activity,
                    Manifest.permission.BLUETOOTH_CONNECT
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                return
            }
            activity.startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT)
        } else {
            startDiscovery()
        }
    }

    private fun startDiscovery() {
        if (ActivityCompat.checkSelfPermission(
                activity,
                Manifest.permission.BLUETOOTH_SCAN
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }
        if (!bluetoothAdapter.isDiscovering) {
            bluetoothAdapter.startDiscovery()
        }
    }
}