package com.spacelabs.app.database.daoHelper

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.entities.SettingsTable
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.iomt.IomtApiManager
import kotlinx.coroutines.*

class SettingDaoHelper(context: Context): DbDaoHelperManager(context) {

     private val mContext: Context
     private val sharedPreferences: SharedPreferences

     init {
         this.mContext = context
         sharedPreferences = mContext.getSharedPreferences("settings", Context.MODE_PRIVATE)
     }

    enum class AppSettings(val key: String, private var value: String = String()) {
        BpTimer("bpTimer", DEFAULT_TIMER),
        TurnTimer("turnTimer", DEFAULT_TIMER),
        FallStream("fallStream", TOGGLE_STATUS),
        PeriodicDataClearance("periodicDataClearance", DEFAULT_PERIODIC_CLEARANCE_TIMER),
        WifiSsid("wifiSsid"),
        WifiPassword("wifiPassword"),
        AccountId("accountId"),
        AutoBp("autoBp", TOGGLE_STATUS),
        PatientMode("patientMode", PatientDaoHelper.PatientModes.Active.name),
        HasAuthenticated("hasAuthenticated"),
        Volume("volume", DEFAULT_VOLUME),
        DeviceUuid("deviceUuid"),
        DevicePass("devicePass"),
        OauthUsername("OauthUsername"),
        OauthPassword("OauthPassword"),
        Origin("Origin"),
        BodyToken("BodyToken"),
        websocketUrl("websocketUrl"),
        apiUrl("apiUrl"),
        apiToken("apiToken"),
        ;


        fun getValue(): String{
            return value
        }

        fun setValue(value: String) {
            if(isAllowedToChange)
                this.value = value

            isAllowedToChange = false
        }
    }

     companion object{

         private const val DEFAULT_TIMER = "1h"
         private const val DEFAULT_PERIODIC_CLEARANCE_TIMER="14d"
         private const val TOGGLE_STATUS = "ON"
         private const val DEFAULT_VOLUME = "100"

         private val personalSettings = listOf(
             AppSettings.BpTimer,
             AppSettings.FallStream,
             AppSettings.TurnTimer,
             AppSettings.AutoBp,
             AppSettings.PatientMode,
             AppSettings.Volume
         )

         private var isAllowedToChange = false
     }

     fun getAllSettings(){
         val allSettings = AppSettings.values()
         for(settings in allSettings) {
             getSettingsAndInsertIfNotExists(settings)
         }
     }

     private fun getSettingsAndInsertIfNotExists(settings: AppSettings){
         try {
             dbCoroutine.launch {
                 val data = settingsDao.getByKey(settings.key)
                 val value = if(data != null){
                     sharedPreferences.getString(settings.key, null)
                 } else {
                     insertSettings(settings)
                     settings.getValue()
                 }
                 setSettingsValue(settings, value ?: String())
                 assignValueByCheckingKey(settings)
             }
         } catch (ex: Exception) {
             Log.d("GetSettingsAndInsertIfNotExists", ex.stackTraceToString())
         }
     }

    fun updateSettings(settings: AppSettings, settingValue: String){
        dbCoroutine.launch {
            val setting = settingsDao.getByKey(settings.key)
            if(setting != null){
                setSettingsValue(settings, settingValue)
                val value = convertToTimeIfKeyIsTimer(settings)
                setting.settingsValue = value
                putSharedPreferenceSettingByKey(settings)
                settingsDao.updateData(setting)
                setDueTimeStamp(settings)
                assignValueByCheckingKey(settings)
            }
        }
    }

    private fun assignValueByCheckingKey(settings: AppSettings){
        try {
            val value = settings.getValue()
            when(settings){
                AppSettings.BpTimer,
                AppSettings.TurnTimer,
                AppSettings.FallStream,
                AppSettings.PatientMode -> onUiValuesChangeAction(settings)
                AppSettings.PeriodicDataClearance ->CommonDataArea.dataDeletionTimer = value
                AppSettings.WifiSsid -> CommonDataArea.SSID = value
                AppSettings.WifiPassword -> CommonDataArea.wifiPassword = value
                AppSettings.AccountId -> if (enableSnsApi)SnsApiManager.ACCOUNT_ID = value else IomtApiManager.ACCOUNT_ID=value
                AppSettings.HasAuthenticated -> IomtApiManager.DEVICE_EXIST = value
                AppSettings.AutoBp -> CommonDataArea.autoBpMode = value == "ON"
                AppSettings.Volume -> CommonDataArea.setAlarmVolume(value)
                AppSettings.DeviceUuid -> IomtApiManager.DEVICE_UUID=value
                AppSettings.DevicePass -> IomtApiManager.DEVICE_PASSPHRASE=value
                AppSettings.OauthUsername ->if(enableSnsApi)SnsApiManager.DEVICE_OAUTH_USERNAME else IomtApiManager.DEVICE_OAUTH_USERNAME=value
                AppSettings.OauthPassword -> if(enableSnsApi)SnsApiManager.DEVICE_OAUTH_PASSWORD else IomtApiManager.DEVICE_OAUTH_PASSWORD=value
                AppSettings.Origin -> SnsApiManager.ORIGIN=value
                AppSettings.BodyToken -> SnsApiManager.BODY_TOKEN=value
                AppSettings.websocketUrl -> IomtApiManager.DEVICE_WEBSOCKET_URL=value
                AppSettings.apiUrl ->if (enableSnsApi)SnsApiManager.LOGIN_API_URL = value else  IomtApiManager.DEVICE_API_URL=value
                AppSettings.apiToken ->if (enableSnsApi)SnsApiManager.LOGIN_API_TOKEN = value else  IomtApiManager.DEVICE_API_TOKEN=value
            }
        } catch (ex: Exception) {
            Log.d("AssignValueByCheckingKey:", ex.stackTraceToString())
        }
    }

    private fun onUiValuesChangeAction(settings: AppSettings) {
        val value = settings.getValue()
        when(settings) {
            AppSettings.BpTimer -> CommonDataArea.bpMeasurementPeriodicTime = value
            AppSettings.TurnTimer -> CommonDataArea.patientModePeriodicTime = value
            AppSettings.FallStream -> CommonDataArea.fallStream = value
            AppSettings.PatientMode -> CommonDataArea.patientMode = value
            else -> return
        }
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SettingsChanged, settings)
    }

     private suspend fun insertSettings(settings: AppSettings){
         putSharedPreferenceSettingByKey(settings)
         val value = convertToTimeIfKeyIsTimer(settings)
         val setting = SettingsTable(
             null,
             settings.key,
             value
         )
         setDueTimeStamp(settings)
         settingsDao.insertData(setting)
     }

    private fun setDueTimeStamp(settings: AppSettings){
        val value = settings.getValue()
        when(settings){
            AppSettings.BpTimer -> CommonDataArea.bpTimerTimestampDbValue = value
            AppSettings.TurnTimer -> CommonDataArea.TurnTimerTimestampDbValue = value
            else -> Log.d("invalidKey", "$settings key is Invalid")
        }
    }

     private fun convertToTimeIfKeyIsTimer(settings: AppSettings): String{
        val value = settings.getValue()
        val timestamp = when(settings){
            AppSettings.BpTimer -> TimestampUtils.getRoundedOffTimeForIntervals(value)
            AppSettings.TurnTimer -> TimestampUtils.convertToTimestamp(value)
            AppSettings.PeriodicDataClearance -> value
            else -> value
        }

         CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.ScheduleAlarm, Pair(settings, timestamp))
         return timestamp
    }

    fun updateDataClearanceTime(dbTime: String) {
        dbCoroutine.launch {
            val clearance = settingsDao.getByKey(AppSettings.PeriodicDataClearance.key)
            if (clearance != null) {
                clearance.settingsValue = dbTime
                settingsDao.updateData(clearance)
            }
        }
    }

    private fun putSharedPreferenceSettingByKey(settings: AppSettings){
        val editor = sharedPreferences.edit()
        editor.putString(settings.key, settings.getValue()).apply()
    }

    suspend fun hasSettings(key: String): Boolean{
        val setting = settingsDao.getByKey(key)
        return if(setting == null)
            false
        else{
            val value = setting.settingsValue
            value.isNotEmpty()
        }
    }

    fun resetPersonalSettingsOnDischarge() {
        for (settings in personalSettings) {
            updateSettings(settings, settings.getValue())
        }
    }

    private fun setSettingsValue(settings: AppSettings, value: String) {
        isAllowedToChange = true
        settings.setValue(value)
    }

    suspend fun hasDeviceSettings(key: String): Boolean{
        val setting = settingsDao.getByKey(key)
        return if(setting == null)
            false
        else{
            val value = setting.settingsValue
            value.isNotEmpty()
        }
    }
    fun resetAllSettings() {
        val settings = AppSettings.values()
        for (setting in settings) {
            updateSettings(setting, setting.getValue())
        }
    }

}