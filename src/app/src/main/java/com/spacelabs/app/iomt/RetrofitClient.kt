package com.spacelabs.app.iomt

import android.util.Log
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit


object RetrofitClient {

   // private var BASE_URL = "https://www.iorbit-tech.com:8443/api/v1/"
    //private var TOKEN = "eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************.oduFehx-Xf97ZVRbUH8P7jsr_oI9BCUlwJeB7HsPSu7FmQ6wxknvpyVR3cculyh8MgGemaAVV5l6lD4ffX-YVg" // Replace with your actual token
 /*  private var BASE_URL = "https://www.iorbit-tech.com:8443/api/v1/"

    *//*SettingDaoHelper.AppSettings.apiUrl.getValue().toString()*//*
    private var TOKEN = SettingDaoHelper.AppSettings.apiToken.getValue()*/
   private var BASE_URL: String = ""
    private var TOKEN: String = ""

    init {
        refreshSettings()
    }
    private val okHttpClient: OkHttpClient = OkHttpClient.Builder()
        .addInterceptor { chain ->
            val request = chain.request().newBuilder()
                .addHeader("Authorization", "Bearer $TOKEN")
                .build()
            chain.proceed(request)
        }
       /* .addInterceptor(HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        })*/
        .addInterceptor(TokenInterceptor())
        .connectTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    val instance: ApiService by lazy {
        refreshSettings()
        val retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .client(okHttpClient)
            .build()
        Log.d("ApiService", "BAse URL: $BASE_URL ")
        retrofit.create(ApiService::class.java)
    }

    private fun refreshSettings() {
        BASE_URL = SettingDaoHelper.AppSettings.apiUrl.getValue().toString()
        TOKEN = SettingDaoHelper.AppSettings.apiToken.getValue()
    }
    fun setApiToken(token: String) {
        TOKEN = token
    }

    fun setApiUrl(apiUrl: String) {
        BASE_URL = apiUrl

    }
    fun getApiToken(): String {
        return TOKEN
    }

}