package com.spacelabs.app.database.objectbox.helpers

import android.util.Log
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.objectbox.boxes.AlarmData
import com.spacelabs.app.database.objectbox.boxes.AlarmEventData
import com.spacelabs.app.database.objectbox.dao.AlarmBoxDao
import java.time.Duration
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.UUID

class AlarmBoxDaoHelper {

    private val alarmBoxDao: AlarmBoxDao = AlarmBoxDao()

    companion object {
        private var isHrAlarmUpdated = false
        private var isRrAlarmUpdated = false
        private var isSpo2AlarmUpdated = false
        private var isTempAlarmUpdated = false
        private var isBpSysAlarmUpdated = false
        private var isBpDiaAlarmUpdated = false
    }

    enum class States(val state: String) {
        ALARM_START("START"),
        ALARM_INTERIM("INTERIM"),
        ALARM_END("END")
    }

    private fun getDateDifferenceAsDuration(endTime: String, startTime: String): Long {
        val sTime = LocalDateTime.parse(startTime, DateTimeFormatter.ISO_DATE_TIME)
        val eTime = LocalDateTime.parse(endTime, DateTimeFormatter.ISO_DATE_TIME)

        val duration = Duration.between(eTime, sTime)
        return duration.toMillis()
    }

    fun checkAndUpdateAllAlarms(){
        if(CommonDataArea.chestSensor == null && CommonDataArea.limbSensor == null && CommonDataArea.bpSensor == null)
            return
        try {
            isHrAlarmUpdated = saveAndUpdateAlarmEvent(
                CommonDataArea.hrAlarmDao,
                isHrAlarmUpdated,
                CommonDataArea.HR.toDouble()
            )
            isRrAlarmUpdated = saveAndUpdateAlarmEvent(
                CommonDataArea.respAlarmDao,
                isRrAlarmUpdated,
                CommonDataArea.RR.toDouble()
            )
            isSpo2AlarmUpdated = saveAndUpdateAlarmEvent(
                CommonDataArea.spo2AlarmDao,
                isSpo2AlarmUpdated,
                CommonDataArea.spo2
            )
            isTempAlarmUpdated = saveAndUpdateAlarmEvent(
                CommonDataArea.tempAlarmDao,
                isTempAlarmUpdated,
                CommonDataArea.temperature
            )
            isBpSysAlarmUpdated = saveAndUpdateAlarmEvent(
                CommonDataArea.bpSysAlarmDao,
                isBpSysAlarmUpdated,
                CommonDataArea.BpSys
            )
            isBpDiaAlarmUpdated = saveAndUpdateAlarmEvent(
                CommonDataArea.bpDiaAlarmDao,
                isBpDiaAlarmUpdated,
                CommonDataArea.BpDia
            )
        } catch (e: Exception) {
            LogWriter.writeExceptLog("AlarmEventUpdate", e.stackTraceToString())
        }
    }

    private fun saveAndUpdateAlarmEvent(alarmParamDao: AlarmParametersDao, isUpdated: Boolean, value: Double): Boolean{
        var isAlarmUpdated = isUpdated
        if(alarmParamDao.alarmPrefix.isNotEmpty()){
            if(alarmParamDao.isBlinking) {
                isAlarmUpdated = when {
                    !isAlarmUpdated -> {
                        saveAndUpdateAlarmToDb(
                            alarmParamDao,
                            value
                        )
                        true
                    }
                    else -> {
                        addAlarmEventToDb(alarmParamDao.alarmPrefix, value)
                        true
                    }
                }
            } else if(isAlarmUpdated) {
                updateDbOnAlarmEnd(alarmParamDao.alarmPrefix, value)
                isAlarmUpdated = false
            }
        }
        return isAlarmUpdated
    }

    private fun saveAndUpdateAlarmToDb(alarmParamDao: AlarmParametersDao, alarmCauseValue: Double?): Long{
        var id = 0L
        try{
            val alarmName = alarmParamDao.alarmPrefix
            val patientId = CommonDataArea.PATIENT.patientId
            try{
                id = if(alarmParamDao.patientAlarmId != null) {
                    setupAlarmAndSaveToDb(alarmName, alarmCauseValue,
                        patientId, alarmParamDao.patientAlarmId, null)
                } else {
                    setupAlarmAndSaveToDb(alarmName, alarmCauseValue, patientId, null, alarmParamDao.defaultAlarmId)
                }
            } catch(e: Exception) {
                Log.d("saveAndUpdateAlarm", e.stackTraceToString())
                LogWriter.writeExceptLog("OnAlarmEventEntry", e.stackTraceToString())
            }
        } catch(e: Exception) {
            LogWriter.writeExceptLog("AlarmSaveEvent", e.stackTraceToString())
        }
        return id
    }

    private fun setupAlarmAndSaveToDb(alarmName: String, alarmCauseValue: Double?, patientId: Long, patientAlarmId: Int?, defaultAlarmId: Int?): Long {
        var id = 0L
        val uuid = generateUUID()
        val exceededAlarmValue: Double = getExceededAlarmValue(alarmName)
        val currentTimeMillis = TimestampUtils.getCurrentTimeMillis()
        if(alarmCauseValue != null) {
            val alarm = AlarmData(
                null,
                uuid,
                patientId,
                defaultAlarmId,
                patientAlarmId?.toLong(),
                alarmName,
                exceededAlarmValue,
                getCurrentTime(),
                null,
                null,
                0,
                null,
                0,
                currentTimeMillis
            )
            id = alarmBoxDao.insertOrUpdateAlarmData(alarm)
            setupAlarmEventAndSaveToDb(id, alarmCauseValue, States.ALARM_START)
        }
        return id
    }

    private fun setupAlarmEventAndSaveToDb(alarmId: Long, value: Double?, alarmState: States): Long {
        if(value == null || value.isNaN()) return 0L
        val currentTimeMillis = TimestampUtils.getCurrentTimeMillis()
        val alarmEvent = AlarmEventData(
            null,
            generateUUID(),
            alarmId,
            value,
            getCurrentTime(),
            alarmState.state,
            0,
            null,
            currentTimeMillis
        )
        return alarmBoxDao.insertOrUpdateAlarmEventData(alarmEvent)
    }

    private fun addAlarmEventToDb(alarmName: String, value: Double?): Long {
        if(value == null)
            return 0

        var id = 0L
        try{
            val alarmId = alarmBoxDao.getLastEnteredAlarmIdByAlarmName(alarmName)
            id = enterValueIfHasChange(alarmId, value)
        } catch (ex: Exception){
            LogWriter.writeExceptLog("AlarmSaveEvent", ex.stackTraceToString())
        }
        return id
    }

    private fun updateDbOnAlarmEnd(alarmName: String, value: Double?) {
        val causeValue = value ?: 0.0
        try{
            val endTime = getCurrentTime()
            try{
                if(value != null){
                    val lastEnteredAlarm = alarmBoxDao.getLastEnteredAlarmByAlarmName(alarmName) ?: return
                    val duration = getDateDifferenceAsDuration(lastEnteredAlarm.startTime, endTime)
                    lastEnteredAlarm.endTime = endTime
                    lastEnteredAlarm.duration = duration
                    alarmBoxDao.insertOrUpdateAlarmData(lastEnteredAlarm)
                    setupAlarmEventAndSaveToDb(lastEnteredAlarm.alarmId!!, causeValue, States.ALARM_END)
                }
            } catch(ex: Exception) {
                Log.e("UpdateAlarmEvent", ex.stackTraceToString())
            }
        } catch (ex: Exception){
            LogWriter.writeExceptLog("AlarmSaveEvent", ex.stackTraceToString())
        }
    }

    private fun enterValueIfHasChange(alarmId: Long, value: Double) :Long {
        var id = 0L
        val lastEnteredEventValue = alarmBoxDao.getLastEnteredEventValueByAlarmId(alarmId)
        if(value.toInt() != lastEnteredEventValue.toInt()) {
            id = setupAlarmEventAndSaveToDb(alarmId, value, States.ALARM_INTERIM)
        }
        return id
    }

    private fun getExceededAlarmValue(alarmName: String): Double{
        var exceededAlarmValue = 0.0
        when (alarmName) {
            "HIGH ECG" -> exceededAlarmValue = CommonDataArea.hrAlarmDao.highValue
            "LOW ECG" -> exceededAlarmValue = CommonDataArea.hrAlarmDao.lowValue
            "EXTREME HIGH ECG" -> exceededAlarmValue = CommonDataArea.hrAlarmDao.extremeHighValue
            "EXTREME LOW ECG" -> exceededAlarmValue = CommonDataArea.hrAlarmDao.extremeLowValue
            "HIGH RESP" -> exceededAlarmValue = CommonDataArea.respAlarmDao.highValue
            "LOW RESP" -> exceededAlarmValue = CommonDataArea.respAlarmDao.lowValue
            "HIGH SpO2" -> exceededAlarmValue = CommonDataArea.spo2AlarmDao.highValue
            "LOW SpO2" -> exceededAlarmValue = CommonDataArea.spo2AlarmDao.lowValue
            "HIGH TEMP_SKIN" -> exceededAlarmValue = CommonDataArea.tempAlarmDao.highValue
            "LOW TEMP_SKIN" -> exceededAlarmValue = CommonDataArea.tempAlarmDao.lowValue
            "HIGH BP_SYS" -> exceededAlarmValue= CommonDataArea.bpSysAlarmDao.highValue
            "LOW BP_SYS" -> exceededAlarmValue= CommonDataArea.bpSysAlarmDao.lowValue
            "HIGH BP_DIA" -> exceededAlarmValue=CommonDataArea.bpDiaAlarmDao.highValue
            "LOW BP_DIA" -> exceededAlarmValue=CommonDataArea.bpDiaAlarmDao.lowValue
        }
        return exceededAlarmValue
    }

    private fun generateUUID(): String {
        val uuid = UUID.randomUUID()
        return uuid.toString()
    }

    private fun getCurrentTime(): String{
        val currentTime = OffsetDateTime.now()
        val timeZone = ZoneOffset.UTC

        val usTime = currentTime.atZoneSameInstant(timeZone)
        return usTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }

    fun getPendingAlarmsToSend(): List<AlarmBoxDao.AlarmDataWithEvents> {
        return alarmBoxDao.getAlarmsAndEventsForPatientWithPendingUpload()
    }

    fun updateAlarmEventUploadStatusByUuid(event: AlarmEventData? = null, uuId: String = String(), status: Int) {
        try {
            val alarmEvent = event ?: alarmBoxDao.getAlarmEventByUuId(uuId) ?: return
            alarmEvent.uploadStatus = status
            alarmEvent.uploadTime = getCurrentTime()
            alarmBoxDao.updateAlarmEventUploadStatus(alarmEvent, status)
        } catch (ex: Exception) {
            Log.d("UpdateMeasurementUploadStatusByUuid", ex.stackTraceToString())
        }
    }
}