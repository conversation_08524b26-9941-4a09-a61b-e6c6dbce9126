package com.spacelabs.app.alarms.alarmDAO

import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.github.mikephil.charting.charts.LineChart
import com.google.android.material.card.MaterialCardView

data class ParameterViewsDao(
    val paramView: TextView,
    val paramOuter: MaterialCardView?,
    val paramChartOuter: MaterialCardView?,
    val highRangeView: TextView? = null,
    val lowRangeView: TextView? = null,
    val connectionAlertTextView: TextView? = null,
    val chart: LineChart? = null,

    var alarmBox: LinearLayout? = null,
    var alarmTextView: TextView? = null,
    var alarmPrefixView: TextView? = null,
    var timerTextView: TextView? = null,
    var currentTimeTextView:TextView?=null
)