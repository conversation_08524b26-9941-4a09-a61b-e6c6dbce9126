package com.spacelabs.app.activities.commonActivities.activityUi

import android.annotation.SuppressLint
import android.graphics.drawable.Animatable2
import android.graphics.drawable.AnimatedImageDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.provider.Settings
import android.text.Html
import android.text.Spannable
import android.util.Log
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Spinner
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatButton
import androidx.core.view.isVisible
import com.spacelabs.app.MainActivity
import com.spacelabs.app.R
import com.spacelabs.app.activities.iomtActivities.AccountConfigActivity
import com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
import com.spacelabs.app.activities.iomtActivities.QrScannerActivity
import com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
import com.spacelabs.app.activities.commonActivities.WifiConfigActivity
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.activities.commonActivities.helpers.WifiConfigActivityHelper
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.iomt.IomtApiManager
import kotlinx.coroutines.launch

class WifiConfigActivityUi(wifiConfigActivity: WifiConfigActivity): ActivityUiController(wifiConfigActivity) {

    private val wifiActivity: WifiConfigActivity
    init {
        this.wifiActivity = wifiConfigActivity
    }

    private lateinit var wifiSpinner: Spinner
    private lateinit var password: EditText
    private lateinit var nextButton: AppCompatButton
    private lateinit var wifiLoadingIcon: ImageView
    private lateinit var animationText: TextView
    private lateinit var animationView: LinearLayout
    private lateinit var skipButton: AppCompatButton

    private lateinit var wifiAdapter: ArrayAdapter<String>
    private lateinit var animatedDrawable: AnimatedImageDrawable

    private val discoveredWifiSSid = mutableListOf<String>()
    private val selectANetworkString = "Select a Network"
    private var wifiSsid = String()
    private var wifiPassword = String()

    override fun initUi(){
        wifiSpinner = wifiActivity.findViewById(R.id.wifiSpinner)
        password = wifiActivity.findViewById(R.id.password)
        nextButton = wifiActivity.findViewById(R.id.nextButton)

        wifiLoadingIcon = wifiActivity.findViewById(R.id.wifiLoading)
        animationView = wifiActivity.findViewById(R.id.animationView)
        animationText = wifiActivity.findViewById(R.id.animationText)
        skipButton = wifiActivity.findViewById(R.id.skipBtn)
    }

    @SuppressLint("HardwareIds")
    @RequiresApi(Build.VERSION_CODES.P)
    override fun postInitUiActions() {
        startScanningAnimation("searching WiFi...")

        CommonDataArea.ANDROID_ID = Settings.Secure.getString(wifiActivity.contentResolver, Settings.Secure.ANDROID_ID)
        wifiAdapter = ArrayAdapter(context, R.layout.spinner_content, discoveredWifiSSid)
        wifiAdapter.setDropDownViewResource(R.layout.spinner_textview_dropdown_item)
        wifiSpinner.adapter = wifiAdapter
        manageWifiList(selectANetworkString, WifiConfigActivityHelper.Operation.ADD, 0)
    }

    override fun uiActionListeners(){
        animationView.setOnClickListener {
            wifiActivity.wifiManager.startScan()
        }

        nextButton.setOnClickListener {
            onNextButtonClickAction()
        }

        wifiSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener{
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long
            ) {
                onWifiSpinnerItemSelectedAction(position, parent)
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {
                onWifiSpinnerNothingSelectedAction()
            }
        }

        skipButton.setOnClickListener {
            checkSettingsAndNavigate()
        }
    }

    private fun onNextButtonClickAction(){
        wifiPassword = password.text.toString()
        if(wifiPassword.isEmpty())
            showToast("Password cannot be empty!")
        else{
            wifiActivity.wifiConfigActivityHelper.saveWifiSettingsAndConnectNetwork(wifiSsid, wifiPassword)
        }
        nextButton.isEnabled = false
    }

    private fun onWifiSpinnerItemSelectedAction(position: Int, parent: AdapterView<*>?){
        wifiSsid = parent?.adapter?.getItem(position).toString()
        if(wifiSsid == selectANetworkString)
            return

        password.visibility = View.VISIBLE
    }

    private fun onWifiSpinnerNothingSelectedAction(){
        manageWifiList(selectANetworkString, WifiConfigActivityHelper.Operation.ADD, 0)
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private fun startScanningAnimation(animText: String){
        uiScope.launch {
            animationView.visibility = View.VISIBLE
            wifiSpinner.isEnabled = false
            animationText.text = Html.fromHtml(animText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            animatedDrawable = wifiLoadingIcon.drawable as AnimatedImageDrawable
            animatedDrawable.repeatCount = 100
            animatedDrawable.start()

            animatedDrawable.registerAnimationCallback(object : Animatable2.AnimationCallback() {
                override fun onAnimationEnd(drawable: Drawable) {
                    // Animation ended, restart it
                    if(animationView.isVisible)
                        animatedDrawable.start()

                }
            })
        }
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private fun stopScanningAnimation(){
        uiScope.launch {
            wifiSpinner.isEnabled = true
            if(animationView.isVisible) {
                animationText.text = Html.fromHtml(String(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                animatedDrawable.stop()

                animationView.visibility = View.GONE
            }
        }
    }

    fun manageWifiList(ssid: String, operation: WifiConfigActivityHelper.Operation, position: Int?){
        when(operation){
            WifiConfigActivityHelper.Operation.ADD -> {
                if(!discoveredWifiSSid.contains(ssid)){
                    if(position != null)
                        discoveredWifiSSid.add(position, ssid)
                    else
                        discoveredWifiSSid.add(ssid)
                }
            }
            WifiConfigActivityHelper.Operation.REMOVE -> discoveredWifiSSid.remove(ssid)
        }
        notifyWifiAdapter()
    }

    private fun notifyWifiAdapter(){
        uiScope.launch {
            wifiAdapter.notifyDataSetChanged()
        }
    }

    private fun checkSettingsAndNavigate() {
        try{
            val nextActivity: Class<out AppCompatActivity> = if (enableSnsApi) {
                val hasAuthenticated = SettingDaoHelper.AppSettings.HasAuthenticated.getValue()
                val accountId = SettingDaoHelper.AppSettings.AccountId.getValue()

                // Reset authentication if AccountId is empty but HasAuthenticated is true
                // This ensures users see the QR code when they haven't completed the full flow
                if (hasAuthenticated == "true" && accountId.isEmpty()) {
                    Log.d("WIFI_NAVIGATION_DEBUG", "checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty")
                    val settingDaoHelper = SettingDaoHelper(wifiActivity)
                    settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.HasAuthenticated, "")
                    AccountConfigActivitySns::class.java
                } else if (hasAuthenticated.isEmpty()) {
                    AccountConfigActivitySns::class.java
                } else if (accountId.isEmpty()) {
                    QrScannerActivitySns::class.java
                } else {
                    MainActivity::class.java
                }
            }else{
                if (IomtApiManager.DEVICE_EXIST.isEmpty()) AccountConfigActivity::class.java else if (IomtApiManager.ACCOUNT_ID.isEmpty()) QrScannerActivity::class.java else MainActivity::class.java
            }
            Log.d("WIFI_NAVIGATION_DEBUG", "checkSettingsAndNavigate: enableSnsApi=$enableSnsApi")
            Log.d("WIFI_NAVIGATION_DEBUG", "checkSettingsAndNavigate: HasAuthenticated='${SettingDaoHelper.AppSettings.HasAuthenticated.getValue()}'")
            Log.d("WIFI_NAVIGATION_DEBUG", "checkSettingsAndNavigate: AccountId='${SettingDaoHelper.AppSettings.AccountId.getValue()}'")
            Log.d("WIFI_NAVIGATION_DEBUG", "checkSettingsAndNavigate: Target activity=${nextActivity.simpleName}")
            navigateToNextActivity(nextActivity)
        } catch (ex:Exception) {
            //LogWriter.writeExceptLog("checkSettingsAndNavigate: ", ex.stackTraceToString())
            Log.e("checkSettingsAndNavigate Wifi: ", ex.stackTraceToString())
        }
    }

    inner class UiEventHandler: UiEventCallback {
        @RequiresApi(Build.VERSION_CODES.P)
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            when(event){
                UiEventCallback.UiEventType.NavigateToNext -> checkSettingsAndNavigate()
                UiEventCallback.UiEventType.StartScanningAnimation -> startScanningAnimation(eventData as String)
                UiEventCallback.UiEventType.StopScanningAnimation -> stopScanningAnimation()
                else -> Log.d("UiEvent", "WIFI_UNKNOWN EVENT -> $event")
            }
        }
    }
}