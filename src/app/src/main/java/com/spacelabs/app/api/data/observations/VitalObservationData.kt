package com.spacelabs.app.api.data.observations

import com.google.gson.Gson
import com.spacelabs.app.api.data.dataclasses.VitalObservationBean
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.iomt.IomtApiManager
import org.json.JSONObject

class VitalObservationData : ObservationManager() {

    data class Observation(
        val resourceType: String,
        val id: String,
        val metaProfile: String ,
        val status: String,
        val category: List<Category>,
        val subject: Subject,
        val effectiveDateTime: String,
        val performer: List<Performer>,
        val device: Device,
        val component: List<Component>
    )

    data class Category(
        val coding: List<Coding>
    )

    data class Coding(
        val system: String,
        val code: String,
        val display: String
    )

    data class Subject(
        val reference: String
    )

    data class Performer(
        val reference: String,
        val display: String
    )

    data class Component(
        val code: Code,
        val valueQuantity: ValueQuantity
    )

    data class Code(
        val coding: List<Coding>
    )

    data class ValueQuantity(
        val value: String,
        val unit: String,
        val system: String,
        val code: String
    )
    data class Device(
        val reference: String,
        val display: String
    )
    fun getObservation(observationData: VitalObservationBean): JSONObject {
        val components = observationData.components.map { component ->
            Component(
                code = Code(
                    coding = listOf(
                        Coding(
                            system = component.system,
                            code = component.code,
                            display = component.display
                        )
                    )
                ),
                valueQuantity = ValueQuantity(
                    value = component.value,
                    unit = component.unit,
                    system = component.valueQuantitySystem,
                    code = component.valueQuantityCode
                )
            )
        }

        val observation = Observation(
            resourceType = observationData.resourceType,
            id = observationData.uuId,
            metaProfile = "http://hl7.org/fhir/StructureDefinition/vitalsigns",
            status = observationData.status,
            category = listOf(
                Category(
                    coding = listOf(
                        Coding(
                            system = hl7SysValue,
                            code = observationData.categoryCode,
                            display = observationData.categoryDisplay
                        )
                    )
                )
            ),
            subject = Subject(reference = "${observationData.subjectReference}/${CommonDataArea.PATIENT.patientId1}"),
            effectiveDateTime = observationData.timestamp,
            performer = listOf(Performer(reference = "Practitioner/f005", display = "A. Langeveld")),
            device =  Device("Device/${IomtApiManager.DEVICE_UUID}",observationData.categoryDisplay),
            component = components
        )

        val json = Gson().toJson(observation)
        return JSONObject(json)
    }
}
