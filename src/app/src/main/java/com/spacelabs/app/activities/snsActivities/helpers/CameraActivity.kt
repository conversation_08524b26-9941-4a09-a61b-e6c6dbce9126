package com.spacelabs.app.activities.snsActivities.helpers

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.util.Size
import androidx.appcompat.app.AppCompatActivity
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.BarcodeScanner
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.common.InputImage
import com.spacelabs.app.R
import com.spacelabs.app.common.CommonDataArea
import java.util.concurrent.Executors

class CameraActivity : AppCompatActivity() {

    private lateinit var previewView: PreviewView
    private val TAG = "Qr_Scanner_Activity_Sns"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_camera)

        previewView = findViewById(R.id.previewView)

        // Set emulator flag if not already set
        if (!CommonDataArea.runModeEmulator) {
            CommonDataArea.runModeEmulator = isRunningOnEmulator()
        }

        Log.d(TAG, "Emulator detection result: ${CommonDataArea.runModeEmulator}")
        Log.d(TAG, "Build info - FINGERPRINT: ${Build.FINGERPRINT}, MODEL: ${Build.MODEL}, MANUFACTURER: ${Build.MANUFACTURER}")

        startCamera()
    }

    /**
     * Detects if the app is running on an Android emulator
     * Uses multiple detection methods for better accuracy
     */
    private fun isRunningOnEmulator(): Boolean {
        return (Build.FINGERPRINT.startsWith("generic") ||
                Build.FINGERPRINT.startsWith("unknown") ||
                Build.MODEL.contains("google_sdk") ||
                Build.MODEL.contains("Emulator") ||
                Build.MODEL.contains("Android SDK built for x86") ||
                Build.MANUFACTURER.contains("Genymotion") ||
                Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic") ||
                "google_sdk" == Build.PRODUCT ||
                Build.HARDWARE.contains("goldfish") ||
                Build.HARDWARE.contains("ranchu"))
    }

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        cameraProviderFuture.addListener(Runnable {
            val cameraProvider = cameraProviderFuture.get()
            val preview = Preview.Builder().build().also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }

            val imageAnalysis = ImageAnalysis.Builder()
                .setTargetResolution(Size(1280, 720))
                .build()
                .also {
                    it.setAnalyzer(Executors.newSingleThreadExecutor(), BarcodeAnalyzer())
                }

            // Try back camera first, fall back to front camera if not available
            var cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
            var cameraDescription = "back camera"

            if (CommonDataArea.runModeEmulator) {
                Log.d(TAG, "Running on emulator - trying back camera first for external webcam QR scanning")
            } else {
                Log.d(TAG, "Running on physical device - trying back camera for QR scanning")
            }

            try {
                cameraProvider.unbindAll()
                cameraProvider.bindToLifecycle(
                    this as LifecycleOwner, cameraSelector, preview, imageAnalysis)
                Log.d(TAG, "Camera binding successful with $cameraDescription")
            } catch (e: Exception) {
                Log.e(TAG, "Use case binding failed with $cameraDescription", e)

                // If back camera fails, try front camera
                if (cameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
                    Log.d(TAG, "Back camera failed, trying front camera as fallback")
                    cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA
                    cameraDescription = "front camera"

                    try {
                        cameraProvider.unbindAll()
                        cameraProvider.bindToLifecycle(
                            this as LifecycleOwner, cameraSelector, preview, imageAnalysis)
                        Log.d(TAG, "Camera binding successful with $cameraDescription (fallback)")
                    } catch (e2: Exception) {
                        Log.e(TAG, "Both cameras failed - back camera and front camera", e2)
                        Log.e(TAG, "Camera hardware not available in emulator. Please configure cameras in AVD settings.")
                        Log.e(TAG, "Instructions: AVD Manager > Edit AVD > Advanced Settings > Set Front/Back Camera to 'Webcam0' or 'Emulated'")

                        // Provide more specific error information
                        when {
                            e2.message?.contains("camera") == true -> {
                                Log.e(TAG, "Camera-specific error: ${e2.message}")
                            }
                            e2.message?.contains("permission") == true -> {
                                Log.e(TAG, "Permission-related error: ${e2.message}")
                            }
                            else -> {
                                Log.e(TAG, "General camera binding error: ${e2.message}")
                            }
                        }

                        // Close this activity since camera is not available
                        finish()
                    }
                } else {
                    // Provide more specific error information for original failure
                    when {
                        e.message?.contains("camera") == true -> {
                            Log.e(TAG, "Camera-specific error: ${e.message}")
                        }
                        e.message?.contains("permission") == true -> {
                            Log.e(TAG, "Permission-related error: ${e.message}")
                        }
                        else -> {
                            Log.e(TAG, "General camera binding error: ${e.message}")
                        }
                    }
                }
            }
        }, ContextCompat.getMainExecutor(this))
    }

    private inner class BarcodeAnalyzer : ImageAnalysis.Analyzer {
        @ExperimentalGetImage
        override fun analyze(imageProxy: ImageProxy) {
            val mediaImage = imageProxy.image
            if (mediaImage != null) {
                val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
                val scanner: BarcodeScanner = BarcodeScanning.getClient()
                scanner.process(image)
                    .addOnSuccessListener { barcodes ->
                        if (barcodes.isNotEmpty()) {
                            Log.d(TAG, "Found ${barcodes.size} barcode(s)")
                        }
                        for (barcode in barcodes) {
                            val rawValue = barcode.rawValue
                            val displayValue = barcode.displayValue
                            val result = rawValue ?: displayValue
                            result?.let {
                                Log.d(TAG, "QR Code scanned successfully: ${it.take(50)}...")
                                val data = Intent().apply {
                                    putExtra("SCAN_RESULT", it)
                                }
                                setResult(RESULT_OK, data)
                                finish()
                            }
                            imageProxy.close()
                            return@addOnSuccessListener
                        }
                        imageProxy.close()
                    }
                    .addOnFailureListener { e ->
                        Log.e(TAG, "Barcode scanning failed", e)
                        imageProxy.close()
                    }
                    .addOnCompleteListener {
                        imageProxy.close()
                    }
            }
        }
    }
}
