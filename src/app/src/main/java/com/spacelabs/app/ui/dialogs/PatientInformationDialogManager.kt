package com.spacelabs.app.ui.dialogs

import android.app.AlertDialog
import android.app.DatePickerDialog
import android.app.Dialog
import android.content.Context
import android.text.Html
import android.text.Spannable
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.widget.AppCompatButton

import com.spacelabs.app.R
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.MainActivity
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.daoHelper.PatientDaoHelper
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.database.dataClearance.DataDeletionHandler
import com.spacelabs.app.database.entities.PatientTable
import com.spacelabs.app.ui.Dialogs
import com.tobiasschuerg.prefixsuffix.PrefixSuffixEditText
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.util.*

class PatientInformationDialogManager(mainActivity: MainActivity): DialogManager(mainActivity) {

    //UI Variables
    private lateinit var patientId: EditText
    private lateinit var patientId2: EditText
    private lateinit var firstName: EditText
    private lateinit var lastName: EditText
    private lateinit var weight: EditText
    private lateinit var height: EditText

    private lateinit var gender: Spinner

    private lateinit var age: TextView
    private lateinit var dob: TextView

    private lateinit var discharge: AppCompatButton
    private lateinit var updateBtn: AppCompatButton

    private lateinit var closeBtn: Button

    //Normal Variable
    private var patient: PatientTable? = null

    private var genderVal = ""
    private var yrs: Int = 0
    private var id = 0

    private lateinit var settingDaoHelper: SettingDaoHelper
    private lateinit var dataDeletionHandler: DataDeletionHandler

    private val patientDaoHelper = PatientDaoHelper(mainActivity)

    override fun onCreateDialogActions(context: Context, dialog: Dialog, layout: Int) {
        Log.d("OnCreateDialogActions:", "Inside PatientHelper's onCreate")
    }

    override fun initUIs(dialog: Dialog) {
        patientId = dialog.findViewById(R.id.patientId)
        patientId2 = dialog.findViewById(R.id.patientId2)
        firstName = dialog.findViewById(R.id.firstName)
        lastName = dialog.findViewById(R.id.lastName)
        gender = dialog.findViewById(R.id.gender)
        age = dialog.findViewById(R.id.age)
        weight = dialog.findViewById(R.id.weight)
        height = dialog.findViewById(R.id.height)
        dob = dialog.findViewById(R.id.dob)
        discharge = dialog.findViewById(R.id.discharge)
        closeBtn = dialog.findViewById(R.id.closeBtn)
        updateBtn = dialog.findViewById(R.id.updateBtn)
        settingDaoHelper = SettingDaoHelper(context)
        dataDeletionHandler = DataDeletionHandler(context)
    }

    override fun postUiInitActions(context: Context) {
        firstName.filters = arrayOf(Dialogs.EmojiExcludeFilter())
        lastName.filters = arrayOf(Dialogs.EmojiExcludeFilter())

        val spinnerContent = R.layout.spinner_content

        ArrayAdapter.createFromResource(context, R.array.gender, spinnerContent).also{
                adapter ->
            // Specify the layout to use when the list of choices appears
            adapter.setDropDownViewResource(R.layout.spinner_dropdown_item)
            // Apply the adapter to the spinner
            gender.adapter = adapter
        }

        getCurrentPatientAndUpdateViews()
    }

    override fun uiActionListeners(context: Context, dialog: Dialog) {
        val setListener =
            DatePickerDialog.OnDateSetListener { _, year, month, dayOfMonth ->
                val mnt = month + 1
                val dateOfBirth = LocalDate.parse("$year-${String.format("%02d",mnt)}-${String.format("%02d",dayOfMonth)}")
                val currentDate = LocalDate.now()
                yrs = Period.between(dateOfBirth, currentDate).years
                Log.d("YEAR","----------------$yrs----------------")
                age.text = Html.fromHtml("$yrs yrs", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                val date = "${getMonthName(mnt)} - $dayOfMonth - $year"
                dob.text = date
            }

        dob.setOnClickListener {
            val calendar = getCalendarWithGivenDate(patient?.dob)

            val yr = calendar[Calendar.YEAR]
            val month = calendar[Calendar.MONTH]
            val day = calendar[Calendar.DAY_OF_MONTH]
            val datePickerDialog = DatePickerDialog(context,R.style.customDatePickerDialog, setListener, yr, month, day)
            datePickerDialog.datePicker.maxDate = TimestampUtils.getCurrentTimeMillis()
            datePickerDialog.show()
        }

        gender.onItemSelectedListener = object : AdapterView.OnItemSelectedListener{
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long
            ) {
                if(!(parent?.getItemAtPosition(position)?.equals("Gender")!!))
                    genderVal = parent.getItemAtPosition(position).toString()
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {
                gender.setSelection(-1)
            }
        }

        gender.isEnabled = true
       // gender.isClickable = false  //Comment this when you want to make the field Editable
       // gender.isFocusable = false  //Comment this when you want to make the field Editable

        updateBtn.setOnClickListener{
            onPatientUpdateButtonClickAction(context, patient)
            closeDialog(dialog)
        }

        discharge.setOnClickListener {
            onDischargeButtonClickAction(context, dialog)
        }

        closeBtn.setOnClickListener {
            closeDialog(dialog)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun getCurrentPatientAndUpdateViews(){
        GlobalScope.launch {
            val currentPatient = patientDaoHelper.getActivePatient()
            if(currentPatient != null)
                setPatientInfoOnViews(currentPatient)
        }
    }

    private fun setPatientInfoOnViews(currentPatient: List<PatientTable>){
        mainActivityContext.uiScope.launch {
            if(currentPatient.isNotEmpty()){
                patient = currentPatient[0]
                if(patient != null && patient!!.isTempPatient == 0){
                    id = patient?.patientId!!
                    patientId.setText(patient?.patientID1)
                    patientId2.setText(if(patient?.patientID2 != null) patient?.patientID2 else null)
                    firstName.setText(patient?.firstName)
                    lastName.setText(patient?.lastName)
                    when(patient?.gender){
                        "Unknown" -> gender.setSelection(0)
                        "Male" -> gender.setSelection(1)
                        "Female" -> gender.setSelection(2)
                        "Others" -> gender.setSelection(3)
                    }// = Html.fromHtml(patient.gender, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    age.text = Html.fromHtml("${patient?.age} yrs", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    weight.setText(if(patient?.weight != null)patient?.weight.toString() else null)
                    height.setText(if(patient?.height != null)patient?.height.toString() else null)
                    dob.text = Html.fromHtml(patient?.dob, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    discharge.visibility = View.VISIBLE
                }else{
                    patientId.isEnabled = true
                    patientId2.isEnabled = true
                    discharge.visibility = View.INVISIBLE
                }
            }
        }
    }

    private fun resetUiAndSensorOnDischarge(context: Context){
        if(CommonDataArea.chestSensor == null || CommonDataArea.chestSensor?.isConnected!!) {
            dialogHelper.resetSensorConfiguration(
                context,
                CommonDataArea.chestSensor,
                SensorType.CHEST
            )

        }
        if(CommonDataArea.limbSensor == null || CommonDataArea.limbSensor?.isConnected!!) {
            dialogHelper.resetSensorConfiguration(
                context,
                CommonDataArea.limbSensor,
                SensorType.LIMB
            )
        }
        if(CommonDataArea.bpSensor == null||CommonDataArea.bpSensor?.isConnected!!){
            dialogHelper.resetBpSensorConfiguration(context, CommonDataArea.bpSensor)
        }

    }

    private fun onDischargeButtonClickAction(context: Context, dialog: Dialog){
        val builder = AlertDialog.Builder(context)
        builder.setMessage("Are you sure, you want to discharge this patient?")
            .setCancelable(false)
            .setPositiveButton("Yes"){ alertDialog, _ ->
                resetUiAndSensorOnDischarge(context)
                patientDaoHelper.setAllPatientsToInActive()
                settingDaoHelper.resetPersonalSettingsOnDischarge()
                dataDeletionHandler.deleteDBDataOnDischarge()
                CommonDataArea.isAlertShowing = false
                alertDialog.dismiss()
                closeDialog(dialog)
                Toast.makeText(context, "Patient Discharged", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("No"){ alertDialog, _ ->
                CommonDataArea.isAlertShowing = false
                alertDialog.dismiss()
            }

        val confirmDialog: AlertDialog = builder.create()
        confirmDialog.setTitle("Discharge Patient")
        if (!CommonDataArea.isAlertShowing) {
            confirmDialog.show()
            CommonDataArea.isAlertShowing = true
        }
            CommonDataArea.tempDisplaySensorType = SensorType.CHEST

    }

    private fun onPatientUpdateButtonClickAction(context: Context, patient: PatientTable?){
        patient ?: return

        CommonDataArea.PATIENT.firstName = firstName.text.toString().ifEmpty { patient.firstName }
        CommonDataArea.PATIENT.lastName = lastName.text.toString().ifEmpty { patient.lastName }
        CommonDataArea.PATIENT.patientId1 = patientId.text.toString().ifEmpty { patient.patientID1 }
        CommonDataArea.PATIENT.patientId2 = patientId2.text.toString().ifEmpty { patient.patientID2 }
        CommonDataArea.PATIENT.dob = dob.text.toString().ifEmpty { patient.dob }
        CommonDataArea.PATIENT.height = height.text.toString().toDoubleOrNull()
        val ag = age.text.toString().ifEmpty { patient.age.toString() }
        CommonDataArea.PATIENT.gender = genderVal
        CommonDataArea.PATIENT.weight = weight.text.toString().toDoubleOrNull()

        CommonDataArea.PATIENT.age = ag.split(" ")[0].toInt()

        CommonDataArea.PATIENT.patientId = patient.patientId!!.toLong()
        val newPatient: PatientTable = setupPatientWithDetails(patient)
        try{
            patientDaoHelper.updatePatientInfo(newPatient)
        } catch (ex: Exception){
            Log.d("PatientInfoUpdate", ex.stackTraceToString())
        }
        Toast.makeText(context,"Patient information updated successfully", Toast.LENGTH_SHORT).show()
    }

    private fun setupPatientWithDetails(patient: PatientTable): PatientTable {
        return PatientTable(
            patient.patientId,
            CommonDataArea.PATIENT.firstName,
            null,
            CommonDataArea.PATIENT.lastName,
            CommonDataArea.PATIENT.patientId1,
            CommonDataArea.PATIENT.patientId2,
            CommonDataArea.PATIENT.dob,
            CommonDataArea.PATIENT.age,
            CommonDataArea.PATIENT.gender,
            CommonDataArea.PATIENT.height?.toDouble(),
            CommonDataArea.PATIENT.weight?.toDouble(),
            null,
            patient.admitDate,
            null,
            patient.createdOn,
            "active",
            0
        )
    }

    private fun getMonthName(month: Int): String {
        return when(month){
            1 -> "JAN"
            2 -> "FEB"
            3 -> "MAR"
            4 -> "APR"
            5 -> "MAY"
            6 -> "JUN"
            7 -> "JUL"
            8 -> "AUG"
            9 -> "SEP"
            10 -> "OCT"
            11 -> "NOV"
            12 -> "DEC"
            else -> ""
        }
    }

    private fun getCalendarWithGivenDate(date: String?): Calendar{
        val calendar = Calendar.getInstance()
        if(date != null){
            val dateFormat = SimpleDateFormat("MMM - d - yyyy", Locale.US)
            val currentDate = dateFormat.parse(date)
            if(currentDate != null)
                calendar.time = currentDate
        }
        return calendar
    }

}