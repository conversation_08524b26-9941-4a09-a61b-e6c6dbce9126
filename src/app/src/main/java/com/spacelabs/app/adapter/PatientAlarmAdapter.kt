package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.PatientAlarmTable

class PatientAlarmAdapter(private var patientList: List<PatientAlarmTable> = emptyList()) : RecyclerView.Adapter<PatientAlarmAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_patient_alarm, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val patientAlarmId: TextView = itemView.findViewById(R.id.PatientAlarmId)
        private val patientId: TextView = itemView.findViewById(R.id.PatientId)
        private val paramId: TextView = itemView.findViewById(R.id.ParamId)
        private val alarmCategory: TextView = itemView.findViewById(R.id.AlarmCategory)
        private val alarmName: TextView = itemView.findViewById(R.id.AlarmName)
        private val value: TextView = itemView.findViewById(R.id.Value)
        private val lowValue: TextView = itemView.findViewById(R.id.LowValue)
        private val extremeLowValue: TextView = itemView.findViewById(R.id.ExtremeLowValue)
        private val highValue: TextView = itemView.findViewById(R.id.HighValue)
        private val extremeHighValue: TextView = itemView.findViewById(R.id.ExtremeHighValue)
        private val timeStamp: TextView = itemView.findViewById(R.id.TimeStamp)
        private val alarmStatus: TextView = itemView.findViewById(R.id.AlarmStatus)
        private val alarmSound: TextView = itemView.findViewById(R.id.AlarmSound)

        fun bind(patient: PatientAlarmTable) {
            patientAlarmId.text = patient.patientAlarmId.toString()
            patientId.text = patient.patientId.toString()
            paramId.text = patient.paramId.toString()
            alarmCategory.text = patient.alarmCategory
            alarmName.text = patient.alarmName
            value.text = patient.value
            lowValue.text = patient.lowValue.toString()
            extremeLowValue.text = patient.extremeLowValue.toString()
            highValue.text = patient.highValue.toString()
            extremeHighValue.text = patient.extremeHighValue.toString()
            timeStamp.text = patient.timeStamp
            alarmStatus.text = patient.alarmStatus.toString()
            alarmSound.text = patient.alarmSound.toString()
        }
    }
}

