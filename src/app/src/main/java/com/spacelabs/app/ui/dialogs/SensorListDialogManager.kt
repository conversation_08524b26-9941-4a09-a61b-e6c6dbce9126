package com.spacelabs.app.ui.dialogs

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Build
import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.View
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.appcompat.widget.AppCompatToggleButton
import androidx.appcompat.widget.SearchView
import androidx.appcompat.widget.SwitchCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.MainActivity
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.CommonDataArea.Companion.bpSensorSelected
import com.spacelabs.app.common.CommonDataArea.Companion.chestSensorSelected
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.common.CommonDataArea.Companion.isSensorRemoved
import com.spacelabs.app.common.CommonDataArea.Companion.limbSensorSelected
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.MainActivityUIHelper
import com.spacelabs.app.ui.dialogs.dialogHelper.SensorRecyclerViewAdapter
import com.spacelabs.app.sensorService
import com.spacelabs.app.ui.Dialogs
import com.spacelabs.app.ui.dialogs.dialogHelper.SensorListDialogHelper
import com.spacelabs.app.ui.dialogs.dialogHelper.SensorListItems
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SensorListDialogManager(mainActivity: MainActivity) : DialogManager(mainActivity) {

    //UI Variables
    private lateinit var searchBox: SearchView
    private lateinit var sensorList: RecyclerView

    private lateinit var saveBtn: AppCompatButton
    private lateinit var removeBtn: AppCompatButton

    private lateinit var refreshBtn: ImageButton

    private lateinit var cancelBtn: Button
    private lateinit var closeBtn: Button

    private lateinit var chestBox: AppCompatCheckBox
    private lateinit var limbBox: AppCompatCheckBox
    private lateinit var bpBox: AppCompatCheckBox

    private lateinit var chestText: TextView
    private lateinit var limbText: TextView
    private lateinit var bpText: TextView
    private lateinit var noSensorText: TextView
    private lateinit var noSensorAvailable: TextView

    private lateinit var chestBoxOuter: LinearLayout
    private lateinit var limbBoxOuter: LinearLayout
    private lateinit var bpBoxOuter: LinearLayout
    private lateinit var sensorAuthenticationCheck: SwitchCompat
    private lateinit var sensorAuthtextView: TextView
    private lateinit var toggleChest: LinearLayout
    private lateinit var toggleLimb: LinearLayout
    private lateinit var toggleBP: LinearLayout
    private lateinit var toggleChestButton: AppCompatToggleButton
    private lateinit var toggleLimbButton: AppCompatToggleButton
    private lateinit var toggleBPButton: AppCompatToggleButton


    //Normal Variables
    private lateinit var uiScope: CoroutineScope
    private lateinit var outerBoxesAndTextViews: Map<LinearLayout, TextView>
    private lateinit var checkBoxAndOuters: Map<LinearLayout, AppCompatCheckBox>
    private lateinit var checkBoxesAndSensorTypes: Map<AppCompatCheckBox, SensorType>
    private lateinit var adapter: SensorRecyclerViewAdapter
    private var isSensorAuthenticationChecked: Boolean = true
    private val progressDialogHandler = ProgressDialogHandler(context)

    @RequiresApi(Build.VERSION_CODES.S)
    override fun onCreateDialogActions(context: Context, dialog: Dialog, layout: Int) {
        sensorService.startScan(30000)
        CommonDataArea.runConnectionStatusUpdateUiHandler = true
        uiScope = dialogHelper.createUICoroutineScope()
    }

    override fun initUIs(dialog: Dialog) {
        searchBox = dialog.findViewById(R.id.searchBox)
        sensorList = dialog.findViewById(R.id.sensorsList)
        saveBtn = dialog.findViewById(R.id.connectBtn)
        cancelBtn = dialog.findViewById(R.id.cancel)
        removeBtn = dialog.findViewById(R.id.remove)
        refreshBtn = dialog.findViewById(R.id.refreshButton)
        chestBoxOuter = dialog.findViewById(R.id.chestBoxOuter)
        chestBox = dialog.findViewById(R.id.chestBox)
        chestText = dialog.findViewById(R.id.chestText)
        limbBoxOuter = dialog.findViewById(R.id.limbBoxOuter)
        limbBox = dialog.findViewById(R.id.limbBox)
        limbText = dialog.findViewById(R.id.limbText)
        bpBoxOuter = dialog.findViewById(R.id.bpBoxOuter)
        bpBox = dialog.findViewById(R.id.bpBox)
        bpText = dialog.findViewById(R.id.bpText)
        noSensorText = dialog.findViewById(R.id.noSensorText)
        closeBtn = dialog.findViewById(R.id.closeBtn)
        sensorAuthenticationCheck = dialog.findViewById(R.id.sensorAuthenticationCheck)
        sensorAuthtextView = dialog.findViewById(R.id.sensorAuthtextView)
        // Initialize toggle buttons
        toggleChest = dialog.findViewById(R.id.toggleChest)
        toggleLimb = dialog.findViewById(R.id.toggleLimb)
        toggleBP = dialog.findViewById(R.id.toggleBP)
        toggleChestButton = dialog.findViewById(R.id.toggleChestButton)
        toggleLimbButton = dialog.findViewById(R.id.toggleLimbButton)
        toggleBPButton = dialog.findViewById(R.id.toggleBPButton)
        noSensorAvailable = dialog.findViewById(R.id.noSensorAvailable)
    }


    override fun postUiInitActions(context: Context) {
        outerBoxesAndTextViews =
            mapOf(chestBoxOuter to chestText, limbBoxOuter to limbText, bpBoxOuter to bpText)
        checkBoxAndOuters =
            mapOf(chestBoxOuter to chestBox, limbBoxOuter to limbBox, bpBoxOuter to bpBox)
        checkBoxesAndSensorTypes = mapOf(
            chestBox to SensorType.CHEST,
            limbBox to SensorType.LIMB,
            bpBox to SensorType.BP2_BLOOD_PRESSURE_MONITOR
        )
        searchBox.setQuery("", false)
        searchBox.clearFocus()
        setupSearchBox(searchBox)

        adapter = SensorRecyclerViewAdapter(CommonDataArea.sensorList, this)
        sensorList.layoutManager = LinearLayoutManager(context)

        setupListView(sensorList, adapter)
        checkConnectivityAndUpdateViews(context, outerBoxesAndTextViews, noSensorText)
        if (enableSnsApi) {
            sensorAuthenticationCheck.visibility = View.GONE
            sensorAuthtextView.visibility = View.GONE
        }
        getSwitchButtonData()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun uiActionListeners(context: Context, dialog: Dialog) {
        checkBoxAndOuterActionsAndRemoveButtonVisibility(context, removeBtn, checkBoxAndOuters)

        mainActivityContext.runOnUiThread {
            while (dialog.isShowing) {
                adapter.notifyDataSetChanged()
            }
        }

        cancelBtn.setOnClickListener {
            searchBox.setQuery("", false)
            searchBox.clearFocus()
            closeDialog(dialog)
        }

        closeBtn.setOnClickListener {
            searchBox.setQuery("", false)
            searchBox.clearFocus()
            closeDialog(dialog)
            CommonDataArea.isAlertShowing = false
        }

        saveBtn.setOnClickListener {
            CommonDataArea.isAlertShowing = false
            isSensorRemoved = false
            searchBox.setQuery("", false)
            searchBox.clearFocus()
            val isActionPerformed = onSaveButtonClick(context)
            if (isActionPerformed) {
                closeDialog(dialog)
            }
        }

        removeBtn.setOnClickListener {
            isSensorRemoved = false
            sensorService.stopScan()
            dialogHelper.onRemoveBtnClick(context, checkBoxesAndSensorTypes)

            closeDialog(dialog)
            CommonDataArea.runConnectionStatusUpdateUiHandler = false
        }

        refreshBtn.setOnClickListener {
            onRefreshButtonClick(10000)
        }

        sensorAuthenticationCheck.setOnCheckedChangeListener { buttonView, isChecked ->
            saveSwitchButtonData(isChecked)
        }
        onToggleClicked(toggleChestButton)
        toggleChest.setOnClickListener { onToggleClicked(toggleChestButton) }
        toggleLimb.setOnClickListener { onToggleClicked(toggleLimbButton) }
        toggleBP.setOnClickListener { onToggleClicked(toggleBPButton) }

        dialog.findViewById<ImageView>(R.id.toggleChestPic)
            .setOnClickListener { onToggleClicked(toggleChestButton) }
        dialog.findViewById<ImageView>(R.id.toggleLimbPic)
            .setOnClickListener { onToggleClicked(toggleLimbButton) }
        dialog.findViewById<ImageView>(R.id.toggleBPPic)
            .setOnClickListener { onToggleClicked(toggleBPButton) }

        toggleChestButton.setOnClickListener { onToggleClicked(it) }
        toggleLimbButton.setOnClickListener { onToggleClicked(it) }
        toggleBPButton.setOnClickListener { onToggleClicked(it) }

        dialog.setOnDismissListener {
            Dialogs.ManageSensorDialog = null
            SimpleDialog = null
        }

    }

    private fun onToggleClicked(view: View) {
        val toggleLayouts = listOf(toggleChest, toggleLimb, toggleBP)
        // Clear previous selections
        chestSensorSelected = ""
        limbSensorSelected = ""
        bpSensorSelected = ""
        // Reset background color for all toggle layouts to their original color
        toggleLayouts.forEach {
            it.backgroundTintList = ColorStateList.valueOf(
                ContextCompat.getColor(
                    context,
                    R.color.components_gray
                )
            )// Remove any background tint
        }

        val selectedOption = when (view.id) {
            R.id.toggleChest, R.id.toggleChestPic, R.id.toggleChestButton -> {
                // Change background color for Chest toggle layout to light blue
                toggleChest.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(context, R.color.lightBlue))
                "Chest"
            }

            R.id.toggleLimb, R.id.toggleLimbPic, R.id.toggleLimbButton -> {
                // Change background color for Limb toggle layout to light blue
                toggleLimb.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(context, R.color.lightBlue))
                "Limb"
            }

            R.id.toggleBP, R.id.toggleBPPic, R.id.toggleBPButton -> {
                // Change background color for BP toggle layout to light blue
                toggleBP.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(context, R.color.lightBlue))
                "BP"
            }

            else -> ""
        }
        CommonDataArea.SelectedSensorName = selectedOption
        val filteredList = filterSensorsByType(selectedOption)
        updateNoSensorAvailableTextView(filteredList)
        adapter = SensorRecyclerViewAdapter(CommonDataArea.sensorList, this)
        sensorList.layoutManager = LinearLayoutManager(context)
        setupListView(sensorList, adapter)
    }

    private fun filterSensorsByType(sensorType: String): List<SensorListItems> {
        return CommonDataArea.sensorList.filter { sensor ->
            when (sensorType) {
                "Chest", "Limb", "BP" -> sensor.getSensorName()
                    .contains(CommonDataArea.SelectedSensorName)

                else -> false
            }
        }
    }

    private fun updateNoSensorAvailableTextView(filteredList: List<SensorListItems>) {
        if (filteredList.isEmpty()) {
            noSensorAvailable.visibility = View.VISIBLE
        } else {
            noSensorAvailable.visibility = View.GONE
        }
    }

    private fun checkBoxAndOuterActionsAndRemoveButtonVisibility(
        context: Context,
        removeBtn: AppCompatButton,
        checkBoxAndOuters: Map<LinearLayout, CheckBox>,
    ) {
        try {
            for (outerAndBoxPair in checkBoxAndOuters) {
                val outer = outerAndBoxPair.key
                val box = outerAndBoxPair.value

                outer.setOnClickListener {
                    updateVisibilityIfHasSensorConfig(context, box)
                }
                box.setOnCheckedChangeListener { _, _ ->
                    updateRemoveButtonVisibility(context, removeBtn, checkBoxAndOuters)
                }
//                box.setOnCheckedChangeListener { _, isChecked ->
//                    if (isChecked) {
//                        removeBtn.backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.red))
////                        removeBtn.visibility = View.VISIBLE
//
//                    }
//                    else{
//                        removeBtn.backgroundTintList = ColorStateList.valueOf(ContextCompat.getColor(context, R.color.components_gray))
////                        updateRemoveButtonVisibilityByCheckBoxStatus(removeBtn, checkBoxAndOuters)
//                    }
//
//                }
            }
        } catch (ex: Exception) {
            LogWriter.writeUILog("BoxOuterAndRemoveVisibilityError", ex.stackTraceToString())
        }
    }

    private fun updateRemoveButtonVisibility(
        context: Context,
        removeBtn: AppCompatButton,
        checkBoxAndOuters: Map<LinearLayout, CheckBox>,
    ) {
        var isAnyChecked = false
        for (box in checkBoxAndOuters.values) {
            if (box.isChecked) {
                isAnyChecked = true
                break
            }
        }

        if (isAnyChecked) {
            removeBtn.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.red))
        } else {
            removeBtn.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.components_gray))
        }
    }

    private fun updateVisibilityIfHasSensorConfig(context: Context, box: CheckBox) {
        val chestSensorName =
            SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.CHEST)
        val limbSensorName =
            SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.LIMB)
        val bpSensorName = SibelSensorManager.getEnrolledSensorNameByType(
            context,
            SensorType.BP2_BLOOD_PRESSURE_MONITOR
        )

        when (box.id) {
            R.id.chestBox -> {
                if (chestSensorName!!.isNotEmpty())
                    box.isChecked = !box.isChecked
            }

            R.id.limbBox -> {
                if (limbSensorName!!.isNotEmpty())
                    box.isChecked = !box.isChecked
            }

            R.id.bpBox -> {
                if (bpSensorName!!.isNotEmpty())
                    box.isChecked = !box.isChecked
            }
        }
    }

//    private fun updateRemoveButtonVisibilityByCheckBoxStatus(
//        removeBtn: AppCompatButton,
//        checkBoxAndOuter: Map<LinearLayout, CheckBox>,
//    ) {
//        for (outerAndBox in checkBoxAndOuter) {
//            val checkBox = outerAndBox.value
//            if (checkBox.isChecked) {
//                removeBtn.visibility = View.VISIBLE
//                break
//            } else
//                removeBtn.visibility = View.GONE
//        }
//    }

    private fun checkConnectivityAndUpdateViews(
        context: Context,
        outerBoxesAndTextViews: Map<LinearLayout, TextView>,
        noSensorText: TextView,
    ) {
        try {
            val chestSensorName =
                SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.CHEST)
            val limbSensorName =
                SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.LIMB)
            val bpSensorName = SibelSensorManager.getEnrolledSensorNameByType(
                context,
                SensorType.BP2_BLOOD_PRESSURE_MONITOR
            )
            mainActivityContext.uiScope.launch {
                while (CommonDataArea.runConnectionStatusUpdateUiHandler) {
                    if (chestSensorName?.isNotEmpty()!! || limbSensorName?.isNotEmpty()!! || bpSensorName!!.isNotEmpty()) {
                        noSensorText.visibility = View.GONE
                        for (outerAndText in outerBoxesAndTextViews) {
                            when (outerAndText.key.id) {
                                R.id.chestBoxOuter -> updateViewOnLhs(
                                    chestSensorName,
                                    outerAndText,
                                    CommonDataArea.chestSensor as Sensor?
                                )

                                R.id.limbBoxOuter -> updateViewOnLhs(
                                    limbSensorName,
                                    outerAndText,
                                    CommonDataArea.limbSensor as Sensor?
                                )

                                R.id.bpBoxOuter -> updateViewOnLhs(
                                    bpSensorName,
                                    outerAndText,
                                    CommonDataArea.bpSensor as Sensor?
                                )
                            }
                        }
                    } else {
                        noSensorText.visibility = View.VISIBLE
                        for (outerAndText in outerBoxesAndTextViews) {
                            outerAndText.key.visibility = View.GONE
                            outerAndText.value.visibility = View.GONE
                        }
                    }
                    delay(1000)
                }
            }
        } catch (ex: Exception) {
            LogWriter.writeUILog("LHSUIUpdate", ex.stackTraceToString())
        }
    }

    private fun updateViewOnLhs(
        sensorName: String?,
        outerAndText: Map.Entry<LinearLayout, TextView>,
        sensor: Sensor?,
    ) {
        val layout = outerAndText.key
        val textView = outerAndText.value
        if (sensorName!!.isNotEmpty()) {
            layout.visibility = View.VISIBLE
            textView.visibility = View.VISIBLE
            val prefix = getPrefixByTextViewId(textView.id)
            textView.text = Html.fromHtml("$prefix$sensorName", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            if (sensor != null && sensor.isConnected && !CommonDataArea.isAlertShowing) {
                MainActivityUIHelper().updateDrawableByBatteryStatus(textView, sensor.sensorType)
            }

        } else {

            layout.visibility = View.GONE
            textView.visibility = View.GONE
        }
    }

    private fun getPrefixByTextViewId(textId: Int): String {
        return when (textId) {
            R.id.chestText -> "Chest Sensor - "
            R.id.limbText -> "Limb Sensor - "
            R.id.bpText -> "BP Monitor - "
            else -> ""
        }
    }

    private fun setupSearchBox(searchView: SearchView) {
        val hint = "Search Sensor To Connect"
        val builder = SpannableStringBuilder(hint)
        builder.setSpan(
            ForegroundColorSpan(Color.GRAY),
            0,
            hint.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        searchView.queryHint = builder
        searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                searchView.setQuery("", false) // Clear the search box
                searchView.clearFocus()
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                val query = newText ?: ""
                val filteredList = CommonDataArea.sensorList.filter { sensor ->
                    val sensorName = sensor.getSensorName().replace(Regex("[^0-9]"), "")
                    val sensorNumber = if (sensorName.length >= 4) {
                        sensorName.takeLast(4) // Get the last 4 digits
                    } else {
                        sensorName
                    }
                    sensorNumber.startsWith(query)
                }
                adapter.filterList(filteredList)
                return true
            }
        })
    }

    private fun setupListView(sensorList: RecyclerView, adapter: SensorRecyclerViewAdapter) {
        uiScope.launch {
            sensorList.adapter = adapter
            sensorList.visibility = View.VISIBLE
            sensorList.isEnabled = true
            while (true) {
                if (sensorService.isScanning) {
                    adapter.notifyItemInserted(CommonDataArea.sensorList.size)
                    val filteredList = filterSensorsByType(CommonDataArea.SelectedSensorName)
                    updateNoSensorAvailableTextView(filteredList)
                } else
                    break

                delay(100)
            }

        }
    }

    fun updateSaveButtonColor() {
        if (chestSensorSelected.isNotEmpty() ||
            limbSensorSelected.isNotEmpty() ||
            bpSensorSelected.isNotEmpty()
        ) {
            saveBtn.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.lightBlue))
        } else {
            saveBtn.backgroundTintList =
                ColorStateList.valueOf(ContextCompat.getColor(context, R.color.components_gray))
        }
    }

    private fun onSaveButtonClick(context: Context): Boolean {
        if (chestSensorSelected.isEmpty() && limbSensorSelected.isEmpty() && bpSensorSelected.isEmpty()) {
            return false
        }
        val sensorHelper = SensorListDialogHelper(context)
        if (enableSnsApi) {
            sensorHelper.startScanWithSns()
        } else {
            if (sensorAuthenticationCheck.isChecked) {
                sensorHelper.startScanWithIomt()
            } else {
                sensorHelper.startScanWithSns()
            }
        }
        return true
    }

    private fun onRefreshButtonClick(scanTime: Long) {
        if (sensorService.isScanning)
            sensorService.stopScan()
        else {
            sensorService.startScan(scanTime)
            adapter = SensorRecyclerViewAdapter(CommonDataArea.sensorList, this)
            setupListView(sensorList, adapter)
        }

        val rotateAnimation = RotateAnimation(
            0f, 360f,
            Animation.RELATIVE_TO_SELF, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f
        )
        rotateAnimation.duration = 1000 // in milliseconds
        rotateAnimation.repeatCount = scanTime.toInt() / 1000 // 0 means do not repeat

        refreshBtn.startAnimation(rotateAnimation)
    }

    private fun getSwitchButtonData() {
        val sharedPreferences =
            context.getSharedPreferences("SensorAuthentication", Context.MODE_PRIVATE)
        isSensorAuthenticationChecked =
            sharedPreferences.getBoolean("sensorAuthenticationCheck", true)
        sensorAuthenticationCheck.isChecked = isSensorAuthenticationChecked
        if (sensorAuthenticationCheck.isChecked) {
            sensorAuthenticationCheck.thumbTintList =
                context.resources.getColorStateList(R.color.lightBlue, context.theme)
        } else {
            sensorAuthenticationCheck.thumbTintList =
                context.resources.getColorStateList(R.color.white, context.theme)
        }
    }

    private fun saveSwitchButtonData(isChecked: Boolean) {
        val sharedPreferences =
            context.getSharedPreferences("SensorAuthentication", Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.apply { putBoolean("sensorAuthenticationCheck", isChecked) }.apply()
        if (sensorAuthenticationCheck.isChecked) {
            sensorAuthenticationCheck.thumbTintList =
                context.resources.getColorStateList(R.color.lightBlue, context.theme)
        } else {
            sensorAuthenticationCheck.thumbTintList =
                context.resources.getColorStateList(R.color.white, context.theme)
        }
    }
}