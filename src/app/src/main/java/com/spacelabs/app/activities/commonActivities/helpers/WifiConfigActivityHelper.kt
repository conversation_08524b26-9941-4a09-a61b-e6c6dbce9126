package com.spacelabs.app.activities.commonActivities.helpers

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.ConnectivityManager.NetworkCallback
import android.net.LinkProperties
import android.net.Network
import android.net.NetworkCapabilities
import android.net.wifi.ScanResult
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSuggestion
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import com.spacelabs.app.activities.commonActivities.WifiConfigActivity
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.common.ApplicationManager
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.ParameterDaoHelper
import com.spacelabs.app.database.daoHelper.PatientDaoHelper
import com.spacelabs.app.database.daoHelper.SettingDaoHelper

class WifiConfigActivityHelper(private val wifiConfigActivity: WifiConfigActivity): CommonActivityHelper(wifiConfigActivity) {

    private val context: Context = wifiConfigActivity
    private val appManager: ApplicationManager = ApplicationManager(wifiConfigActivity)

    private lateinit var patientDaoHelper: PatientDaoHelper
    private lateinit var settingDaoHelper: SettingDaoHelper
    private lateinit var parameterDaoHelper: ParameterDaoHelper

    private lateinit var connectivityManager: ConnectivityManager
    private lateinit var networkCallback: NetworkCallback

    private val availableWifi = mutableListOf<ScanResult>()
    private val suggestionList = mutableListOf<WifiNetworkSuggestion>()

    companion object {
        const val REQUEST_CODE_FINE_LOCATION = 1001
        const val REQUEST_CODE_CHANGE_WIFI_STATE = 1002
    }

    enum class Operation {
        ADD,
        REMOVE
    }

    fun initialSetup() {
        setupKeyStore()
        appManager.requestPermissions()

        parameterDaoHelper = ParameterDaoHelper(context)
        parameterDaoHelper.loadDefaultAlarmValuesToDbFromFile(context)

        getAllSettings()
    }

    fun navigateIfWifiExist(key: String) {
        navigateIfSettingExist(key)
    }

    private fun getAllSettings() {
        settingDaoHelper = SettingDaoHelper(wifiConfigActivity.applicationContext)
        settingDaoHelper.getAllSettings()
        patientDaoHelper = PatientDaoHelper(context)
        patientDaoHelper.fetchCurrentPatientDetailsIfExist()
    }

    private val wifiScanReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val success = intent.getBooleanExtra(WifiManager.EXTRA_RESULTS_UPDATED, false)
            if (success) {
                scanSuccess(wifiConfigActivity.wifiManager)
            } else {
                Log.d("WifiReceiver", "Scan Failed")
            }
        }
    }

    private fun scanSuccess(wifiManager: WifiManager) {
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.StopScanningAnimation, null)
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return
        } else {
            availableWifi.addAll(wifiManager.scanResults)
        }
        processScanResults()
    }

    private fun processScanResults() {
        if (availableWifi.isEmpty()) return

        for (wifi in availableWifi) {
            Log.d("WifiFound", wifi.capabilities)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                val security = wifi.securityTypes
                Log.d("ProcessScanResults", security.joinToString(","))
                addWifiDiscovered(wifi.wifiSsid.toString())
            } else {
                addWifiDiscovered(wifi.SSID)
            }
        }
    }

    private fun addWifiDiscovered(ssid: String) {
        if (ssid.isEmpty()) return
        Log.d("WIFI_SSID", "addWifiDiscovered: $ssid")
        val nameWithoutQuotes = ssid.removeSurrounding("\"")
        wifiConfigActivity.wifiConfigActivityUi.manageWifiList(nameWithoutQuotes,
            Operation.ADD, null)
    }

    @RequiresApi(Build.VERSION_CODES.R)
    fun registerWifiReceiver(wifiManager: WifiManager) {
        val intentFilter = IntentFilter()
        intentFilter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)
        wifiConfigActivity.registerReceiver(wifiScanReceiver, intentFilter)

        wifiManager.startScan()
    }

    fun unregisterWifiReceiver() {
        wifiConfigActivity.unregisterReceiver(wifiScanReceiver)
    }

    fun saveWifiSettingsAndConnectNetwork(ssid: String, password: String) {
        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.WifiSsid, ssid)
        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.WifiPassword, password)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)
            connectWifi(ssid, password)
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun connectWifi(ssid: String, password: String) {
        appManager.requestPermissionsSequentially(arrayOf(Manifest.permission.CHANGE_WIFI_STATE), REQUEST_CODE_CHANGE_WIFI_STATE)
        val suggestion = WifiNetworkSuggestion.Builder()
            .setSsid(ssid)
            .setWpa2Passphrase(password)
            .setIsAppInteractionRequired(true)
            .build()

        suggestionList.add(suggestion)

        val status = wifiConfigActivity.wifiManager.addNetworkSuggestions(suggestionList)
        Log.d("wifiConnect", "Network suggestion status: $status")

        if (status != WifiManager.STATUS_NETWORK_SUGGESTIONS_SUCCESS) {
            Log.e("wifiConnect", "---------------------------Failed---------------------------")
        } else {
            Log.d("wifiConnect", "Network suggestions added successfully")

            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.StartScanningAnimation, "connecting...")
        }
    }




    fun registerNetworkCallback() {
        connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        networkCallback = object : NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                Log.d("NetworkCallback", "Network is available: $network")
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.NavigateToNext, null)
            }

            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                super.onCapabilitiesChanged(network, networkCapabilities)
                val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                Log.d("NetworkCallback", "Network capabilities changed: $networkCapabilities, hasInternet: $hasInternet")

                if (hasInternet) {
                    CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.NavigateToNext, null)
                }
            }
            override fun onLost(network: Network) {
                super.onLost(network)
                Log.d("NetworkCallback", "Network is lost: $network")
                // Handle network lost event
            }

            override fun onUnavailable() {
                super.onUnavailable()
                Log.d("NetworkCallback", "Network is unavailable")
                // Handle network unavailable event
            }


            override fun onLosing(network: Network, maxMsToLive: Int) {
                super.onLosing(network, maxMsToLive)
                Log.d("NetworkCallback", "Network is onLosing")
            }

            override fun onLinkPropertiesChanged(network: Network, linkProperties: LinkProperties) {
                super.onLinkPropertiesChanged(network, linkProperties)
                Log.d("NetworkCallback", "Network is onLinkPropertiesChanged")
            }


            override fun onBlockedStatusChanged(network: Network, blocked: Boolean) {
                super.onBlockedStatusChanged(network, blocked)
                Log.d("NetworkCallback", "Network is onBlockedStatusChanged")
            }




        }
        connectivityManager.registerDefaultNetworkCallback(networkCallback)
    }

    fun unregisterCallbacksAndCoroutines() {
        Log.d("NetworkCallback", "Network is unregisterCallbacksAndCoroutines")

        connectivityManager.unregisterNetworkCallback(networkCallback)
    }
}
