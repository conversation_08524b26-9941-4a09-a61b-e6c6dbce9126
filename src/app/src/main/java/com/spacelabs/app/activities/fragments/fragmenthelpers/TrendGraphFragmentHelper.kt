package com.spacelabs.app.activities.fragments.fragmenthelpers

import android.content.Context
import android.graphics.Color
import android.text.Html
import android.text.Spannable
import android.util.Log
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.spacelabs.app.R
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.iomt.data.CmsLiveDataTransactionManager
import com.spacelabs.app.ui.dialogs.trend_graph.trend_dao.TrendParamDao
import java.text.DateFormat
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.Date

class TrendGraphFragmentHelper(context: Context?) {

    val context: Context?

    private var currentHighlightedX = 0L

    private val trendParams: MutableSet<TrendParamDao> = hashSetOf()
    val noValText = context?.getString(R.string.noValue)!!

    private val noDataText: String
    private val loadingText: String

    private val encryptionVitalConverter = EncryptionVitalConverter()
    private val cmsLiveDataTransactionManager = CmsLiveDataTransactionManager()

    init {
        this.context = context
        noDataText = context?.getString(R.string.noDataToDisplay)!!
        loadingText = context.getString(R.string.loading)
    }

    private lateinit var date: TextView

    companion object {
        var minTimeMillis = 0L
        var maxTimeMillis = 0L

        private var xAxisMin = 0L
        private var xAxisMax = 0L

        var TrendInterval = 1
        var trendLoader = true
    }

    fun initChartAndVitalUi(view: View) {
        date = view.findViewById(R.id.date)
        trendParams.addAll(
            listOf(
                initHrTrendUi(view),
                initRrTrendUi(view),
                initSpo2TrendUi(view),
                initTempTrendUi(view),
                initBpSysTrendUi(view),
                initBpDiaTrendUi(view)
            )
        )
    }

    private fun initHrTrendUi(view: View): TrendParamDao {
        return TrendParamDao(
            StreamDataType.HR,
            view.findViewById(R.id.hr_chart_outer),
            view.findViewById(R.id.hrChart),
            view.findViewById(R.id.hr_chart_text),
            view.findViewById(R.id.hr_val_outer),
            view.findViewById(R.id.trend_hr),
            view.findViewById(R.id.hr_time),
            paramColor = getColor(R.color.lightGreen),
            yMax = CommonDataArea.hrAlarmDao.highMaxValue.toFloat(),
            yMin = CommonDataArea.hrAlarmDao.lowMinValue.toFloat()
        )
    }

    private fun initRrTrendUi(view: View): TrendParamDao {
        return TrendParamDao(
            StreamDataType.RR,
            view.findViewById(R.id.rr_chart_outer),
            view.findViewById(R.id.rrChart),
            view.findViewById(R.id.rr_chart_text),
            view.findViewById(R.id.rr_val_outer),
            view.findViewById(R.id.trend_rr),
            view.findViewById(R.id.rr_time),
            paramColor = getColor(R.color.white),
            yMax = CommonDataArea.respAlarmDao.highMaxValue.toFloat(),
            yMin = CommonDataArea.respAlarmDao.lowMinValue.toFloat()
        )
    }

    private fun initSpo2TrendUi(view: View): TrendParamDao {
        return TrendParamDao(
            StreamDataType.SPO2,
            view.findViewById(R.id.spo2_chart_outer),
            view.findViewById(R.id.spo2Chart),
            view.findViewById(R.id.spo2_chart_text),
            view.findViewById(R.id.spo2_val_outer),
            view.findViewById(R.id.trend_spo2),
            view.findViewById(R.id.spo2_time),
            paramColor = getColor(R.color.spo2Blue),
            yMax = CommonDataArea.spo2AlarmDao.highMaxValue.toFloat(),
            yMin = CommonDataArea.spo2AlarmDao.lowMinValue.toFloat()
        )
    }

    private fun initTempTrendUi(view: View): TrendParamDao {
        return TrendParamDao(
            StreamDataType.TEMP_SKIN,
            view.findViewById(R.id.temp_chart_outer),
            view.findViewById(R.id.tempChart),
            view.findViewById(R.id.temp_chart_text),
            view.findViewById(R.id.temp_val_outer),
            view.findViewById(R.id.trend_temp),
            view.findViewById(R.id.temp_time),
            paramColor = getColor(R.color.white),
            yMax = CommonDataArea.tempAlarmDao.highMaxValue.toFloat(),
            yMin = CommonDataArea.tempAlarmDao.lowMinValue.toFloat()
        )
    }

    private fun initBpSysTrendUi(view: View): TrendParamDao {
        return TrendParamDao(
            StreamDataType.BP_SYS,
            view.findViewById(R.id.bpSys_chart_outer),
            view.findViewById(R.id.bpSysChart),
            view.findViewById(R.id.bpSys_chart_text),
            view.findViewById(R.id.bpSys_val_outer),
            view.findViewById(R.id.trend_bpSys),
            view.findViewById(R.id.bpSys_time),
            paramColor = getColor(R.color.pink),
            yMax = CommonDataArea.bpSysAlarmDao.highMaxValue.toFloat(),
            yMin = CommonDataArea.bpSysAlarmDao.lowMinValue.toFloat()
        )
    }

    private fun initBpDiaTrendUi(view: View): TrendParamDao {
        return TrendParamDao(
            StreamDataType.BP_DIA,
            view.findViewById(R.id.bpDia_chart_outer),
            view.findViewById(R.id.bpDiaChart),
            view.findViewById(R.id.bpDia_chart_text),
            view.findViewById(R.id.bpDia_val_outer),
            view.findViewById(R.id.trend_bpDia),
            view.findViewById(R.id.bpDia_time),
            paramColor = getColor(R.color.pink),
            yMax = CommonDataArea.bpDiaAlarmDao.highMaxValue.toFloat(),
            yMin = CommonDataArea.bpDiaAlarmDao.lowMinValue.toFloat()
        )
    }

    fun setupCharts() {
        trendParams.forEach {
            setupInitialChartProperties(it.chart, it.yMax, it.yMin)
        }
    }

    private fun setupInitialChartProperties(chart: LineChart, yMax: Float, yMin: Float) {
        chart.setTouchEnabled(false) // Enable touch gestures including zooming
        chart.setPinchZoom(false)
        chart.setNoDataText("")

        chart.legend.isEnabled = false
        chart.description.isEnabled = false
        chart.isAutoScaleMinMaxEnabled = true
        val xAxis = chart.xAxis
        xAxis.setDrawGridLines(false)
        xAxis.setDrawAxisLine(false)
        xAxis.position = XAxis.XAxisPosition.BOTTOM
        xAxis.granularity = .001f // Set the granularity to 1 to display all labels
        xAxis.isGranularityEnabled = true
        xAxis.setDrawLabels(false) // Disable drawing labels

        val axisLeft = chart.axisLeft
        axisLeft.axisMaximum = yMax
        axisLeft.axisMinimum = yMin
        axisLeft.setDrawGridLines(false)
        axisLeft.setDrawAxisLine(false)
        axisLeft.setDrawLabels(false) // Disable drawing labels

        val axisRight = chart.axisRight
        axisRight.isEnabled = false
    }

    private fun showToast(msg: String) {
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
    }

    private fun setXAxisMinMax(maxTimeInMillis: Long) {
        val currentTimeMillis = TimestampUtils.getCurrentTimeMillis()
        if (maxTimeInMillis > currentTimeMillis) {
            showToast("Reached Maximum Range")
        } else {
            maxTimeMillis = maxTimeInMillis
            minTimeMillis = maxTimeMillis - TrendInterval * 60 * 60000

            xAxisMax = extractDayAndTimeFromTimestamp(maxTimeMillis)
            xAxisMin = extractDayAndTimeFromTimestamp(minTimeMillis)
            currentHighlightedX = 0L
        }

        val maxTime = TimestampUtils.convertMillisToDayMonthHourMinutes(maxTimeMillis)
        val minTime = TimestampUtils.convertMillisToDayMonthHourMinutes(minTimeMillis)

        date.text = Html.fromHtml("$minTime - $maxTime", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    fun extractAndPlotDataFromObjectBox(xMax: Long) {
        try {
            trendLoader = true
            setXAxisMinMax(xMax)
            trendParams.forEach {
                it.data.clear()
                manageChartVisibilityIfDataAvailable(it, loadingText)
                setupChartUpdateProperties(it.chart)
            }
            CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.GetTrendData, Pair(TrendInterval, maxTimeMillis))
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.TrendLoading, true)
        } catch (e: Exception) {
            Log.e("ExtractAndPlot", e.stackTraceToString())
        }
    }

    private fun setupChartUpdateProperties(chart: LineChart) {
        val xAxis = chart.xAxis

        xAxis.axisMinimum = xAxisMin.toFloat()
        xAxis.axisMaximum = xAxisMax.toFloat()

        chart.invalidate()
    }

    private fun manageChartVisibilityIfDataAvailable(trendParamDao: TrendParamDao, msg: String) {
        val isDataAvailable = trendParamDao.data.isNotEmpty()
        val chart = trendParamDao.chart
        val paramText = trendParamDao.valueText
        val timeText = trendParamDao.timeText
        val chartError = trendParamDao.chartErrorText
        if (isDataAvailable) {
            chart.visibility = View.VISIBLE
            paramText.visibility = View.VISIBLE
            timeText.visibility = View.VISIBLE
            chartError.visibility = View.GONE
        } else {
            chart.visibility = View.GONE
            paramText.visibility = View.INVISIBLE
            timeText.visibility = View.INVISIBLE
            chartError.visibility = View.VISIBLE
            chartError.text = Html.fromHtml(msg, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    fun uiUpdateOnCompleteLoading() {
        trendParams.forEach {
            if(it.data.isEmpty())
                manageChartVisibilityIfDataAvailable(it, noDataText)
        }
    }

    private fun plotDataOnChart(trendParamDao: TrendParamDao) {
        val data = trendParamDao.data
        manageChartVisibilityIfDataAvailable(trendParamDao, noDataText)
        if (data.isEmpty())
            return

        val dataSet = LineDataSet(data, trendParamDao.trendType.name)
        dataSet.color = trendParamDao.paramColor
        setupDataset(dataSet)

        val lineData = LineData(dataSet)
        trendParamDao.chart.data = lineData
        trendParamDao.chart.notifyDataSetChanged()
        trendParamDao.chart.invalidate()
    }


    private fun setupDataset(dataSet: LineDataSet) {
        dataSet.highLightColor = Color.rgb(41, 102, 147)
        dataSet.setDrawHorizontalHighlightIndicator(false)
        dataSet.setDrawVerticalHighlightIndicator(true)
        dataSet.highlightLineWidth = 5f

        dataSet.mode = LineDataSet.Mode.LINEAR // Use cubic bezier to create smoother waveform
        dataSet.setDrawCircles(false)
        dataSet.lineWidth = .75f // Adjust line width
        dataSet.setDrawValues(false)
    }

    private fun getColor(colorId: Int): Int {
        return ContextCompat.getColor(context!!, colorId)
    }

    private fun getHighlight(currentPosition: Long): Highlight? {
        return if (currentHighlightedX > xAxisMax) {
            showToast("Reached End of the Chart")
            currentHighlightedX = currentPosition
            null
        } else if (currentHighlightedX < xAxisMin) {
            showToast("Reached Start of the Chart")
            currentHighlightedX = currentPosition
            null
        } else Highlight(currentHighlightedX.toFloat(), 0, 0)
    }

    fun onForwardBackwardButtonClick(hasBackwardClicked: Boolean) {
        val currentValue = if (currentHighlightedX == 0L) xAxisMax else currentHighlightedX
        currentHighlightedX = if (hasBackwardClicked)
            currentValue - TrendInterval * 60000
        else
            currentValue + TrendInterval * 60000

        val newHighlight = getHighlight(currentValue) ?: return

        for (paramAndView in trendParams) {
            if (paramAndView.chart.data != null)
                paramAndView.chart.highlightValue(newHighlight, true)
        }
    }

    fun onNextPreviousButtonClick(hasPreviousClicked: Boolean) {
        currentHighlightedX = 0L
        if (hasPreviousClicked) {
            extractAndPlotDataFromObjectBox(minTimeMillis)
        } else {
            val maxTimeInMillis = maxTimeMillis + TrendInterval * 60 * 60000
            extractAndPlotDataFromObjectBox(maxTimeInMillis)
        }
    }

    fun onHighlighterChanged() {
        trendParams.forEach {
            val chart = it.chart
            val paramText = it.valueText
            val timeText = it.timeText
            chart.setOnChartValueSelectedListener(object : OnChartValueSelectedListener {
                override fun onValueSelected(e: Entry?, h: Highlight?) {
                    if (e != null) {
                        val xValue = e.x.toLong()
                        val yValue = e.y.toInt()
                        val formattedDateAndTime = getFormattedTimeByMilliseconds(xValue)
                        paramText.text =
                            Html.fromHtml(yValue.toString(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        timeText.text = Html.fromHtml(
                            formattedDateAndTime.second,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                        date.text = Html.fromHtml(
                            formattedDateAndTime.first,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    } else {
                        paramText.text =
                            Html.fromHtml(noValText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        timeText.text = Html.fromHtml(noValText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                }

                override fun onNothingSelected() {
                    // Handle when nothing is selected
                }
            })
        }
    }

    fun getFormattedTimeByMilliseconds(milliseconds: Long, locale: java.util.Locale = java.util.Locale.getDefault(), ): Pair<String, String> {
        val originalTimeMillis = maxTimeMillis - (xAxisMax - milliseconds)
        val dayMonthYearFormat = DateFormat.getDateInstance(DateFormat.MEDIUM, locale)
        val hourMinSecondFormat = DateFormat.getTimeInstance(DateFormat.MEDIUM, locale)
        val date = Date(originalTimeMillis)
        return Pair(dayMonthYearFormat.format(date), hourMinSecondFormat.format(date))
    }

    fun onTrendDataReceived(measurementData: List<MeasurementData>) {
        Log.d("OnTrendDataReceived", measurementData.size.toString())
        for (obj in measurementData) {
            val paramName = obj.paramName
            val value =
                encryptionVitalConverter.convertToEntityProperty(obj.value) ?: continue
            val time = obj.tickValue ?: continue

            val entryX = extractDayAndTimeFromTimestamp(time).toFloat()
            if (paramName == "BP") {
                splitBpData(value, entryX)
            } else {
                val trendView =
                    trendParams.firstOrNull { it.trendType.name.equals(paramName, true) }
                        ?: continue
                trendView.data.add(Entry(entryX, value.toFloat()))
            }
        }
        trendParams.forEach { plotDataOnChart(it) }
    }

    private fun extractDayAndTimeFromTimestamp(timestamp: Long): Long {
        val dateTime = LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.UTC)
        val time = dateTime.toLocalTime()
        return time.toNanoOfDay() / 1_000_000
    }

    private fun splitBpData(value: Double, time: Float) {
        val combinedBp: Long = value.toLong()

        // Extract systolic and diastolic values from combined BP
        val sys: Int = cmsLiveDataTransactionManager.getSys(combinedBp).toInt()
        val dia: Int = cmsLiveDataTransactionManager.getDia(combinedBp).toInt()

        // Find the TrendParamDao objects for bpSys and bpDia
        val trendViewSys = trendParams.firstOrNull { it.trendType == StreamDataType.BP_SYS }
        val trendViewDia = trendParams.firstOrNull { it.trendType == StreamDataType.BP_DIA }

        // Add systolic and diastolic values to their respective TrendParamDao data lists
        trendViewSys?.data?.add(Entry(time, sys.toFloat()))
        trendViewDia?.data?.add(Entry(time, dia.toFloat()))
    }

    fun onCloseFragment() {
        trendLoader = false
        trendParams.forEach {
            val chart = it.chart
            chart.clear()
            chart.notifyDataSetChanged()
            chart.invalidate()
        }
    }
}