package com.spacelabs.app.iomt.apiHandler.sensorEventApi

import android.util.Log
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.iomt.apiHandler.SensorApiHandler
import com.spacelabs.app.iomt.apiHandler.interfaces.SensorDisconnectCallback
import com.spacelabs.app.iomt.apiHandler.interfaces.SensorMappingCallback

class SensorEventHandling {
    private val sensorApiHandler = SensorApiHandler()



    fun mapConnectedSensor(sensor: Sensor) {
        if(!CommonDataArea.enableSnsApi) {

            sensorApiHandler.mapSensorToDevice(
                SettingDaoHelper.AppSettings.DeviceUuid.getValue(),
                sensor.name,
                sensor.address,
                object :
                    SensorMappingCallback {
                    override fun onMappingResult(success: Boolean, message: String) {
                        if (success) {
                            Log.d("mapConnectedSensor", "onMappingResult: $message")
                        } else {
                            Log.d("mapConnectedSensor", "onMappingResult: $message")

                        }
                    }
                })
        }
        else return

    }

    fun disconnectSensorFromDevice(sensor: Sensor) {
        if(!CommonDataArea.enableSnsApi) {

            sensorApiHandler.disconnectSensorFromDevice(
                SettingDaoHelper.AppSettings.DeviceUuid.getValue(),
                sensor.name,
                sensor.address,
                object :
                    SensorDisconnectCallback {
                    override fun onDisconnectResult(success: Boolean, message: String) {
                        if (success) {
                            Log.d("disConnectSensor", "onDisconnectResult: $message")
                        } else {
                            Log.d("disConnectSensor", "onDisconnectResult: $message")

                        }
                    }
                })
        }else return

    }
}