package com.spacelabs.app.database.objectbox.encryption

import android.util.Log
import com.spacelabs.app.common.CommonDataArea
import java.io.UnsupportedEncodingException
import java.nio.ByteBuffer
import java.security.GeneralSecurityException
import java.security.MessageDigest
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object AESVitalCrypt {

    private const val TAG = "AESCrypt"
    private const val AES_MODE = "AES/CBC/PKCS7Padding"
    private const val CHARSET = "UTF-8"
    private const val HASH_ALGORITHM = "SHA-256"

    private val ivBytes = byteArrayOf(
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    )

    private var DEBUG_LOG_ENABLED = false
    private val key = CommonDataArea.keystoreManager.getKeyFromKeystore()?.let { generateKey(it) }

    private fun generateKey(password: String): SecretKeySpec {
        val digest = MessageDigest.getInstance(HASH_ALGORITHM)
        val bytes = password.toByteArray(charset(CHARSET))
        digest.update(bytes, 0, bytes.size)
        val key = digest.digest()

        log("SHA-256 key", key)

        return SecretKeySpec(key, "AES")
    }

    @Throws(GeneralSecurityException::class, UnsupportedEncodingException::class)
    fun encrypt(value: Double): ByteArray? {
        if (key == null) {
            Log.e(TAG, "Encryption key is null")
            return null
        }

        return try {
            val plainText = ByteBuffer.allocate(java.lang.Double.BYTES).putDouble(value).array()
            encrypt(key, ivBytes, plainText)
        } catch (e: UnsupportedEncodingException) {
            if (DEBUG_LOG_ENABLED) Log.e(TAG, "UnsupportedEncodingException during encryption", e)
            throw GeneralSecurityException(e)
        }
    }

    @Throws(GeneralSecurityException::class)
    fun decrypt(cipherText: ByteArray): Double? {
        if (key == null) {
            Log.e(TAG, "Decryption key is null")
            return null
        }

        return try {
            val decryptedBytes = decrypt(key, ivBytes, cipherText)
            ByteBuffer.wrap(decryptedBytes).double
        } catch (e: UnsupportedEncodingException) {
            if (DEBUG_LOG_ENABLED) Log.e(TAG, "UnsupportedEncodingException during decryption", e)
            throw GeneralSecurityException(e)
        }
    }

    @Throws(GeneralSecurityException::class)
    private fun encrypt(key: SecretKeySpec, iv: ByteArray, message: ByteArray): ByteArray {
        val cipher = Cipher.getInstance(AES_MODE)
        val ivSpec = IvParameterSpec(iv)
        cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec)
        val cipherText = cipher.doFinal(message)

        log("cipherText", cipherText)

        return cipherText
    }

    @Throws(GeneralSecurityException::class)
    private fun decrypt(key: SecretKeySpec, iv: ByteArray, decodedCipherText: ByteArray): ByteArray {
        val cipher = Cipher.getInstance(AES_MODE)
        val ivSpec = IvParameterSpec(iv)
        cipher.init(Cipher.DECRYPT_MODE, key, ivSpec)
        val decryptedBytes = cipher.doFinal(decodedCipherText)

        log("decryptedBytes", decryptedBytes)

        return decryptedBytes
    }

    private fun log(what: String, bytes: ByteArray) {
        if (DEBUG_LOG_ENABLED) Log.d(TAG, "$what [${bytes.size}] [${bytesToHex(bytes)}]")
    }

    private fun log(what: String, value: String) {
        if (DEBUG_LOG_ENABLED) Log.d(TAG, "$what [${value.length}] [$value]")
    }

    private fun bytesToHex(bytes: ByteArray): String {
        val hexArray = charArrayOf(
            '0', '1', '2', '3', '4', '5', '6', '7',
            '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'
        )
        val hexChars = CharArray(bytes.size * 2)
        for (j in bytes.indices) {
            val v = bytes[j].toInt() and 0xFF
            hexChars[j * 2] = hexArray[v ushr 4]
            hexChars[j * 2 + 1] = hexArray[v and 0x0F]
        }
        return String(hexChars)
    }
}
