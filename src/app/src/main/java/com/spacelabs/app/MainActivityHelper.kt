package com.spacelabs.app

import android.app.Activity
import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.Network
import android.net.wifi.WifiManager
import android.os.BatteryManager
import android.os.Build
import android.util.Log
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2DeviceStatus
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.data.SwitchDeviceStatus
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.alarms.AlarmReceiver
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.api.data.ApiDataTransactionManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.connectivity.IbsmBluetoothManager
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.helpers.MeasurementDataDaoHelper
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.iomt.CmsClient
import com.spacelabs.app.iomt.data.CmsLiveDataTransactionManager
import com.spacelabs.app.iomt.apiHandler.AuthenticationHandler
import com.spacelabs.app.iomt.apiHandler.sensorEventApi.SensorEventHandling
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.Dialogs
import com.spacelabs.app.ui.MainActivityUIs
import com.spacelabs.app.ui.dialogs.CommonNotificationDialogs
import kotlinx.coroutines.*

class MainActivityHelper(mainActivity: MainActivity) {

    private val mainActivity: MainActivity
    private val context: Context
    private val connectivityManager: ConnectivityManager
    val ibsmBluetoothManager: IbsmBluetoothManager

    private lateinit var snsApiManager: SnsApiManager
    private lateinit var wifiManager: WifiManager
    private var authenticationHandler = AuthenticationHandler()

    private val settingDaoHelper: SettingDaoHelper

    private val alarmManager: AlarmManager
    private val cmsliveDataManager = CmsLiveDataTransactionManager()
    private val snsLiveDataManager = ApiDataTransactionManager()
    private val mainActivityUIs:MainActivityUIs

    private val measurementDaoHelper: MeasurementDataDaoHelper
    private val sensorEventHandling = SensorEventHandling()
    private val commonNotification: CommonNotificationDialogs
    lateinit var uiScope: CoroutineScope


    init {
        this.mainActivity = mainActivity
        context = mainActivity
        connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        ibsmBluetoothManager = IbsmBluetoothManager(context)
        settingDaoHelper = SettingDaoHelper(context)
        alarmManager = mainActivity.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        measurementDaoHelper = MeasurementDataDaoHelper()
        commonNotification = CommonNotificationDialogs(context)
        mainActivityUIs=MainActivityUIs(context)
    }

  fun onCreateOptionsMenuBatteryAction(iconBattery: MenuItem) {
        try {
            wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
            val batteryStatus: Intent? =
                IntentFilter(Intent.ACTION_BATTERY_CHANGED).let { ifilter ->
                    context.registerReceiver(null, ifilter)
                }
            val level = batteryStatus?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
            val scale = batteryStatus?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1
            val batteryPercentage = (level.toFloat() / scale.toFloat() * 100).toInt()

            // Check if the phone is currently charging
            val isCharging = batteryStatus?.getIntExtra(
                BatteryManager.EXTRA_STATUS,
                -1
            ) == BatteryManager.BATTERY_STATUS_CHARGING
            val batteryIconResource = if (isCharging) {
                CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.NoAlarm, null)
                R.drawable.battery_bolt

            } else {
                    criticalAndLowBatteryStatusPopupForDevice(batteryPercentage)
                getBatteryIconResource(batteryPercentage)

            }

            iconBattery.setIcon(batteryIconResource)

        } catch (ex: Exception) {
            LogWriter.writeExceptLog("BluetoothOnCreateOptionsMenu", ex.stackTraceToString())
        }
    }
    fun onCreateChargingBattery(iconBattery: MenuItem){
        val batteryStatus: Intent? =
            IntentFilter(Intent.ACTION_BATTERY_CHANGED).let { ifilter ->
                context.registerReceiver(null, ifilter)
            }
        val level = batteryStatus?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
        val scale = batteryStatus?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1
        val batteryPercentage = (level.toFloat() / scale.toFloat() * 100).toInt()
        val isCharging = batteryStatus?.getIntExtra(
            BatteryManager.EXTRA_STATUS,
            -1
        ) == BatteryManager.BATTERY_STATUS_CHARGING

        if (isCharging) {
            iconBattery.setIcon( R.drawable.battery_bolt)
        }
        else{
            iconBattery.setIcon(getBatteryIconResource(batteryPercentage))
        }
    }
     private fun getBatteryIconResource(batteryPercentage: Int): Int {
        return when {
            batteryPercentage <= 10 -> R.drawable.battery_warning
            batteryPercentage  in 10..20 ->  R.drawable.ic_battery_verylow_solid
            batteryPercentage in  21..50 -> R.drawable.battery_half
            batteryPercentage <= 75 -> R.drawable.battery_three_quarters
            else -> R.drawable.battery_full
        }

    }
    private fun criticalAndLowBatteryStatusPopupForDevice(batteryValue: Int) {
        when (batteryValue) {
            in 0..10-> {
                CommonDataArea.CriticalDeviceBatteryAlarmDao.isAttached=false
                CommonDataArea.CriticalDeviceBatteryAlarmDao.alarmStatus=true
                CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.CriticalDeviceBatteryAlarm, null)
                commonNotification.showAlert(context.getString(R.string.critical_battery), context.getString(R.string.connect_to_power), null, "", R.drawable.battery_warning, 5000)
                CommonDataArea.CriticalDeviceBatteryAlarmDao.isAttached=true
                CommonDataArea.CriticalDeviceBatteryAlarmDao.alarmStatus=false
                CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.NoAlarm, null)
            }
            in 10..20 -> {
                commonNotification.showAlert(context.getString(R.string.low_battery), context.getString(R.string.connect_to_power), null, "", R.drawable.ic_battery_verylow_solid, 3000)
            }
        }
    }
    @RequiresApi(Build.VERSION_CODES.S)
    fun enableBluetoothIfTurnedOff() {
        ibsmBluetoothManager.turnOnBluetoothIfIsOff()
    }

    private val bluetoothEnableLauncher = mainActivity.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult(),
    ) { result: ActivityResult ->
        if (result.resultCode != Activity.RESULT_OK) {
            Toast.makeText(context, "Can't Enable Bluetooth", Toast.LENGTH_SHORT).show()
            CommonDataArea.isBluetoothEnabled = false
        } else {
            CommonDataArea.isBluetoothEnabled = true
        }
    }

    fun internetConnectionCallback() {
        val networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                CommonDataArea.hasInternetConnection = true
                if (enableSnsApi) {
                    snsApiManager = SnsApiManager()
                } else {
                    /*if (CommonDataArea.cmsAccessToken == null) {
                        authenticationHandler.getAccessTokenforCms()

                    } else {*/
                    val cmsClient = CmsClient()
                    cmsClient.cmsConnect()
                    // }
                }
            }

            override fun onLost(network: Network) {
                CommonDataArea.hasInternetConnection = false
                if (SnsApiManager.webSocketClient != null)
                    SnsApiManager.webSocketClient!!.close()

                SnsApiManager.webSocketClient = null
                if (!enableSnsApi){
                    if (CmsClient.mWebSocketClient != null)
                        CmsClient.mWebSocketClient!!.close()
                    CmsClient.mWebSocketClient = null
                }
            }
        }
        connectivityManager.registerDefaultNetworkCallback(networkCallback)
    }

    private fun startBp(isAutoBp: Boolean) {
        try {
            if (CommonDataArea.bpSensor == null || !CommonDataArea.bpSysAlarmDao.alarmStatus)
                return

            if (CommonDataArea.bpSensor?.deviceStatus != BP2DeviceStatus.BP_MEASURING) {
                if (isAutoBp)
                    settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.BpTimer, CommonDataArea.bpMeasurementPeriodicTime)

                CommonDataArea.bpSensor?.sendCommand(SwitchDeviceStatus.Request(SwitchDeviceStatus.TargetStatus.START_MEASURE_BP))
            }
        } catch (ex: Exception) {
            Log.e("CheckForAutoBpTimeDueAndStartMeasure", ex.stackTraceToString())
        }
    }

    private fun getScheduledEventBySettings(settings: SettingDaoHelper.AppSettings): AlarmReceiver.ScheduledEvents? {
        return when (settings) {
            SettingDaoHelper.AppSettings.BpTimer -> AlarmReceiver.ScheduledEvents.BpMeasurement
            SettingDaoHelper.AppSettings.TurnTimer -> AlarmReceiver.ScheduledEvents.TurnDue
            else -> null
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun scheduleAlarm(settings: SettingDaoHelper.AppSettings, timestamp: String) {
        val event = getScheduledEventBySettings(settings) ?: return

        try {
            if (alarmManager.canScheduleExactAlarms()) {
                val triggerAtMillis = TimestampUtils.timestampToMillis(timestamp)
                val intent = Intent(context, AlarmReceiver::class.java)
                intent.action = event.action

                val pendingIntent = PendingIntent.getBroadcast(context, event.requestCode, intent, PendingIntent.FLAG_IMMUTABLE)
                if (pendingIntent != null)
                    alarmManager.cancel(pendingIntent)

                alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerAtMillis, pendingIntent)
            }
        } catch (e: Exception) {
            Log.e("ScheduleAlarm", e.stackTraceToString())
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun cancelScheduledAlarm(settings: SettingDaoHelper.AppSettings) {
        val event = getScheduledEventBySettings(settings) ?: return

        try {
            if (alarmManager.canScheduleExactAlarms()) {
                val intent = Intent(context, AlarmReceiver::class.java)
                intent.action = event.action

                val pendingIntent = PendingIntent.getBroadcast(context, event.requestCode, intent, PendingIntent.FLAG_IMMUTABLE)
                if (pendingIntent != null)
                    alarmManager.cancel(pendingIntent)
            }
        } catch (e: Exception) {
            Log.e("CancelScheduledAlarm", e.stackTraceToString())
        }
    }

    private fun onTurnDueTriggerAction(hasTurnDue: Boolean) {
        if (CommonDataArea.chestSensor?.isConnected == true)
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SetResetTurnDue, hasTurnDue)
    }

    private fun onFallDataReceived(isAcknowledged: Boolean) {
        CommonDataArea.fallAlarmDao.isAttached = isAcknowledged
        CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.Fall, null)
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.FallNotified, isAcknowledged)
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun onReceiveMultiValuedEvents(
        event: IOEventCallback.IOEventType,
        eventData: Pair<*, *>,
    ) {
        val first = eventData.first
        val second = eventData.second
        when (event) {
            IOEventCallback.IOEventType.ScheduleAlarm -> scheduleAlarm(
                first as SettingDaoHelper.AppSettings,
                second as String
            )

            IOEventCallback.IOEventType.ConnectionStatusUpdate -> onReceiveConnectionStatusUpdate(
                first as Sensor,
                second as ConnectionStatus
            )

            IOEventCallback.IOEventType.StreamUpload ->  snsLiveDataManager.uploadLiveMeasurementData(first as MeasurementData, second as ByteArray?)

            IOEventCallback.IOEventType.GetTrendData -> measurementDaoHelper.getTrendDataForInterValAndNotifyCharts(
                first as Int,
                second as Long
            )

            else -> return
        }
    }

    private fun getCurrentAlarmParamByStreamType(streamType: StreamDataType) {
        val streamAlarmParam = when (streamType) {
            StreamDataType.HR -> CurrentAlarmParameter.Ecg
            StreamDataType.RR -> CurrentAlarmParameter.Resp
            StreamDataType.SPO2 -> CurrentAlarmParameter.Spo2
            StreamDataType.TEMP_SKIN -> CurrentAlarmParameter.Temp
            StreamDataType.BP_SYS -> CurrentAlarmParameter.BpSys
            StreamDataType.BP_DIA -> CurrentAlarmParameter.BpDia
            else -> return
        }

        CommonEventHandler.postAlarmEvent(streamAlarmParam, Double.NaN)
    }

    fun resetValuesBySensorType(streamType: StreamDataType) {
        when (streamType) {
            StreamDataType.HR -> CommonDataArea.HR = 0
            StreamDataType.RR -> CommonDataArea.RR = 0
            StreamDataType.SPO2 -> CommonDataArea.spo2 = Double.NaN
            StreamDataType.TEMP_SKIN -> CommonDataArea.temperature = Double.NaN
            StreamDataType.BP_SYS -> {
                CommonDataArea.BpSys = Double.NaN
                CommonDataArea.bpTime = ""
            }

            StreamDataType.BP_DIA -> {
                CommonDataArea.BpDia = Double.NaN
                CommonDataArea.bpTime = ""
            }

            StreamDataType.BODY_POSITION -> CommonDataArea.bodyPosition = String()
            StreamDataType.ACCEL -> CommonDataArea.angle = 0
            else -> return
        }
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalReceived, streamType)
        getCurrentAlarmParamByStreamType(streamType)
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun onReceiveConnectionStatusUpdate(
        sensor: Sensor,
        connectionStatus: ConnectionStatus,
    ) {
        when (connectionStatus) {
            ConnectionStatus.CONNECTING -> onSensorConnecting(sensor)
            ConnectionStatus.CONNECTED -> onSensorConnected(sensor)
            ConnectionStatus.CONNECTION_LOST -> onSensorConnectionLost(sensor)
            ConnectionStatus.DISCONNECTED -> onSensorDisconnect(sensor)
        }
        if (connectionStatus != ConnectionStatus.CONNECTING)
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorBatteryChanged, sensor.sensorType)
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun onSensorDisconnect(sensor: Sensor) {
        ibsmBluetoothManager.unpairSensor(sensor)
        sensorEventHandling.disconnectSensorFromDevice(sensor)
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorDisconnected, sensor)
        when (sensor.sensorType) {
            SensorType.CHEST -> onTurnDueTriggerAction(false)
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.EndBpAlarms, null)
            else -> return
        }
    }
    private fun onSensorConnectionLost(sensor: Sensor) {
        Log.d("OnSensorConnectionLost", "${sensor.sensorType} -> Connection Lost")
    }
    private fun onSensorConnecting(sensor: Sensor) {
        Log.d("OnSensorConnecting", "${sensor.sensorType} -> Connecting")
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.PopupDialog, Dialogs.Popups.SensorConnectionUpdate)
    }
    private fun onSensorConnected(sensor: Sensor) {
        Log.d("OnSensorConnected", "${sensor.sensorType} -> Connected")
        sensorEventHandling.mapConnectedSensor(sensor)
    }

    private fun enrollSensor(sensor: Sensor) {
        SibelSensorManager.enrollSensorNameByType(context, sensor.name, sensor.sensorType)
        sensorService.startScan(15000)
    }

    private fun saveRefreshToken(value: String) {
        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.apiToken, value)
    }

    inner class IoEventHandler : IOEventCallback {
        @RequiresApi(Build.VERSION_CODES.S)
        override fun ioEvent(event: IOEventCallback.IOEventType, eventData: Any?) {
            mainActivity.ioScope.launch {
                when (event) {
                    IOEventCallback.IOEventType.EnrollSensor -> enrollSensor(eventData as Sensor)
                    IOEventCallback.IOEventType.AskPermission -> mainActivity.appManager.askPermissions(
                        eventData as String
                    )

                    IOEventCallback.IOEventType.CancelAlarm -> cancelScheduledAlarm(eventData as SettingDaoHelper.AppSettings)
                    IOEventCallback.IOEventType.StartAutoBp -> startBp(true)
                    IOEventCallback.IOEventType.StartManualBp -> startBp(false)
                    IOEventCallback.IOEventType.TriggerTurnDue -> onTurnDueTriggerAction(eventData as Boolean)
                    IOEventCallback.IOEventType.HasFallen -> onFallDataReceived(eventData as Boolean)
                    IOEventCallback.IOEventType.BackgroundUpload -> if (enableSnsApi) snsLiveDataManager.uploadHistoricalData() else cmsliveDataManager.uploadHistoricalData()
                    IOEventCallback.IOEventType.StreamTypeStart -> resetValuesBySensorType(eventData as StreamDataType)
                    IOEventCallback.IOEventType.StreamUpload,
                    IOEventCallback.IOEventType.ScheduleAlarm,
                    IOEventCallback.IOEventType.GetTrendData,
                    IOEventCallback.IOEventType.ConnectionStatusUpdate,
                    -> onReceiveMultiValuedEvents(event, eventData as Pair<*, *>)

                    IOEventCallback.IOEventType.ApiTokenUpdate -> saveRefreshToken(eventData as String)
                }
            }
        }
    }
}