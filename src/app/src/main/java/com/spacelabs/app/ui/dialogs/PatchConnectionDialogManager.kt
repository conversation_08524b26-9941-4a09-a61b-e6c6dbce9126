package com.spacelabs.app.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.Spannable
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatButton
import com.sibelhealth.bluetooth.sensor.sibel.SibelSensor
import com.sibelhealth.bluetooth.sensorservice.SensorService
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.MainActivity
import com.spacelabs.app.R
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.isSensorRemoved
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.sensorService
import com.spacelabs.app.ui.Dialogs
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PatchConnectionDialogManager(mainActivity: MainActivity) : DialogManager(mainActivity) {

    companion object {
        private var chestSensor: Sensor? = null
        private var limbSensor: Sensor? = null
        private var bpSensor: Sensor? = null

        private var sensorCount = 0

        fun setSensor(sensor: Sensor) {
            when(sensor.sensorType) {
                SensorType.CHEST -> chestSensor = sensor
                SensorType.LIMB -> limbSensor = sensor
                else -> bpSensor = sensor
            }
            sensorCount++
        }

        private fun removeSensor(type: SensorType){
            when(type) {
                SensorType.CHEST -> chestSensor = null
                SensorType.LIMB -> limbSensor = null
                else -> bpSensor = null
            }
        }
    }

    // UI Variables

    private lateinit var chestGroup: Triple<LinearLayout, TextView, TextView>
    private lateinit var limbGroup: Triple<LinearLayout, TextView, TextView>
    private lateinit var bpGroup: Triple<LinearLayout, TextView, TextView>

    private lateinit var connectionText: TextView
    private lateinit var closeBtn: Button
    private lateinit var okBtn: AppCompatButton
    private lateinit var cancelBtn: AppCompatButton
    private lateinit var confirmBtn: AppCompatButton

    // Normal Variables
    private lateinit var uiScope: CoroutineScope
    private lateinit var currentDialog: Dialog
    private val sensorChangeRequest = mainActivityContext.getString(R.string.sensorChangeRequest)
    private  val sensorRemovingAlertText=mainActivityContext.getString(R.string.sensorRemovingAlertText)
    private val sensorTryingToConnect: MutableSet<Sensor> = HashSet()


    override fun onCreateDialogActions(context: Context, dialog: Dialog, layout: Int) {
        uiScope = dialogHelper.createUICoroutineScope()
        currentDialog = dialog
    }

    override fun initUIs(dialog: Dialog) {
        chestGroup = Triple(
            dialog.findViewById(R.id.chestBox),
            dialog.findViewById(R.id.chestStatus),
            dialog.findViewById(R.id.chestName)
        )

        limbGroup = Triple(
            dialog.findViewById(R.id.limbBox),
            dialog.findViewById(R.id.limbStatus),
            dialog.findViewById(R.id.limbName)
        )

        bpGroup = Triple(
            dialog.findViewById(R.id.bpBox),
            dialog.findViewById(R.id.bpStatus),
            dialog.findViewById(R.id.bpName)
        )

        connectionText = dialog.findViewById(R.id.connectionText)
        closeBtn = dialog.findViewById(R.id.closeBtn)
        okBtn = dialog.findViewById(R.id.btnOk)
        cancelBtn = dialog.findViewById(R.id.btnCancel)
        confirmBtn = dialog.findViewById(R.id.btnConfirm)
    }

    override fun postUiInitActions(context: Context) {
        val chestSensor = SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.CHEST)
        val limbSensor = SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.LIMB)
        val bpSensor = SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.BP2_BLOOD_PRESSURE_MONITOR)
        uiScope.launch {
            if(!chestSensor.isNullOrEmpty())
                chestGroup.first.visibility = View.VISIBLE

            if(!limbSensor.isNullOrEmpty())
                limbGroup.first.visibility = View.VISIBLE

            if(!bpSensor.isNullOrEmpty())
                bpGroup.first.visibility = View.VISIBLE

            updateIconColor()
        }
        askToChangeTheSensorInThirtySeconds()

    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun uiActionListeners(context: Context, dialog: Dialog) {

        closeBtn.setOnClickListener {
            onDialogDismissAction(dialog)
            isSensorRemoved=true
        }

        okBtn.setOnClickListener {
            onDialogDismissAction(dialog)
            isSensorRemoved=true
        }

        cancelBtn.setOnClickListener {
            sensorService.startScan(30000)
            backToNormalView()
            askToChangeTheSensorInThirtySeconds()
            isSensorRemoved=false
        }

        confirmBtn.setOnClickListener {
            if(sensorTryingToConnect.isNotEmpty()) {
                for(sensor in sensorTryingToConnect) {
                    handleSensorConnectionFailure(sensor,sensor.sensorType)
                    closeDialogInFiveSeconds()
                }
            }
            sensorCount = 0
            Dialogs.connectionAlertDialog = null
        }

        dialog.setOnDismissListener {
            if(sensorTypesRemoved.isNotEmpty()) {
                for(type in sensorTypesRemoved) {
                    removeSensor(type)
                }
            }

            sensorCount = 0
            Dialogs.connectionAlertDialog = null

        }
    }
    private fun handleSensorConnectionFailure(sensor: Sensor,type: SensorType){
        when(type) {
            SensorType.CHEST ->{
                dialogHelper.resetSensorConfiguration(context,CommonDataArea.chestSensor,type)
                chestSensor = null }
            SensorType.LIMB ->{
                dialogHelper.resetSensorConfiguration(context,CommonDataArea.limbSensor,type)
                limbSensor = null
            }
            else -> {
                dialogHelper.resetBpSensorConfiguration(context,CommonDataArea.bpSensor)
                bpSensor = null
            }
        }
        handleSensorConnectionFailureVisibility(sensor)
        SibelSensorManager.enrollSensorNameByType(context, "", type)
    }
    private fun onDialogDismissAction(dialog: Dialog) {
        closeDialog(dialog)

    }

    private fun updateIconColor() {
        uiScope.launch {
            delay(100)
            val chestConnected = chestSensor?.isConnected == true
            val limbConnected = limbSensor?.isConnected == true
            val bpConnected = bpSensor?.isConnected == true

            updateIconAndConnectionText(chestGroup, chestSensor)
            updateIconAndConnectionText(limbGroup, limbSensor)
            updateIconAndConnectionText(bpGroup, bpSensor)
            if (chestConnected && limbConnected && bpConnected) {
                closeDialogInFiveSeconds()
            } else {
                updateIconColor()
            }
        }
    }

    private suspend fun updateIconAndConnectionText(uiGroup: Triple<LinearLayout, TextView, TextView>, sensor: Sensor?) {
        withContext(Dispatchers.Main) {
            val sensorStatus = uiGroup.second
            val sensorName = uiGroup.third

            if (sensor == null) {
                setupNoActivityUi(sensorStatus, sensorName, "")
                return@withContext
            }

            when (sensor.connectionStatus) {
                ConnectionStatus.CONNECTING -> {
                    sensorName.text = Html.fromHtml("", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    sensorTryingToConnect.add(sensor)
                    setupConnectingUi(sensorStatus, sensorName)
                    setupConnectionAndPrefixText("Connecting to", sensor)
                }
                ConnectionStatus.CONNECTED -> {
                    setupConnectedUi(sensorStatus, sensorName, sensor)
                    updateConnectionTextIfAllSensorsConnectedOrDisconnected(ConnectionStatus.CONNECTED)
                }
                ConnectionStatus.DISCONNECTED -> {
                    setupDisconnectedUi(sensor, sensorStatus, sensorName)
                    updateConnectionTextIfAllSensorsConnectedOrDisconnected(ConnectionStatus.DISCONNECTED)
                }
                else -> {
                    setupNoActivityUi(sensorStatus, sensorName, sensor.name)
                }
            }
        }
    }

    private fun setupStatusIcon(status: TextView, iconId: Int) {
        val color = when(iconId) {
            R.string.checkIcon -> mainActivityContext.getColor(R.color.lightGreen)
            R.string.warningIcon -> mainActivityContext.getColor(R.color.warningYellow)
            else -> mainActivityContext.getColor(R.color.red)
        }

        status.setTextColor(color)
        status.text = Html.fromHtml(mainActivityContext.getString(iconId), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun updateConnectionTextIfAllSensorsConnectedOrDisconnected(connectionStatus: ConnectionStatus) {
        val hasChestConnected = chestSensor == null || chestSensor?.isConnected == true
        val hasLimbConnected = limbSensor == null || limbSensor?.isConnected == true
        val hasBpConnected = bpSensor == null || bpSensor?.isConnected == true

        if(hasChestConnected && hasLimbConnected && hasBpConnected) {
            onAllConnectedOrDisconnected(connectionStatus)
        }
        if(connectionStatus == ConnectionStatus.DISCONNECTED) {
            onAllConnectedOrDisconnected(connectionStatus)
        }
    }

    private fun onAllConnectedOrDisconnected(connectionStatus: ConnectionStatus) {
        val status = when(connectionStatus) {
            ConnectionStatus.CONNECTED -> "Connected"
            else -> "Disconnected"
        }

        val msg = if(connectionStatus == ConnectionStatus.CONNECTED) {
            if(sensorCount > 1) "Sensors $status" else "Sensor $status"
        } else {
            if(sensorTypesRemoved.size > 1) "Sensors Disconnected"
            else "Sensor Disconnected"
        }

        changeCloseButtonsVisibility(true)
        connectionText.text = Html.fromHtml(msg, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        closeDialogInFiveSeconds()
    }

    private fun changeCloseButtonsVisibility(setVisible: Boolean) {
        val closeBtnVisibility = if(setVisible) View.VISIBLE else View.INVISIBLE
        val controlBtnVisibility = if(setVisible) View.INVISIBLE else View.VISIBLE

        cancelBtn.visibility = controlBtnVisibility
        confirmBtn.visibility = controlBtnVisibility

        okBtn.visibility = closeBtnVisibility
        closeBtn.visibility = closeBtnVisibility
    }

    private val sensorTypesRemoved: MutableSet<SensorType> = HashSet()


    private fun closeDialogInFiveSeconds() {
        uiScope.launch {
            delay(5000)
            onDialogDismissAction(currentDialog)
        }
    }

    private fun askToChangeTheSensorInThirtySeconds() {
        uiScope.launch {
            delay(30000)
            sensorService.stopScan()
            connectionText.text = Html.fromHtml(mainActivityContext.getString(R.string.sensorChangeRequest), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            changeCloseButtonsVisibility(false)
        }
    }
    private fun handleSensorConnectionFailureVisibility(sensor: Sensor) {
            sensorService.stopScan()
            connectionText.text = Html.fromHtml(sensor.name + sensorRemovingAlertText, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            changeCloseButtonsVisibility(true)

    }
    private fun setupConnectionAndPrefixText(prefix: String, sensor: Sensor?) {
        if(connectionText.text.toString() == sensorChangeRequest)
            return

        val connectionTxt = if(sensor != null)
            when(sensor.sensorType) {
                SensorType.BP2_BLOOD_PRESSURE_MONITOR -> "BP Sensor"
                else -> "${sensor.sensorType} Sensor"
            }
        else ""
        connectionText.text = Html.fromHtml("$prefix $connectionTxt", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun setupNoActivityUi(status: TextView, name: TextView, sensorName: String) {
        setupStatusIcon(status, R.string.wrongIcon)
        stopAnimationAndUpdateSensorName(name, sensorName)
    }

    private fun setupDisconnectedUi(sensor: Sensor, status: TextView, name: TextView) {
        changeCloseButtonsVisibility(true)
        setupNoActivityUi(status, name, sensor.name)
        val sensorIconLayout = when(sensor.sensorType) {
            SensorType.CHEST -> chestGroup.first
            SensorType.LIMB -> limbGroup.first
            else -> bpGroup.first
        }

        sensorIconLayout.visibility = View.VISIBLE

        sensorTypesRemoved.add(sensor.sensorType)
        closeDialogInFiveSeconds()
    }

    private fun setupConnectingUi(status: TextView, name: TextView) {
        setupStatusIcon(status, R.string.warningIcon)
        name.text = Html.fromHtml(mainActivityContext.getString(R.string.refreshIcon), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        rotateTextView(name)
    }

    private fun setupConnectedUi(status: TextView, name: TextView, sensor: Sensor) {
        stopAnimationAndUpdateSensorName(name, sensor.name)
        setupStatusIcon(status, R.string.checkIcon)
    }

    private fun stopAnimationAndUpdateSensorName(name: TextView, sensorName: String) {
        name.clearAnimation()
        name.animate()
            .rotation(0f)
            .setDuration(0)
            .start()

        name.clearAnimation()
        name.text = Html.fromHtml(sensorName.filter { it.isDigit() }, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun rotateTextView(rotatingText: TextView) {
        rotatingText.animate()
            .rotationBy(360f)
            .setDuration(500)
            .withEndAction {
                if(rotatingText.animation != null){
                    uiScope.launch {
                        delay(1000)
                        rotateTextView(rotatingText)
                    }
                }
            }
            .start()
    }

    private fun backToNormalView() {
        connectionText.text = Html.fromHtml("", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        cancelBtn.visibility = View.INVISIBLE
        confirmBtn.visibility = View.INVISIBLE
    }
}