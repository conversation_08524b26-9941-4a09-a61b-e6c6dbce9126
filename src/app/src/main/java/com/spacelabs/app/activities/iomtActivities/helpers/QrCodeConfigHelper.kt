package com.spacelabs.app.activities.iomtActivities.helpers

import android.content.Context
import com.spacelabs.app.activities.commonActivities.helpers.CommonActivityHelper
import com.spacelabs.app.activities.iomtActivities.QrScannerActivity
import com.spacelabs.app.database.daoHelper.SettingDaoHelper

class QrCodeConfigHelper(qrCodeActivity: QrScannerActivity) : CommonActivityHelper(qrCodeActivity) {

    private val context: Context
    private val qrCodeActivity : QrScannerActivity

    init {
        this.qrCodeActivity = qrCodeActivity
        context = qrCodeActivity
    }
    fun navigateIfAccountIdExists() {
        navigateIfSettingExist(SettingDaoHelper.AppSettings.AccountId.key)
    }

    fun saveAccountIdSettingsAndNavigateToNext(value: String) {
        saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.AccountId)
    }
    fun saveDeviceValues(key: Int, value: String):Boolean {
        when (key) {
            0 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.OauthUsername)
            1 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.OauthPassword)
            2 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.websocketUrl)
            3 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.apiUrl)
            4 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.apiToken)
            else -> return false
        }
        return true
    }

}
