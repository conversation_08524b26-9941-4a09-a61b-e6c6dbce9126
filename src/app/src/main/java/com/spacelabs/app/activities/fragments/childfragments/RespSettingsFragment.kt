package com.spacelabs.app.activities.fragments.childfragments

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.widget.EditText
import android.widget.Switch
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.AlarmDaoHelper
import java.util.AbstractMap

class RespSettingsFragment(val paramSettingsHelper: ParamSettingsHelper): Fragment(R.layout.settings_dialog_resp) {

    //Ui variables
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private lateinit var respAlarmSwitch: Switch

    private lateinit var respHigh: EditText
    private lateinit var highIncButton: AppCompatButton
    private lateinit var highDecButton: AppCompatButton

    private lateinit var respLow: EditText
    private lateinit var lowIncButton: AppCompatButton
    private lateinit var lowDecButton: AppCompatButton

    //Normal Variables
    private lateinit var alarmDaoHelper: AlarmDaoHelper
    private lateinit var alarmParamsDao: AlarmParametersDao
    private lateinit var incButtons: Map<AppCompatButton, EditText>
    private lateinit var decButtons: Map<AppCompatButton, EditText>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        alarmDaoHelper = AlarmDaoHelper(context!!)

        alarmParamsDao = CommonDataArea.respAlarmDao

        respAlarmSwitch = view.findViewById(R.id.respAlarmSwitch)

        respHigh = view.findViewById(R.id.respHigh)
        highIncButton = view.findViewById(R.id.respHighIncButton)
        highDecButton = view.findViewById(R.id.respHighDecButton)

        respLow = view.findViewById(R.id.respLow)
        lowIncButton = view.findViewById(R.id.respLowIncButton)
        lowDecButton = view.findViewById(R.id.respLowDecButton)

        incButtons = mapOf(highIncButton to respHigh, lowIncButton to respLow)
        decButtons = mapOf(highDecButton to respHigh, lowDecButton to respLow)

        uiActionListeners()
    }

    override fun onStart() {
        super.onStart()
        postUiInitActions()
    }

    fun postUiInitActions() {
        respAlarmSwitch.isChecked = alarmParamsDao.alarmStatus

        if(alarmParamsDao.alarmStatus){
            respAlarmSwitch.thumbTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
            respAlarmSwitch.trackTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
        }else{
            respAlarmSwitch.thumbTintList =
                context?.resources?.getColorStateList(R.color.white, context!!.theme)
            respAlarmSwitch.trackTintList =
                context?.resources?.getColorStateList(R.color.white, context!!.theme)
        }

        respHigh.setText("${alarmParamsDao.highValue.toInt()}")
        respLow.setText("${alarmParamsDao.lowValue.toInt()}")
    }

    fun uiActionListeners() {
        respAlarmSwitch.setOnCheckedChangeListener { _, isChecked ->
            val streamTypes = arrayOf(StreamDataType.RR, StreamDataType.RR_WAVEFORM)
            val hasActionCompleted = paramSettingsHelper.alarmSwitchActionListener(respAlarmSwitch, isChecked, streamTypes, CommonDataArea.chestSensor as Sensor?)
            if(hasActionCompleted) {
                alarmParamsDao.alarmStatus = isChecked
                alarmDaoHelper.updatePatientAlarmToDb(alarmParamsDao)
                paramSettingsHelper.uiEventOnSwitchAction(alarmParamsDao)
            } else {
                respAlarmSwitch.isChecked = !isChecked
                Toast.makeText(context, "Action Not Allowed, No Sensor Found", Toast.LENGTH_SHORT).show()
            }
        }

        paramSettingsHelper.incDecButtonsActionListeners(incButtons, decButtons, 1)

        val highEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(respHigh, ParamSettingsHelper.SettingsType.High)
        val lowEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(respLow, ParamSettingsHelper.SettingsType.Low)
        paramSettingsHelper.settingsValueTextChangedActionListener(highEditText, alarmParamsDao, alarmDaoHelper)
        paramSettingsHelper.settingsValueTextChangedActionListener(lowEditText, alarmParamsDao, alarmDaoHelper)
    }

}