package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.SettingsTable

class SettingAdapter(private var settingList: List<SettingsTable> = emptyList()) : RecyclerView.Adapter<SettingAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_settings, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val settings = settingList[position]
        holder.bind(settings)
    }

    override fun getItemCount(): Int {
        return settingList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
       private val settingID: TextView = itemView.findViewById(R.id.setting_id)
        private val settingName: TextView = itemView.findViewById(R.id.setting_name)
        private val settingValue: TextView = itemView.findViewById(R.id.setting_value)


        fun bind(setting: SettingsTable) {
            settingID.text = setting.id.toString()
            settingName.text = setting.settingsName
            settingValue.text = setting.settingsValue
        }
    }
}

