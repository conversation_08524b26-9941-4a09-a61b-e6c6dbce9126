package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.SensorTable

class SensorAdapter(private var patientList: List<SensorTable> = emptyList()) : RecyclerView.Adapter<SensorAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_sensor, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val sensorId: TextView = itemView.findViewById(R.id.SensorId)
        private val sensorDefId: TextView = itemView.findViewById(R.id.SensorDefId)
        private val patientID: TextView = itemView.findViewById(R.id.PatientID)
        private val sensorType: TextView = itemView.findViewById(R.id.SensorType)
        private val sensorName: TextView = itemView.findViewById(R.id.SensorName)
        private val sensorAddress: TextView = itemView.findViewById(R.id.SensorAddress)
        private val registerTimeStamp: TextView = itemView.findViewById(R.id.RegisterTimeStamp)
        private val pairingStatus: TextView = itemView.findViewById(R.id.PairingStatus)

        fun bind(patient: SensorTable) {
            sensorId.text = patient.sensorId.toString()
            sensorDefId.text = patient.sensorDefId.toString()
            patientID.text = patient.patientId.toString()
            sensorType.text = patient.sensorType
            sensorName.text = patient.sensorName
            sensorAddress.text = patient.sensorAddress.toString()
            registerTimeStamp.text = patient.registerTimestamp
            pairingStatus.text = patient.pairingStatus.toString()
        }
    }
}

