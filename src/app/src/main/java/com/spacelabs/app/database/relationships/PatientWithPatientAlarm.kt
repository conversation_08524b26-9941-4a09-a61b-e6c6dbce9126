package com.spacelabs.app.database.relationships

import androidx.room.Embedded
import androidx.room.Relation
import com.spacelabs.app.database.entities.PatientAlarmTable
import com.spacelabs.app.database.entities.PatientTable

data class PatientWithPatientAlarm(
    @Embedded
    val patient: PatientTable,
    @Relation(
        parentColumn = "patientId",
        entityColumn = "patientId"
    )
    val patientAlarms: List<PatientAlarmTable>?
)