package com.spacelabs.app.common

import android.util.Log
import com.spacelabs.app.api.ApiPostObservations
import com.spacelabs.app.api.data.dataclasses.WaveObservationBean
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import java.util.*

/**
 * Demonstration class showing how to integrate static test data
 * into existing data transmission methods.
 * 
 * This class provides examples of how to use StaticTestDataManager
 * to inject static FHIR observation data for testing purposes.
 */
class StaticTestDataDemo {
    
    companion object {
        private const val TAG = "StaticTestDataDemo"
        
        /**
         * Demonstrate how to use static waveform data in place of live sensor data
         */
        fun demonstrateStaticWaveformData() {
            val staticDataManager = StaticTestDataManager.getInstance()
            
            if (staticDataManager.shouldUseStaticData()) {
                Log.d(TAG, "Using static test data for waveform transmission")
                
                // Example: Get static ECG waveform data
                val ecgObservation = staticDataManager.getStaticECGWaveformObservation()
                if (ecgObservation != null) {
                    Log.d(TAG, "Static ECG Observation: $ecgObservation")
                    // This would be sent instead of live ECG data
                }
                
                // Example: Get static SpO2 waveform data
                val spo2Observation = staticDataManager.getStaticSpO2WaveformObservation()
                if (spo2Observation != null) {
                    Log.d(TAG, "Static SpO2 Observation: $spo2Observation")
                    // This would be sent instead of live SpO2 data
                }
                
                // Example: Get static respiration waveform data
                val respObservation = staticDataManager.getStaticRespirationWaveformObservation()
                if (respObservation != null) {
                    Log.d(TAG, "Static Respiration Observation: $respObservation")
                    // This would be sent instead of live respiration data
                }
                
                // Example: Get static waveform samples as DoubleArray
                val ecgSamples = staticDataManager.getStaticWaveformSamples("ECG")
                if (ecgSamples != null) {
                    Log.d(TAG, "Static ECG samples count: ${ecgSamples.size}")
                    Log.d(TAG, "First 5 ECG samples: ${ecgSamples.take(5).joinToString(", ")}")
                }
            } else {
                Log.d(TAG, "Using live sensor data (static test data disabled)")
            }
        }
        
        /**
         * Demonstrate how to use static vital signs data
         */
        fun demonstrateStaticVitalData() {
            val staticDataManager = StaticTestDataManager.getInstance()
            
            if (staticDataManager.shouldUseStaticData()) {
                Log.d(TAG, "Using static test data for vital signs")
                
                // Example: Get complete vital observation
                val vitalObservation = staticDataManager.getStaticVitalObservation()
                if (vitalObservation != null) {
                    Log.d(TAG, "Static Vital Observation: $vitalObservation")
                }
                
                // Example: Get specific vital values
                val heartRate = staticDataManager.getStaticVitalValue("147842") // MDC_ECG_HEART_RATE
                val respRate = staticDataManager.getStaticVitalValue("151554") // MDC_RESP_RATE
                val spo2 = staticDataManager.getStaticVitalValue("150316") // MDC_SAT_O2
                val temperature = staticDataManager.getStaticVitalValue("150364") // MDC_TEMP_BODY
                
                Log.d(TAG, "Static Heart Rate: $heartRate bpm")
                Log.d(TAG, "Static Respiratory Rate: $respRate /min")
                Log.d(TAG, "Static SpO2: $spo2 %")
                Log.d(TAG, "Static Temperature: $temperature °C")
            } else {
                Log.d(TAG, "Using live vital signs data (static test data disabled)")
            }
        }
        
        /**
         * Demonstrate how to use static alarm data
         */
        fun demonstrateStaticAlarmData() {
            val staticDataManager = StaticTestDataManager.getInstance()
            
            if (staticDataManager.shouldUseStaticData()) {
                Log.d(TAG, "Using static test data for alarms")
                
                // Example: Get static alarm observation
                val alarmObservation = staticDataManager.getStaticAlarmObservation("HR_LOW")
                if (alarmObservation != null) {
                    Log.d(TAG, "Static Alarm Observation: $alarmObservation")
                }
                
                // Example: Get static technical alarm
                val technicalAlarm = staticDataManager.getStaticTechnicalAlarm("BATTERY")
                if (technicalAlarm != null) {
                    Log.d(TAG, "Static Technical Alarm: $technicalAlarm")
                }
            } else {
                Log.d(TAG, "Using live alarm data (static test data disabled)")
            }
        }
        
        /**
         * Example of how to modify existing waveform transmission method
         * to use static data when the flag is enabled
         */
        fun exampleWaveformTransmissionWithStaticData(
            paramData: WaveObservationBean, 
            liveSamples: DoubleArray, 
            timeStamp: String,
            measurement: MeasurementData
        ) {
            val staticDataManager = StaticTestDataManager.getInstance()
            
            if (staticDataManager.shouldUseStaticData()) {
                // Use static data instead of live data
                Log.d(TAG, "Transmitting static waveform data for testing")
                
                // Determine waveform type from component code
                val waveformType = when (paramData.componentCode) {
                    "131143" -> "ECG"  // MDC_ECG_LEAD_A
                    "150452" -> "SPO2" // MDC_PULS_OXIM_PLETH
                    "151562" -> "RESP" // MDC_RESP_RATE
                    else -> "UNKNOWN"
                }

                val staticSamples = when (waveformType) {
                    "ECG" -> staticDataManager.getStaticWaveformSamples("ECG")
                    "SPO2" -> staticDataManager.getStaticWaveformSamples("SPO2")
                    "RESP" -> staticDataManager.getStaticWaveformSamples("RESP")
                    else -> null
                }

                if (staticSamples != null) {
                    // Use static samples instead of live samples
                    Log.d(TAG, "Using ${staticSamples.size} static samples for $waveformType (code: ${paramData.componentCode})")
                    // Here you would call the actual transmission method with staticSamples
                    // ApiPostObservations.sendWaveformObservation(paramData, staticSamples, timeStamp)
                } else {
                    Log.w(TAG, "No static data available for $waveformType (code: ${paramData.componentCode}), using live data")
                    // Fall back to live data
                    // ApiPostObservations.sendWaveformObservation(paramData, liveSamples, timeStamp)
                }
            } else {
                // Use live data as normal
                Log.d(TAG, "Using live waveform data")
                // ApiPostObservations.sendWaveformObservation(paramData, liveSamples, timeStamp)
            }
        }
        
        /**
         * Example of how to modify existing vital transmission method
         * to use static data when the flag is enabled
         */
        fun exampleVitalTransmissionWithStaticData(
            measurement: MeasurementData,
            parameter: Triple<String, String, String>,
            uuId: String
        ) {
            val staticDataManager = StaticTestDataManager.getInstance()
            
            if (staticDataManager.shouldUseStaticData()) {
                // Use static vital value instead of live measurement
                Log.d(TAG, "Transmitting static vital data for testing")
                
                val staticValue = staticDataManager.getStaticVitalValue(parameter.first)
                if (staticValue != null) {
                    Log.d(TAG, "Using static value $staticValue for parameter ${parameter.third}")
                    
                    // Create a modified measurement with static value
                    // Note: In real implementation, you would modify the existing measurement
                    // or create a proper MeasurementData instance with all required parameters
                    Log.d(TAG, "Would create modified measurement with static value: $staticValue")
                    Log.d(TAG, "Original measurement paramName: ${measurement.paramName}")
                    Log.d(TAG, "Test UUID: ${staticDataManager.generateTestUUID()}")
                    Log.d(TAG, "Test timestamp: ${staticDataManager.getCurrentTestTimestamp()}")

                    // Here you would call the actual transmission method with modified measurement
                    // In real implementation, you would modify measurement.value with staticValue
                    // ApiPostObservations.sendVitalObservation(measurement, parameter, uuId)
                } else {
                    Log.w(TAG, "No static data available for ${parameter.third}, using live data")
                    // Fall back to live data
                    // ApiPostObservations.sendVitalObservation(measurement, parameter, uuId)
                }
            } else {
                // Use live data as normal
                Log.d(TAG, "Using live vital data")
                // ApiPostObservations.sendVitalObservation(measurement, parameter, uuId)
            }
        }
        
        /**
         * Toggle static test data on/off for testing
         */
        fun toggleStaticTestData(enabled: Boolean) {
            CommonDataArea.useStaticTestData = enabled
            Log.d(TAG, "Static test data ${if (enabled) "ENABLED" else "DISABLED"}")
            
            if (enabled) {
                Log.d(TAG, "All data transmissions will now use static test data from JSON file")
            } else {
                Log.d(TAG, "All data transmissions will now use live sensor data")
            }
        }
        
        /**
         * Run a complete demonstration of all static data features
         */
        fun runCompleteDemo() {
            Log.d(TAG, "=== Static Test Data Demo ===")
            
            // Enable static data for demo
            toggleStaticTestData(true)
            
            // Demonstrate different data types
            demonstrateStaticWaveformData()
            demonstrateStaticVitalData()
            demonstrateStaticAlarmData()
            
            // Disable static data
            toggleStaticTestData(false)
            
            Log.d(TAG, "=== Demo Complete ===")
        }
    }
}
