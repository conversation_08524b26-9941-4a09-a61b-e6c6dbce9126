package com.spacelabs.app.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblSensors")
data class SensorTable(
    @PrimaryKey(autoGenerate = true)
    val sensorId: Int?,
    val sensorDefId: Int?,
    val patientId: Int,
    val sensorType: String,
    val sensorName: String,
    val sensorAddress: String?,
    val registerTimestamp: String,
    @ColumnInfo(defaultValue = "not connected")
    val pairingStatus: String?,
)