package com.spacelabs.app.sensor.helper

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2BloodPressureMonitor
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2DeviceStatus
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.Package
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.data.GetRealTimeData
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.data.GetRealTimeWaveform
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.data.GetRealTimeWaveform.Response.BpMeasureFinishedData.State
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.data.ResponseData
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.models.data.SwitchDeviceStatus
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.dataManager.MeasurementDataManager
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.dialogs.AlertConfirmDialogs
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.HashSet

class BpSensorObserverHelper: SensorObserverHelper() {
    val mainActivity = CommonDataArea.curActivity
    private val settingDaoHelper = SettingDaoHelper(mainActivity!!)
    private val measurementManager = MeasurementDataManager()
    private val handler = Handler(Looper.getMainLooper())

    private var hasBpFinished = true

    enum class BpProperties {
        IS_DEFLATING,
        IS_GET_PULSE,
        IS_IRREGULAR_HEART_RATE,
        PRESSURE,
        SYS_PRESSURE,
        DIA_PRESSURE,
        PULSE_RATE,
        STATE_CODE
    }

    override fun onSensorConnectingAction(sensor: Sensor) {
        Log.d("onBpSensorConnecting", "${sensor.sensorType} Connecting")
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorConnecting, sensor.sensorType)
    }

    override fun onSensorConnectedAction(sensor: Sensor) {
        if(!isSensorSavedOnTheSharedPreference(sensor)) {
            sensor.disconnect(true)
            return
        }
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.CONNECTED)

        patientDataManager.updateSensorsAgainstPatient(sensor)

        SibelSensorManager.sensorManagerHelper.measurementDataStreamController(
            CommonDataArea.bpSysAlarmDao.alarmStatus,
            sensor,
            arrayOf(StreamDataType.BP_SYS, StreamDataType.BP_DIA)
        )
        val bpSensor: BP2BloodPressureMonitor = sensor as BP2BloodPressureMonitor
        CommonDataArea.bpSensor = bpSensor

        bpSensor.sendCommand(SwitchDeviceStatus.Request(SwitchDeviceStatus.TargetStatus.POWER_ON))

        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.BpTimer, CommonDataArea.bpMeasurementPeriodicTime)
        updateSensorConnectedTime(sensor.sensorType)
    }

    override fun onSensorConnectionLostAction(sensor: Sensor) {
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.CONNECTION_LOST)
        CommonDataArea.bpSensorConnectingStatus = SibelSensorManager.SensorScanMode.Reconnecting
        val bpSensorName = SibelSensorManager.getEnrolledSensorNameByType(mnActivity.applicationContext!!, SensorType.BP2_BLOOD_PRESSURE_MONITOR)
        if(!bpSensorName.equals(sensor.name)){
            sensor.disconnect(true)
            return
        }
        Handler(Looper.getMainLooper()).postDelayed({
            SibelSensorManager.sensorManagerHelper.reconnectWhenInRange(
                sensor
            )
        }, 60000)
        LogWriter.writeSensorLog("bp sensor Connection lost ->", sensor.name)
    }

    override fun onSensorDisconnectAction(sensor: Sensor) {

        CommonDataArea.BpSys = Double.NaN
        CommonDataArea.BpDia = Double.NaN
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.DISCONNECTED)
    }

    fun onDeviceStatusUpdatedAction(deviceStatus: BP2DeviceStatus, bloodPressureMonitor: BP2BloodPressureMonitor){
        when(deviceStatus){
            BP2DeviceStatus.CHARGING ->{
                val alertConfirmDialogs = AlertConfirmDialogs(mainActivity!!)
                alertConfirmDialogs.getAlertDialog("BP Patch Charging","${bloodPressureMonitor.name} Charging...",4000)
                  handler.postDelayed({
                        SibelSensorManager.sensorManagerHelper.reconnectWhenInRange(bloodPressureMonitor)
                    }, 60000)
            }
            BP2DeviceStatus.READY -> {
                if(!hasBpFinished) {
                    CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.StreamTypeStart, StreamDataType.BP_SYS)
                    CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.StreamTypeStart, StreamDataType.BP_DIA)
                    updateBPTime(null)
                    hasBpFinished = true
                }
            }
            else -> {
                Log.d("onDeviceStatusUpdated", "Unrecognized Status -> $deviceStatus")
            }
        }
    }

    private fun updateBPTime(bpTime: String?) {
        CommonDataArea.bpTime = bpTime ?: String()
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalCompanionText, StreamDataType.BP)
    }

    fun onPackageReceivedAction(bloodPressureMonitor: BP2BloodPressureMonitor, pkg: Package<ResponseData>) {
        val pkgData = pkg.data
        if(pkgData is GetRealTimeData.Response) {
            when (val data = pkgData.realTimeWaveformResponse.data) {
                is GetRealTimeWaveform.Response.BpMeasureData -> onBpMeasuringAction(bloodPressureMonitor, data.description)
                is GetRealTimeWaveform.Response.BpMeasureFinishedData -> onBpFinishedAction(bloodPressureMonitor, data.description)
                else -> Log.d("OnPackageReceivedAction", "Unknown Data")
            }
        }
    }

    private fun onBpMeasuringAction(bloodPressureMonitor: BP2BloodPressureMonitor, dataDescription: HashSet<Triple<BpProperties, Class<*>, Any>>) {
        if(!CommonDataArea.bpSysAlarmDao.alarmStatus) {
            bloodPressureMonitor.stopMeasurementCommand()
            bloodPressureMonitor.sendCommand(SwitchDeviceStatus.Request(SwitchDeviceStatus.TargetStatus.POWER_ON))
            return
        }

        var hasBpError = false
        for (description in dataDescription) {
            val property = description.first
            val data = getDataByType(description.third, description.second)?:continue
            when(property) {
                BpProperties.STATE_CODE -> hasBpError = onStateCodeReceivedAction(data)
                else -> Log.d("OnBpMeasuringAction", "Unknown Property -> $property -> $data")
            }
        }

        if(hasBpFinished) {
            updateBPTime("Measuring...")
            hasBpFinished = false
        }

        CommonDataArea.bpSysAlarmDao.isAttached = true
        CommonDataArea.bpDiaAlarmDao.isAttached = true
        if(hasBpError)
            bloodPressureMonitor.sendCommand(SwitchDeviceStatus.Request(SwitchDeviceStatus.TargetStatus.POWER_ON))
    }

    private fun onBpFinishedAction(bloodPressureMonitor: BP2BloodPressureMonitor, dataDescription: HashSet<Triple<BpProperties, Class<*>, Any>>) {
        hasBpFinished = true
        val currentTime = TimestampUtils.getCurrentTimeAndMillis()
        var hasBpError = false
        for (description in dataDescription) {
            Log.d("OnBpFinishedAction", description.toString())
            val property = description.first
            val data = getDataByType(description.third, description.second)?: continue
            when(property) {
                BpProperties.DIA_PRESSURE -> onReceiveDiastolic((data as UShort).toDouble())
                BpProperties.SYS_PRESSURE -> onReceiveSystolic((data as UShort).toDouble())
                BpProperties.STATE_CODE -> hasBpError = onStateCodeReceivedAction(data)
                else -> Log.d("OnBpFinishedAction", "Unknown Property -> $property")
            }

            Log.d("OnBpFinishedAction", "BP -> ${CommonDataArea.BpSys}/${CommonDataArea.BpDia}")
        }

        resetOnBpFinished(bloodPressureMonitor)
        val bp = getBpCombineValues()
        if(bp != 0.0)
            measurementManager.onVitalDataUpdatedAction(bloodPressureMonitor, getBpCombineValues(), StreamDataType.BP, currentTime)

        bpMeasurementOutputUpdatedAction(hasBpError)
    }

    private fun onReceiveDiastolic(data: Double) {
        CommonDataArea.BpDia = data
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalReceived, StreamDataType.BP_DIA)
        CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.BpDia, CommonDataArea.BpDia)
    }

    private fun onReceiveSystolic(data: Double) {
        CommonDataArea.BpSys = data
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalReceived, StreamDataType.BP_SYS)
        CommonEventHandler.postAlarmEvent(CurrentAlarmParameter.BpSys, CommonDataArea.BpSys)
    }

    private fun bpMeasurementOutputUpdatedAction(hasError: Boolean) {
        if(hasError) {
            onReceiveSystolic(Double.NaN)
            onReceiveDiastolic(Double.NaN)
        }
        onAttachDetachStatusUpdate(hasError, SensorType.BP2_BLOOD_PRESSURE_MONITOR, CommonDataArea.bpMonitorError ?: String())
    }

    @SuppressLint("SimpleDateFormat")
    private fun resetOnBpFinished(bloodPressureMonitor: BP2BloodPressureMonitor) {
        val dateFormat = SimpleDateFormat("hh:mm a")
        val bpTime: String =
            dateFormat.format(Calendar.getInstance().time)
        CommonDataArea.bpTime = bpTime
        bloodPressureMonitor.sendCommand(SwitchDeviceStatus.Request(SwitchDeviceStatus.TargetStatus.POWER_ON))
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalCompanionText, StreamDataType.BP)
    }

    private fun onStateCodeReceivedAction(data: Any): Boolean {
        val state = data as State
        CommonDataArea.bpMonitorError = when(state) {
            State.SLEEVE_IS_LOOSE -> "Cuff Loose"
            State.DISTURB_DETECTED -> "Disturbance"
            State.WEAK_SIGNAL -> "Weak Signal"
            else -> null
        }

        return CommonDataArea.bpMonitorError != null
    }

    private fun getBpCombineValues(): Double {
        val factor = 1_000_000L
        val combinedX = (CommonDataArea.BpSys * factor).toLong()
        val combinedY = (CommonDataArea.BpDia * factor).toLong()
        val value = (combinedX shl 32) or (combinedY and 0xFFFFFFFFL)
        return value.toDouble()
    }

    private val GetRealTimeWaveform.Response.BpMeasureFinishedData.description: HashSet<Triple<BpProperties, Class<*>, Any>>
        get() {
            return hashSetOf(
                Triple(BpProperties.IS_DEFLATING, isDeflating.javaClass, isDeflating),
                Triple(BpProperties.PRESSURE, pressure.javaClass, pressure),
                Triple(BpProperties.SYS_PRESSURE, systolicPressure.javaClass, systolicPressure),
                Triple(BpProperties.DIA_PRESSURE, diastolicPressure.javaClass, diastolicPressure),
                Triple(BpProperties.PULSE_RATE, pulseRate.javaClass, pulseRate),
                Triple(BpProperties.STATE_CODE, stateCode.javaClass, stateCode),
                Triple(BpProperties.IS_IRREGULAR_HEART_RATE, isIrregularHeartRate.javaClass, isIrregularHeartRate)
            )
        }

    private val GetRealTimeWaveform.Response.BpMeasureData.description: HashSet<Triple<BpProperties, Class<*>, Any>>
        get() {
            return hashSetOf(
                Triple(BpProperties.IS_DEFLATING, isDeflating.javaClass, isDeflating),
                Triple(BpProperties.IS_GET_PULSE, isGetPulse.javaClass, isGetPulse),
                Triple(BpProperties.PRESSURE, pressure.javaClass, pressure),
                Triple(BpProperties.PULSE_RATE, pulseRate.javaClass, pulseRate)
            )
        }

    private fun getDataByType(data: Any, type: Class<*>): Any? {
        return when(type) {
            Boolean::class.java -> data as Boolean
            UShort::class.java -> data as UShort
            Short::class.java -> data as Short
            State::class.java -> data as State
            else -> null
        }
    }

}