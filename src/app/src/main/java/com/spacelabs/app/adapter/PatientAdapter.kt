package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.PatientTable

class PatientAdapter(private var patientList: List<PatientTable> = emptyList()) : RecyclerView.Adapter<PatientAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_patient, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val patientId: TextView = itemView.findViewById(R.id.PatientId)
        private val firstName: TextView = itemView.findViewById(R.id.FristName)
        private val middleName: TextView = itemView.findViewById(R.id.MiddleName)
        private val lastName: TextView = itemView.findViewById(R.id.LastName)
        private val patientID1: TextView = itemView.findViewById(R.id.PatientID1)
        private val patientID2: TextView = itemView.findViewById(R.id.PatientID2)
        private val dob: TextView = itemView.findViewById(R.id.DOB)
        private val age: TextView = itemView.findViewById(R.id.Age)
        private val gender: TextView = itemView.findViewById(R.id.Gender)
        private val height: TextView = itemView.findViewById(R.id.Height)
        private val weight: TextView = itemView.findViewById(R.id.Weight)
        private val bedName: TextView = itemView.findViewById(R.id.BedName)
        private val adminDate: TextView = itemView.findViewById(R.id.AdminDate)
        private val facilityName: TextView = itemView.findViewById(R.id.FacilityName)
        private val createdOn: TextView = itemView.findViewById(R.id.CreatedOn)
        private val status: TextView = itemView.findViewById(R.id.Status)
        private val isTempPatient: TextView = itemView.findViewById(R.id.isTempPatient)

        fun bind(patient: PatientTable) {
            patientId.text = patient.patientId.toString()
            firstName.text = patient.firstName
            middleName.text = patient.middleName
            lastName.text = patient.lastName
            patientID1.text = patient.patientID1
            patientID2.text = patient.patientID2
            dob.text = patient.dob
            age.text = patient.age.toString()
            gender.text = patient.gender
            height.text = patient.height.toString()
            weight.text = patient.weight.toString()
            bedName.text = patient.bedName
            adminDate.text = patient.admitDate
            facilityName.text = patient.facilityName
            createdOn.text = patient.createdOn
            status.text = patient.status
            isTempPatient.text = patient.isTempPatient.toString()
        }
    }
}

