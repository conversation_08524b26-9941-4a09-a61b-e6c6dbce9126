package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.EventsLogTable

class EventLogAdapter(private var patientList: List<EventsLogTable> = emptyList()) : RecyclerView.Adapter<EventLogAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_event_log, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val eventLogId: TextView = itemView.findViewById(R.id.EventLogId)
        private val eventId: TextView = itemView.findViewById(R.id.EventId)
        private val patientId: TextView = itemView.findViewById(R.id.PatientId)
        private val patientID1: TextView = itemView.findViewById(R.id.PatientID1)
        private val timeStamp: TextView = itemView.findViewById(R.id.TimeStamp)

        fun bind(patient: EventsLogTable) {
            eventLogId.text = patient.eventLogId.toString()
            eventId.text = patient.eventId.toString()
            patientId.text = patient.patientId.toString()
            patientID1.text = patient.patientID1
            timeStamp.text = patient.timeStamp
        }
    }
}

