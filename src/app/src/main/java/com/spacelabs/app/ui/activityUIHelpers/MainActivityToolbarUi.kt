package com.spacelabs.app.ui.activityUIHelpers

import android.os.Build
import android.os.CountDownTimer
import android.text.Html
import android.text.Spannable
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.Toolbar
import com.spacelabs.app.R
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.MainActivity
import com.spacelabs.app.alarms.SibelAlarmManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.ui.MainActivityUIHelper
import kotlinx.coroutines.delay
import java.util.*

class MainActivityToolbarUi(mainActivity: MainActivity) : ActivityUiManager(mainActivity) {

    private lateinit var toolbar: Toolbar

    private lateinit var pauseAlarmText: TextView
    private lateinit var chestSensorName: TextView
    private lateinit var limbSensorName: TextView
    private lateinit var bpSensorName: TextView

    private lateinit var sensorInfo: LinearLayout
    private lateinit var chestSensorInfo: LinearLayout
    private lateinit var limbSensorInfo: LinearLayout
    private lateinit var bpSensInfo: LinearLayout
    private lateinit var pauseAlarmView: LinearLayout

    private lateinit var pauseCancelBtn: Button

    private lateinit var appName: String
    private var countDownTimer: CountDownTimer? = null

    private val mainActivity: MainActivity

    init {
        this.mainActivity = mainActivity
    }

    override fun beforeUiInit() {
        toolbar = mainActivity.findViewById(R.id.appToolbar)
        appName = mainActivity.resources.getString(R.string.spacelabsHub)
        displayCurrentPatientName()

    }

    override fun initUi() {
        pauseAlarmView = mainActivity.findViewById(R.id.pauseAlarmView)
        pauseAlarmText = mainActivity.findViewById(R.id.pauseAlarmText)
        pauseCancelBtn = mainActivity.findViewById(R.id.cancelPause)

        sensorInfo = mainActivity.findViewById(R.id.sensorInfo)
        chestSensorInfo = mainActivity.findViewById(R.id.chestSensInfo)
        chestSensorName = mainActivity.findViewById(R.id.chestSensName)
        limbSensorInfo = mainActivity.findViewById(R.id.limbSensInfo)
        limbSensorName = mainActivity.findViewById(R.id.limbSensName)
        bpSensInfo = mainActivity.findViewById(R.id.bpSensInfo)
        bpSensorName = mainActivity.findViewById(R.id.bpSensName)
    }

    override fun postUiInitLooperAction() {

    }

    override fun postUiInitAction() {
        mainActivity.setSupportActionBar(toolbar)
        mainActivity.supportActionBar?.setDisplayHomeAsUpEnabled(false)
        displayCurrentPatientName()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun uiActionListeners() {
        toolbar.setOnClickListener {
            mainActivity.dialog.getPatientInformation()
        }
        sensorInfo.setOnClickListener {
            mainActivity.dialog.getSensorListDialog()
        }
        pauseCancelBtn.setOnClickListener {
            endPauseAlarm()
        }
    }

    suspend fun updateSensorInfoOnToolBar(sensorType: SensorType) {
        val layoutAndTextView: Pair<LinearLayout, TextView> = when (sensorType) {
            SensorType.CHEST -> Pair(chestSensorInfo, chestSensorName)
            SensorType.LIMB -> Pair(limbSensorInfo, limbSensorName)
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> Pair(bpSensInfo, bpSensorName)
            else -> null
        } ?: return
        val chestSensor =
            SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.CHEST)
        val limbSensor =
            SibelSensorManager.getEnrolledSensorNameByType(context, SensorType.LIMB)
        val bpSensor = SibelSensorManager.getEnrolledSensorNameByType(
            context,
            SensorType.BP2_BLOOD_PRESSURE_MONITOR
        )
        val sensorName = SibelSensorManager.getEnrolledSensorNameByType(context, sensorType)
        if (!sensorName.isNullOrEmpty()) {
            layoutAndTextView.first.visibility = View.VISIBLE
            layoutAndTextView.second.text =
                Html.fromHtml(sensorName, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            while (CommonDataArea.runConnectionStatusUpdateUiHandler) {
                CommonDataArea.isToolBarCoroutineRunning = true
                if (chestSensor!!.isNotEmpty() || limbSensor!!.isNotEmpty() || bpSensor!!.isNotEmpty()) {
                    MainActivityUIHelper().updateDrawableByBatteryStatus(
                        layoutAndTextView.second,
                        sensorType
                    )
                }
                delay(50000)
            }
        } else {
            layoutAndTextView.first.visibility = View.GONE
        }
    }

    fun displayCurrentPatientName() {
        val patient = CommonDataArea.PATIENT
        if (patient.isPatientTemp) {
            toolbar.title =
                Html.fromHtml(appName, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            toolbar.showOverflowMenu()
        } else {
            val name = "${patient.patientId1} - ${patient.firstName} ${patient.lastName}"
            val patientName = name.replaceFirstChar {
                if (it.isLowerCase()) it.titlecase(
                    Locale.getDefault()
                ) else it.toString()
            }
            val arr = patientName.split(" ".toRegex(), 2)
            toolbar.title = Html.fromHtml(
                "<p><strong>${arr[0].uppercase(Locale.getDefault())} </strong>${arr[1]}</p>",
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            toolbar.showOverflowMenu()
        }
    }

    fun startPauseAlarm() {
        if (countDownTimer != null)
            return

        toolbar.visibility = View.GONE
        pauseAlarmView.visibility = View.VISIBLE
        startPauseAlarmCountDown(pauseAlarmText)
    }

    private fun endPauseAlarm() {
        countDownTimer?.cancel()

        countDownTimer = null
        SibelAlarmManager.pauseAlarms = false
        pauseAlarmView.visibility = View.GONE
        toolbar.visibility = View.VISIBLE
    }

    private fun startPauseAlarmCountDown(textView: TextView) {
        countDownTimer = object : CountDownTimer(120000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                SibelAlarmManager.pauseAlarms = true
                if (CommonDataArea.curActivity != null) {
                    val minutes = ((millisUntilFinished / 1000) / 60).toInt()
                    val seconds = ((millisUntilFinished / 1000) % 60).toInt()
                    val timeLeftFormatted =
                        String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds)
                    textView.text = Html.fromHtml(
                        "All Alarms Paused - <strong>$timeLeftFormatted</strong>",
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }

            override fun onFinish() {
                endPauseAlarm()
            }

        }.start()
    }

    data class ActivePatientDetails(
        val name: String,
        val patientId: Int,
    )
}