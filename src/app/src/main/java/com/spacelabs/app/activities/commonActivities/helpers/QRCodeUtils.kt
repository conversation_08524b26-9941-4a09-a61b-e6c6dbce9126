package com.spacelabs.app.activities.commonActivities.helpers

import android.graphics.Bitmap
import android.util.Log
import com.google.zxing.BarcodeFormat
import com.google.zxing.MultiFormatWriter
import com.google.zxing.common.BitMatrix
import java.security.InvalidKeyException
import java.security.KeyFactory
import java.security.NoSuchAlgorithmException
import java.security.PublicKey
import java.security.spec.InvalidKeySpecException
import java.security.spec.KeySpec
import java.security.spec.X509EncodedKeySpec
import java.util.Base64
import java.util.UUID
import javax.crypto.BadPaddingException
import javax.crypto.Cipher
import javax.crypto.IllegalBlockSizeException
import javax.crypto.NoSuchPaddingException
import javax.crypto.SecretKey
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.PBEKeySpec
import javax.crypto.spec.SecretKeySpec

internal object QRCodeUtils {

    private var publicKeyString ="MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo2GC/8n4Vy9mhTbruq3qt6YA/6g6Z62Jnc5jaRkNreshJZQzqXBfGw5wdBRGtYXR61fT9h6ZUhbCsoKJRcAtLQ/aBtkDHnFhNCpnZljeXd6OXj2jJSgtH1eEtthQ78R5Zc20PVRctLrHCkoWC88gyPznEf9WxQ0jPMZzRVKtSUg84EqXZTMETl/hlrZKdCQjhmaHX/HQFm0I6JVQxfVotPQWZsihAOENSNuXQ9YQ0hUJLmt6afAtyH1pe2ilXMRsj5k1egg+y1dl9V5Z4jnDzlYKi+HtBUPrFBB3eJgB0p9pt+6nVe1qLz0ZT3BEbzIdEaZHMvBJsuf8VuY5S2CcfwIDAQAB"

   internal fun generateRandomUUID(): String {
        // Generate a random UUID
        return UUID.randomUUID().toString()
    }

    internal fun generateRandomPassphrase(length: Int): String {
        val allowedChars =
            ('A'..'Z') + ('a'..'z') + ('0'..'9') + listOf('!', '@', '#', '$', '%', '&', '*')
        return (1..length)
            .map { allowedChars.random() }
            .joinToString("")
    }


    internal fun loadPublicKeyFromAssets(): PublicKey? {
        return try {
            val publicKeyBytes: ByteArray = java.util.Base64.getDecoder().decode(publicKeyString)
            val keyFactory = KeyFactory.getInstance("RSA")
            keyFactory.generatePublic(X509EncodedKeySpec(publicKeyBytes))
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    internal fun encryptWithPublicKey(publicKey: PublicKey?,data: String): String {
        try {

            val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
            cipher.init(Cipher.ENCRYPT_MODE, publicKey)
            val encryptedBytes = cipher.doFinal(data.toByteArray())
            return android.util.Base64.encodeToString(encryptedBytes, android.util.Base64.DEFAULT)
        } catch (e: InvalidKeySpecException) {
            Log.e("Encryption", "Invalid key format: ${e.message}")
        } catch (e: NoSuchAlgorithmException) {
            Log.e("Encryption", "Algorithm not supported: ${e.message}")
        } catch (e: NoSuchPaddingException) {
            Log.e("Encryption", "Padding not supported: ${e.message}")
        } catch (e: InvalidKeyException) {
            Log.e("Encryption", "Invalid key: ${e.message}")
        } catch (e: BadPaddingException) {
            Log.e("Encryption", "Bad padding: ${e.message}")
        } catch (e: IllegalBlockSizeException) {
            Log.e("Encryption", "Illegal block size: ${e.message}")
        } catch (e: Exception) {
            Log.e("Encryption", "Unexpected error: ${e.message}")
        }
        return "" // Handle encryption failure
    }
    internal fun generateQRCode(content: String): Bitmap? {
        try {
            /*    val displayMetrics = DisplayMetrics()
                accountConfigActivity.windowManager.defaultDisplay.getMetrics(displayMetrics)

                val screenWidth = displayMetrics.widthPixels
                val screenHeight = displayMetrics.heightPixels

                val size = if (screenWidth < screenHeight) screenWidth else screenHeight
    */
            val multiFormatWriter = MultiFormatWriter()
            val bitMatrix: BitMatrix =
                multiFormatWriter.encode(content, BarcodeFormat.QR_CODE, 550, 550)
            val width = bitMatrix.width
            val height = bitMatrix.height
            val bmp = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888) // Use ARGB_8888 for transparency

            val black = -0x1000000 // Black color
            val white = -0x1// white color

            for (x in 0 until width) {
                for (y in 0 until height) {
                    val color = if (bitMatrix[x, y]) black else white
                    bmp.setPixel(x, y, color)
                }
            }

            return bmp
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("QRcode", "generateAndDisplayQRCode: ${e.stackTraceToString()}")
        }
        return null
    }

    internal fun decryptWithPassphrase(encryptedString: String, passphrase: String): String {
        try {
            val encryptedData: ByteArray = Base64.getDecoder().decode(encryptedString)


            // Convert passphrase to byte array
            val passphraseBytes = passphrase.toByteArray(Charsets.UTF_8)

            // Use passphrase as salt
            val salt = passphraseBytes

            // Generate AES key from passphrase and salt
            val secretKey: SecretKey = generateKeyFromPassphrase(passphraseBytes, salt)

            // Decrypt the data
            val cipher: Cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
            cipher.init(Cipher.DECRYPT_MODE, secretKey, IvParameterSpec(passphraseBytes))
            val decryptedData: ByteArray = cipher.doFinal(encryptedData)

            return String(decryptedData, Charsets.UTF_8)
        }
        catch (e: Exception) {
            e.printStackTrace()
            Log.e("DecryptError", "Error during decryption", e)

            return "err"
        }
    }

    private fun generateKeyFromPassphrase(passphrase: ByteArray, salt: ByteArray): SecretKey {
        // Derive a key from the passphrase using a key derivation function (KDF)
        val iterationCount = 10000 // Adjust the iteration count as needed
        val spec: KeySpec = PBEKeySpec(String(passphrase, Charsets.UTF_8).toCharArray(), salt, iterationCount, 256)
        val factory: SecretKeyFactory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256")
        val secretKeyBytes: ByteArray = factory.generateSecret(spec).encoded

        return SecretKeySpec(secretKeyBytes, "AES")
    }
}