package com.spacelabs.app.api.data.observations

import com.google.gson.GsonBuilder
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.api.data.dataclasses.BpObservationBean
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.iomt.IomtApiManager

class NibpObservationData: ObservationManager() {

    companion object{
        private const val DEVICE_DISPLAY = "Blood Pressure Monitor"
    }

    data class Observation(
        val resourceType: String,
        val id: String,
        val meta: Meta,
        val device: Device,
        val status: String,
        val category:  List<Category>,
        val code: Code,
        val subject: Subject,
        val effectiveDateTime: String,
        val performer: List<Performer>,
        val bodySite: BodySite,
        val component: List<Component>
    )

    data class Meta(
        val profile: List<String>
    )

    data class Device(
        val reference: String,
        val display: String
    )

    data class Category(
        val coding: List<Coding>
    )

    data class Coding(
        val system: String,
        val code: String,
        val display: String
    )

    data class Code(
        val coding: List<Coding>,
        val text: String
    )

    data class Subject(
        val reference: String,
        val display: String
    )

    data class Performer(
        val reference: String
    )

    data class BodySite(
        val coding: List<Coding>
    )

    data class Component(
        val code: Code,
        val valueQuantity: ValueQuantity
    )

    data class ValueQuantity(
        val value: Int,
        val unit: String,
        val system: String,
        val code: String
    )

    fun getObservationJson(observationData: BpObservationBean): String {
        val meta = Meta(listOf(hl7VitalSignUrl))
        val device = Device("Device/${IomtApiManager.DEVICE_UUID}", DEVICE_DISPLAY)
        val status = observationData.status
        val category = listOf(Category(listOf(Coding(hl7SysValue, observationData.categoryCode, observationData.categoryDisplay))))
        val coding1 = Coding(loincSysValue, bp.first, bp.third)
        val coding2 = Coding(loincSysValue, bpSys.first, bpSys.third)
        val coding3 = Coding(loincSysValue, bpDia.first, bpDia.third)
        val code = Code(listOf(coding1), observationData.codeText)
        //val subject = Subject("Patient/" + SnsApiManager.apiResponseData.patient.patientID1, SnsApiManager.apiResponseData.patientName)
        val subject = Subject("Patient/" +  CommonDataArea.PATIENT.patientId1, CommonDataArea.PATIENT.firstName)
        val performer = listOf(Performer("Practitioner/f005"))
        val coding4 = Coding(observationData.bodySiteSystem, observationData.bodySiteCode, observationData.bodySiteDisplay)
        val bodySite = BodySite(listOf(coding4))
        val valueQuantity1 = ValueQuantity(observationData.bpSys, bpSys.second, observationData.valQuantitySys, observationData.valQuantityCode)
        val valueQuantity2 = ValueQuantity(observationData.bpDia, bpDia.second, observationData.valQuantitySys, observationData.valQuantityCode)
        val component1 = Component(Code(listOf(coding2), ""), valueQuantity1)
        val component2 = Component(Code(listOf(coding3), ""), valueQuantity2)
        val component = listOf(component1, component2)

        val observation = Observation(
            observationData.resourceType,
            observationData.uuId,
            meta,
            device,
            status,
            category,
            code,
            subject,
            observationData.timestamp,
            performer,
            bodySite,
            component
        )

        val gson = GsonBuilder().create()
        return gson.toJson(observation)
    }
}