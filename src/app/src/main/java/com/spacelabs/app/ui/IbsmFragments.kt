package com.spacelabs.app.ui

import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.spacelabs.app.activities.fragments.ManageSensorsFragment
import com.spacelabs.app.activities.fragments.ParamSettingsFragment
import com.spacelabs.app.activities.fragments.TrendGraphFragment

class IbsmFragments(val activity: AppCompatActivity) {

    enum class Windows {
        HR_SETTINGS,
        RR_SETTINGS,
        SPO2_SETTINGS,
        TEMP_SETTINGS,
        BP_SETTINGS,
        ACTIVITY_SETTINGS,
        GENERAL_SETTINGS,
        TREND_GRAPHS,
        MANAGE_SENSORS,
        PATIENT_INFO    //To be Implemented
    }

    fun getFragment(fragmentWindow: Windows, frameLayout: FrameLayout) {
        val fragment: Fragment = when(fragmentWindow) {
            Windows.MANAGE_SENSORS -> ManageSensorsFragment(frameLayout)
            Windows.TREND_GRAPHS -> TrendGraphFragment(frameLayout)
            else -> getParamSettingFragment(fragmentWindow, frameLayout)
        } ?: return

        activity.supportFragmentManager.beginTransaction().apply {
            replace(frameLayout.id, fragment)
            addToBackStack(null)
            commit()
        }
    }

    private fun getParamSettingFragment(window: Windows, frameLayout: FrameLayout): Fragment? {
        val navBtn = when(window) {
            Windows.HR_SETTINGS -> ParamSettingsFragment.NavButtons.ECG
            Windows.RR_SETTINGS -> ParamSettingsFragment.NavButtons.RESP
            Windows.SPO2_SETTINGS -> ParamSettingsFragment.NavButtons.SPO2
            Windows.TEMP_SETTINGS -> ParamSettingsFragment.NavButtons.TEMP
            Windows.BP_SETTINGS -> ParamSettingsFragment.NavButtons.BP
            Windows.ACTIVITY_SETTINGS -> ParamSettingsFragment.NavButtons.ACTIVITY
            Windows.GENERAL_SETTINGS -> ParamSettingsFragment.NavButtons.SETTINGS
            else -> return null
        }
        return ParamSettingsFragment(navBtn, frameLayout)
    }
}