package com.spacelabs.app.api

import android.util.Log
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.spacelabs.app.api.data.dataclasses.AlarmObservationBean
import com.spacelabs.app.api.data.dataclasses.BpObservationBean
import com.spacelabs.app.api.data.dataclasses.Component
import com.spacelabs.app.api.data.dataclasses.VitalObservationBean
import com.spacelabs.app.api.data.dataclasses.VitalObservationBeanOld
import com.spacelabs.app.api.data.dataclasses.WaveObservationBean
import com.spacelabs.app.api.data.observations.AlarmObservationData
import com.spacelabs.app.api.data.observations.NibpObservationData
import com.spacelabs.app.api.data.observations.ObservationManager
import com.spacelabs.app.api.data.observations.VitalObservationData
import com.spacelabs.app.api.data.observations.VitalObservationDataOld
import com.spacelabs.app.api.data.observations.WaveformObservationData
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.dao.AlarmBoxDao
import com.spacelabs.app.database.objectbox.helpers.AlarmBoxDaoHelper
import java.util.AbstractMap.SimpleEntry

class ApiPostObservations {

    private val TAG="SNS_ApiPostObservations"
    private val TAG_2="SNS_WAVE_Observations"
    private val vitalObservation = VitalObservationData()
    private val vitalObservation1 = VitalObservationDataOld()
    private val waveformObservation = WaveformObservationData()
    private val nibpObservation = NibpObservationData()
    private val alarmObservation = AlarmObservationData()
    private val alarmBoxDao = AlarmBoxDaoHelper()
    private val encryptionVitalConverter = EncryptionVitalConverter()

    companion object{
        lateinit var ecgObservation: WaveObservationBean
        lateinit var respObservation: WaveObservationBean
        lateinit var ppgIrObservation: WaveObservationBean

        private const val HL7_MDC_CODE_SYSTEM = "urn:oid:2.16.840.1.113883.6.24"
    }

    init {
        ecgObservation = setupEcgObservation()
        respObservation = setupRespObservation()
        ppgIrObservation = setupPpgIrObservation()
    }

    fun sendVitalObservation1(measurementData: MeasurementData, parameter: Triple<String, String, String>, uuId: String){
        try{
            val component = Component(
                system = "http://loinc.org",  // Assuming LOINC system for the components
                code = parameter.first,
                display = parameter.third,
                value = encryptionVitalConverter.convertToEntityProperty(measurementData.value!!).toString(),
                unit = parameter.second
            )


            val paramData = VitalObservationBean(
                components = listOf(component),
                timestamp = measurementData.timestamp,
                uuId = uuId
            )
            val observation = vitalObservation.getObservation(paramData).toString()
            if(SnsApiManager.webSocketClient != null && SnsApiManager.webSocketClient!!.isOpen){
                SnsApiManager.webSocketClient!!.send(observation)
                Log.d(TAG, "VitalObservation: ${observation} ")

            }
        } catch (ex: Exception){
            Log.d("VitalApiException", ex.stackTraceToString())
        }
    }

    fun sendVitalObservation(measurementData: MeasurementData, parameter: Triple<String, String, String>, uuId: String){
        try{
          if(measurementData.paramName!=StreamDataType.BODY_POSITION.name) {
              val paramData = VitalObservationBeanOld(
                  value = encryptionVitalConverter.convertToEntityProperty(measurementData.value!!)
                      .toString(),
                  vitalCode = parameter.first,
                  vitalDisplay = parameter.third,
                  valueUnit = parameter.second,
                  timestamp = measurementData.timestamp,
                  uuId = uuId
              )
              val observation = vitalObservation1.getObservation(paramData)

              if (SnsApiManager.webSocketClient != null && SnsApiManager.webSocketClient!!.isOpen) {
                  SnsApiManager.webSocketClient!!.send(observation)
                  Log.d(TAG, "VitalObservation: ${observation}")

              }
          }else {
              val valForConverting =
                  encryptionVitalConverter.convertToEntityProperty(measurementData.value!!)?.toInt()

              val paramData = VitalObservationBeanOld(
                  value = mapPosition(valForConverting)
                      .toString(),
                  vitalCode = parameter.first,
                  vitalDisplay = parameter.third,
                  valueUnit = parameter.second,
                  timestamp = measurementData.timestamp,
                  uuId = uuId
              )
              val observation = vitalObservation1.getObservation(paramData)

              if (SnsApiManager.webSocketClient != null && SnsApiManager.webSocketClient!!.isOpen) {
                  SnsApiManager.webSocketClient!!.send(observation)
                  if (measurementData.paramName == StreamDataType.BODY_POSITION.name)
                      Log.d(
                      TAG,
                      "MDC_BODY_POSITION: ${observation} "
                  )

              }


          }
        } catch (ex: Exception){
            Log.d("VitalApiException", ex.stackTraceToString())
        }
    }


    /*From SNS DOC*/
    /* Value between 1-5 to indicate positioning
        case "1" -> "Upright";
        case "2" -> "Supine";
        case "3" -> "Prone";
        case "4" -> "Right";
        case "5" -> "Left";
        */
    fun mapPosition(value: Int?): Int {
        return when (value) {
            0 -> 1  // UPRIGHT -> 1
            1 -> 2  // SUPINE -> 2
            2 -> 3  // PRONE -> 3
            3 -> 4  // RIGHT -> 4
            4 -> 5  // LEFT -> 5
            else -> throw IllegalArgumentException("Invalid position value: $value")
        }
    }


    fun sendWaveformObservation(paramData: WaveObservationBean, samples: DoubleArray, timeStamp: String) {
        try{
            paramData.timestamp = timeStamp
            val observation = waveformObservation.createWaveformObservation(paramData, samples).toString()
            if(SnsApiManager.webSocketClient != null && SnsApiManager.webSocketClient!!.isOpen){
                SnsApiManager.webSocketClient!!.send(observation)
                     Log.d(TAG_2, "WaveformObservation: ${observation} ")
            }
        } catch(ex: Exception){
            //SnsApiManager.connectFhir()
            Log.d(TAG," WaveApiException ${ex.stackTraceToString() }")
        }
    }

    fun sendNIBPObservation(niBp: String, timeStamp: String, uuId: String){
        val splitNiBp = niBp.split("/")
        try{
            val bpObservationData = BpObservationBean(
                timestamp = timeStamp,
                bpSys = splitNiBp.first().toInt(),
                bpDia = splitNiBp.last().toInt(),
                uuId = uuId
            )
            val observation = nibpObservation.getObservationJson(bpObservationData)
            if(SnsApiManager.webSocketClient != null && SnsApiManager.webSocketClient!!.isOpen){
                SnsApiManager.webSocketClient!!.send(observation)
                Log.d(TAG, "NIBPObservation: ${observation} ")

            }
        } catch(ex: Exception){
            Log.d("NiBpApiException", ex.stackTraceToString())
        }
    }

    fun sendAlarmObservation(alarmAndEvents: AlarmBoxDao.AlarmDataWithEvents){
        val isAcknowledged = alarmAndEvents.alarmData.isAcknowledged != 0
        val tripleAndRanges = getAlarmRangeMapByAlarmName(alarmAndEvents.alarmData.alarmName) ?: return

        for(event in alarmAndEvents.alarmEvents){
            val alarmEndTime = getAlarmEndTime(alarmAndEvents.alarmData.endTime, event.alarmStatus)
            val observationData = AlarmObservationBean(
                observationId = event.alarmEventUUID,
                alarmType = event.alarmStatus,
                isAcknowledged = isAcknowledged,
                vitalTriple = tripleAndRanges.key,
                alarmStartTime = event.updatedTime,
                alarmEndTime = alarmEndTime,
                vitalValue = event.value.toFloat().toString(),
                vitalRanges = tripleAndRanges.value
            )
            val observation = alarmObservation.getObservationJson(observationData)

            if(SnsApiManager.webSocketClient != null && SnsApiManager.webSocketClient!!.isOpen){
                SnsApiManager.webSocketClient!!.send(observation)
                alarmBoxDao.updateAlarmEventUploadStatusByUuid(event, status = 1)
                Log.d(TAG, "AlarmObservation: ${observation} ")

            }
        }
    }

    private fun getAlarmEndTime(endTime: String?, alarmsStatus: String): String?{
        return if(alarmsStatus.equals("end", true)) endTime else null
    }

    private fun getAlarmRangeMapByAlarmName(alarmName: String): Map.Entry<Triple<String, String, String>, Map<String, Int>>?{
        val splitAlarm = alarmName.split(" ")
        return when(splitAlarm.last()){
            StreamDataType.HR.name -> SimpleEntry(ObservationManager.HR, AlarmObservationBean.hrRanges)
            "RR" -> SimpleEntry(ObservationManager.RR, AlarmObservationBean.rrRanges)
            "SpO2" -> SimpleEntry(ObservationManager.SpO2, AlarmObservationBean.spo2Ranges)
            "TEMP_SKIN" -> SimpleEntry(ObservationManager.TempForChest, AlarmObservationBean.tempRanges)
            "BP_DIA" -> SimpleEntry(ObservationManager.bpDia, AlarmObservationBean.bpDiaRanges)
            "BP_SYS" -> SimpleEntry(ObservationManager.bpSys, AlarmObservationBean.bpSysRanges)
            else -> null
        }
    }

    private fun setupEcgObservation(): WaveObservationBean {
        return WaveObservationBean(
            period = 3.90625,
            factor = 1.0,
            origin = 0/*getOrigin(4095, 0)*/,
            originalUpperLimit = 6.25,
            originalLowerLimit = -6.25,
            apiUpperLimit = 4095,
            apiLowerLimit = 0,
            code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "131143", "display" to "MDC_ECG_LEAD_A"),
            //code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "131328", "display" to "MDC_ECG_ELEC_POTL"),
            //deviceDisplay = "MDC_ECG_LEAD_A",
            deviceDisplay = "SLHUB-ECG",
            componentCode = "131143",
        )
        //MDC_ECG_LEAD_A(131143),
    }

    private fun setupPpgIrObservation(): WaveObservationBean{
        return WaveObservationBean(
            period = 15.625,
            factor = 1.0,
            origin = 0,
            originalUpperLimit = 65535.0,
            originalLowerLimit = 0.0,
            apiUpperLimit = 65535,   //2368
            apiLowerLimit = 0,   //1856
            //code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "150452", "display" to "MDC_PULS_OXIM_PLETH"),
            code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "150452", "display" to "MDC_PULS_OXIM_PLETH"),
            //deviceDisplay = "MDC_PULS_OXIM_PLETH",
            deviceDisplay = "SLHUB-PPG-IR",
            componentCode = "150452"
        )
//        //MDC_PULS_OXIM_PLETH(150452),
    }

    private fun setupRespObservation(): WaveObservationBean{
        return WaveObservationBean(
            period = 38.461538462,
            factor = 1.0,
            origin = getOrigin(2432, 1792),
            originalUpperLimit = 65536.0,
            originalLowerLimit = 0.0,
            apiUpperLimit = 2368,
            apiLowerLimit = 1856,
            code = mapOf("system" to HL7_MDC_CODE_SYSTEM, "code" to "151562", "display" to "MDC_RESP_RATE"),
            deviceDisplay = "SLHUB-RR",
            componentCode = "151562"
        )
    }

    private fun getOrigin(upper: Int, lower: Int): Int{
        val center = (upper - lower) / 2
        return center + lower
    }

}