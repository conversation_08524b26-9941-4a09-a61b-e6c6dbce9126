package com.spacelabs.app.sensor.helper

import android.os.Handler
import android.os.Looper
import com.spacelabs.app.R
import com.sibelhealth.bluetooth.sensor.sibel.chest.ChestSensor
import com.sibelhealth.bluetooth.sensor.sibel.model.CommandType
import com.sibelhealth.bluetooth.sensor.sibel.model.ResetCountParam
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.charts.XYDataSource
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.sensor.SibelSensorManager

class ChestSensorObserverHelper: SensorObserverHelper() {

    override fun onSensorConnectingAction(sensor: Sensor){
        if(isSensorSavedOnTheSharedPreference(sensor))
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorConnecting, sensor.sensorType)
    }

    override fun onSensorConnectedAction(sensor: Sensor) {
        if(!isSensorSavedOnTheSharedPreference(sensor)) {
            sensor.disconnect(true)
            return
        }

        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.CONNECTED)

        val chestSensor: ChestSensor = sensor as ChestSensor
        chestSensor.sendCommand(CommandType.RESET_COUNT, ResetCountParam.FALL)
        chestSensor.sendCommand(CommandType.RESET_COUNT, ResetCountParam.STEP)

        patientDataManager.updateSensorsAgainstPatient(sensor)

        val ecgStreamArray = arrayOf(StreamDataType.ECG, StreamDataType.HR, StreamDataType.FALL_COUNT, StreamDataType.BODY_POSITION, StreamDataType.ACCEL, StreamDataType.ACCEL_X, StreamDataType.ACCEL_Y, StreamDataType.ACCEL_Z, StreamDataType.STEP_COUNT)

        SibelSensorManager.sensorManagerHelper.measurementDataStreamController(CommonDataArea.hrAlarmDao.alarmStatus, sensor, ecgStreamArray)
        SibelSensorManager.sensorManagerHelper.measurementDataStreamController(CommonDataArea.respAlarmDao.alarmStatus, sensor, arrayOf(StreamDataType.RR, StreamDataType.RR_WAVEFORM))
        SibelSensorManager.sensorManagerHelper.measurementDataStreamController(CommonDataArea.tempAlarmDao.alarmStatus, sensor, arrayOf(StreamDataType.TEMP_SKIN))

        CommonDataArea.chestSensor = chestSensor

        updateSensorConnectedTime(sensor.sensorType)
        LogWriter.writeSensorLog("Chest sensor Connected ->", sensor.name)
    }

    override fun onSensorConnectionLostAction(sensor: Sensor) {
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.CONNECTION_LOST)
        CommonDataArea.chestSensorConnectingStatus = SibelSensorManager.SensorScanMode.Reconnecting
        val chestSensorName = SibelSensorManager.getEnrolledSensorNameByType(mnActivity.applicationContext!!, SensorType.CHEST)
        if(!chestSensorName.equals(sensor.name)){
            sensor.disconnect(true)
            return
        }
        Handler(Looper.getMainLooper()).postDelayed({
            SibelSensorManager.sensorManagerHelper.reconnectWhenInRange(
                sensor
            )
        }, 60000)
        LogWriter.writeSensorLog("Chest sensor Connection lost ->", sensor.name)
    }

    override fun onSensorDisconnectAction(sensor: Sensor) {
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.DISCONNECTED)
        LogWriter.writeSensorLog("Chest sensor Disconnected ->", sensor.name)
    }

    fun onLeadOffStatusUpdatedAction(chestSensor: ChestSensor, isLeadOff: Boolean){
        try {
            val leadOffMessage = mnActivity.getString(R.string.leadOff)
            onAttachDetachStatusUpdate(isLeadOff, chestSensor.sensorType, leadOffMessage)
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onLeadOffStatusUpdated", e.stackTraceToString())

        }
    }

    fun resetPointerAndMasterStreamForLeadOff(isLeadOff:Boolean) {
        if (isLeadOff){
            CommonDataArea.isECGWritePtrUpdated = false
            CommonDataArea.isRESPWritePtrUpdated = false
            CommonDataArea.masterStream = XYDataSource.XYDataType.PPG_IR.name
        }
    }





}