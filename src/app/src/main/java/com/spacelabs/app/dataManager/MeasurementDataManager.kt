package com.spacelabs.app.dataManager

import android.annotation.SuppressLint
import android.util.Log
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.objectbox.encryption.EncryptionConverter
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.interfaces.IOEventCallback
import io.objectbox.Box
import kotlinx.coroutines.*
import java.util.*

class MeasurementDataManager {

    companion object {
        private val measurementBox: Box<MeasurementData> = ObjectBoxManager.getMeasurementBox()
    }
    private val encryptionConverter = EncryptionConverter()
    private val encryptionVitalConverter = EncryptionVitalConverter()

    fun onVitalDataUpdatedAction(
        sensor: Sensor,
        val1: Double,
        type: StreamDataType,
        timestampAndMillis: Pair<String, Long>
    ) {
        val paramName = when (type) {
            StreamDataType.SPO2 -> "SpO2"
            StreamDataType.ACCEL -> "ANGLE"
            else -> type.toString()
        }
        insertVitalsToDB(sensor, val1, paramName, timestampAndMillis)
    }

    @SuppressLint("SimpleDateFormat")
    fun insertVitalsToDB(sensor: Sensor, val1: Double, paramName: String, timestampAndMillis: Pair<String, Long>) {
        try {
            val sensorId = when (sensor.sensorType) {
                SensorType.CHEST -> CommonDataArea.currentChestSensorId
                SensorType.LIMB -> CommonDataArea.currentLimbSensorId
                else -> CommonDataArea.currentBpSensorId
            }
            val tick = timestampAndMillis.second
            val patID = CommonDataArea.PATIENT.patientId1
            val encryptedValue = encryptionVitalConverter.convertToDatabaseValue(val1)

            val reading = MeasurementData(
                0,
                getUuid(),
                CommonDataArea.PATIENT.patientId,
                CommonDataArea.PATIENT.visitId.toInt(),
                sensorId,
                patID,
                "DOUBLE",
                paramName,
                encryptedValue,
                null,
                1,
                timestampAndMillis.first,
                0,
                null,
                null,
                tick,
                0
            )
            val rowNum = measurementBox.put(reading)
            if (rowNum>0)CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.StreamUpload, Pair(reading, null))
            else Log.d("measurementBox ","insertVitalsToDB: $rowNum Failed")
        } catch (ex: TimeoutCancellationException) {
            Log.e("TimeoutException", "Coroutine timed out: ${ex.message}")
        }
    }

    @SuppressLint("SimpleDateFormat")
    fun insertByteStreamToDB2(
        sensor: Sensor,
        measurementData: ByteArray,
        paramName: String,
        timestampAndMillis: Pair<String, Long>,
    ) {
        try {
            val patID = CommonDataArea.PATIENT.patientId1
            val sensorId = when (sensor.sensorType) {
                SensorType.CHEST -> CommonDataArea.currentChestSensorId
                SensorType.LIMB -> CommonDataArea.currentLimbSensorId
                else -> null
            } ?: return
            val encryptedStream = encryptionConverter.convertToDatabaseValue(measurementData)
            val tick = timestampAndMillis.second
            val measurement = MeasurementData(
                measurementId = 0,
                measurementUuid = getUuid(),
                patientId = CommonDataArea.PATIENT.patientId,
                visitId = 0,
                sensorId = sensorId,
                valueType = "byteArray",
                paramName = paramName,
                measurementData = encryptedStream,
                numberOfSamples = measurementData.size,
                timestamp = timestampAndMillis.first,
                uploadStatus = 0,
                uploadTimestamp = null,
                retryCount = 0,
                tickValue = tick,
                patientID1 = patID,
                value = null,
                sln =0
            )
           val rowNum= measurementBox.put(measurement)
            if (rowNum>0)CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.StreamUpload, Pair(measurement, measurementData))
            else Log.d("measurementBox ","insertByteStreamToDB2: $rowNum Failed")

        } catch (ex: Exception) {
            Log.i("Exp", ex.message.toString())
        }
    }

    private fun getUuid(): String {
        val uuid = UUID.randomUUID()
        return uuid.toString()
    }
}
