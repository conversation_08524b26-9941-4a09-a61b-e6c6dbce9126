package com.spacelabs.app.database.daoHelper

import android.content.Context
import android.util.Log
import com.spacelabs.app.api.ApiResponseData
import com.spacelabs.app.database.entities.PatientTable
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.util.*

class PatientDaoHelper(context: Context): DbDaoHelperManager(context) {

    enum class PatientModes {
        Active,
        BedBound
    }

    private val alarmDaoHelper = AlarmDaoHelper(context)
    private suspend fun createTemporaryPatient(){
        val date = Calendar.getInstance().time
        val simpleDateFormatter = SimpleDateFormat("MM-dd-yyyy", Locale.getDefault())
        val currentDate = simpleDateFormatter.format(date)

        val pid1 = getRandomPatientId()
        val patient = PatientTable(null, "Temp", null, "Patient", pid1, null, "DEC - 6 - 2014", 8, "Unknown", null, null, null, currentDate, null, currentDate, "active", 1)
        patientDao.insertNewPatientAsActive(patient)
        alarmDaoHelper.fetchCurrentPatientAlarmSettings()
    }

    suspend fun getActivePatient(): List<PatientTable>? {
        return patientDao.getActivePatient()
    }

    fun setAllPatientsToInActive() {
        dbCoroutine.launch {
            patientDao.setAllActivePatientsToInactive()
            createTemporaryPatient()
        }
    }

    fun updatePatientInfo(patient: PatientTable) {
        dbCoroutine.launch {
            try {
                patientDao.updatePatientInfo(patient)
            } catch (ex: Exception) {
                Log.e("UpdatePatientInfo:", ex.stackTraceToString())
            }
        }
    }


    fun updateCurrentPatientWithAPIPatientInfo(apiResponseData: ApiResponseData){
        dbCoroutine.launch {
            var currentPatient: PatientTable? = null
            val patientList = patientDao.getActivePatient()
            if(patientList != null) {
                for (patient in patientList) {
                    currentPatient = patient
                }
            }
            if(currentPatient != null){
                val isPatientTemp = currentPatient.isTempPatient == 1
                if(isPatientTemp) {
                    val updatedPatient = getUpdatedPatient(apiResponseData, currentPatient)
                    patientDao.updatePatientInfo(updatedPatient)
                } else {
                    compareReceivedPatientInfoWithActivePatientAndUpdate(currentPatient, apiResponseData)
                }
            }
        }
    }

    private suspend fun compareReceivedPatientInfoWithActivePatientAndUpdate(currentPatient: PatientTable, apiResponseData: ApiResponseData){
        if(currentPatient.patientID1 == apiResponseData.patient.patientID1)
            return
        val patient = getUpdatedPatient(apiResponseData, null)
        patientDao.insertNewPatientAsActive(patient)
    }

    private fun getUpdatedPatient(apiResponseData: ApiResponseData, currentPatient: PatientTable?): PatientTable{
        val dobAndAge = getDobAndAge(apiResponseData.patient.birthDate)
        return if(currentPatient != null){
            PatientTable(
                currentPatient.patientId,
                apiResponseData.patient.firstName,
                currentPatient.middleName,
                apiResponseData.patient.family,
                apiResponseData.patient.patientID1,
                currentPatient.patientID2,
                dobAndAge.key,
                dobAndAge.value,
                apiResponseData.patient.gender,
                currentPatient.height,
                currentPatient.weight,
                currentPatient.bedName,
                currentPatient.admitDate,
                currentPatient.facilityName,
                currentPatient.createdOn,
                currentPatient.status,
                0
            )
        } else{
            PatientTable(
                null,
                apiResponseData.patient.firstName,
                null,
                apiResponseData.patient.family,
                apiResponseData.patient.patientID1,
                null,
                dobAndAge.key,
                dobAndAge.value,
                apiResponseData.patient.gender,
                null,
                null,
                null,
                null,
                null,
                null,
                "active",
                0
            )
        }
    }

    fun getDobAndAge(dob: String): Map.Entry<String, Int>{
        val splitDob = dob.split("-")
        val dateOfBirth = LocalDate.parse("${splitDob[0]}-${splitDob[1]}-${splitDob[2]}")
        val currentDate = LocalDate.now()
        val yrs = Period.between(dateOfBirth, currentDate).years
        val birthDate = "${getMonthName(splitDob[1].toInt())} - ${splitDob[2]} - ${splitDob[0]}"
        return AbstractMap.SimpleEntry(birthDate, yrs)
    }

    private fun getMonthName(mnth: Int): String {
        return when(mnth){
            1 -> "JAN"
            2 -> "FEB"
            3 -> "MAR"
            4 -> "APR"
            5 -> "MAY"
            6 -> "JUN"
            7 -> "JUL"
            8 -> "AUG"
            9 -> "SEP"
            10 -> "OCT"
            11 -> "NOV"
            12 -> "DEC"
            else -> ""
        }
    }

    private fun getRandomPatientId(): String{
        val random = (100000..999999).random()
        return random.toString()
    }

    fun fetchCurrentPatientDetailsIfExist() {
        try {
            dbCoroutine.launch {
                val activePatients = patientDao.getActivePatient()
                if (!activePatients.isNullOrEmpty()) {
                    val patient = activePatients.first()
                    patientDao.setUpPatientInfo(patient)
                } else {
                    createTemporaryPatient()
                }
            }
        } catch (ex: Exception) {
            Log.e("FetchCurrentPatientDetailsIfExist:", ex.stackTraceToString())
        }
    }
}