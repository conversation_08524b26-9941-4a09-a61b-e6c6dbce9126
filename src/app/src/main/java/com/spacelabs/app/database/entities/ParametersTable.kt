package com.spacelabs.app.database.entities

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import org.apache.commons.lang3.StringUtils

@Entity(tableName = "tblParameters")
data class ParametersTable(
    @PrimaryKey(autoGenerate = true)
    val paramId: Int?,
    val paramName: String,
    val paramCategory: String?,
    val paramType: Int?,
    val linkedSensorDef: String?
)