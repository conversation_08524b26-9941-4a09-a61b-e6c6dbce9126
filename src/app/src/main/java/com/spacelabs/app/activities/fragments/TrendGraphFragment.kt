package com.spacelabs.app.activities.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.FrameLayout
import android.widget.Spinner
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.TrendGraphFragmentHelper
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.interfaces.UiEventCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

class TrendGraphFragment(private val fragmentHolder: FrameLayout): Fragment(R.layout.trend_graph) {

    private lateinit var btnToLeft: AppCompatButton
    private lateinit var btnToRight: AppCompatButton
    private lateinit var btnNext: AppCompatButton
    private lateinit var btnPrevious: AppCompatButton

    private lateinit var trendTimer: Spinner
    private lateinit var closeBtn: Button

    private lateinit var trendGraphHelper: TrendGraphFragmentHelper

    private val viewModelJob = Dispatchers.Main + Job()
    private lateinit var uiScope: CoroutineScope

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        uiScope = CoroutineScope(viewModelJob)
        trendGraphHelper = TrendGraphFragmentHelper(context)
        trendTimer = view.findViewById(R.id.trend_duration)

        closeBtn = view.findViewById(R.id.trend_close_button)
        btnToLeft = view.findViewById(R.id.btn_backward)
        btnToRight = view.findViewById(R.id.btn_forward)
        btnNext = view.findViewById(R.id.btn_next)
        btnPrevious = view.findViewById(R.id.btn_previous)

        trendGraphHelper.initChartAndVitalUi(view)

        closeBtn.setOnClickListener {
            closeFragment()
        }

        trendTimer.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                onSpinnerItemSelected(parent, position)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                // Do nothing
            }
        }

        trendGraphHelper.onHighlighterChanged()

        btnToLeft.setOnClickListener {
            trendGraphHelper.onForwardBackwardButtonClick(true)
        }

        btnToRight.setOnClickListener {
            trendGraphHelper.onForwardBackwardButtonClick(false)
        }

        btnNext.setOnClickListener {
            trendGraphHelper.onNextPreviousButtonClick(false)
        }

        btnPrevious.setOnClickListener {
            trendGraphHelper.onNextPreviousButtonClick(true)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        view?.setOnTouchListener(fun(_: View, _: MotionEvent): Boolean {
            return true
        })

        return view
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)

        trendGraphHelper.setupCharts()
        val timeIntervals = context?.resources?.getStringArray(R.array.trendIntervals) ?: return
        val adapter: ArrayAdapter<String> =
            ArrayAdapter(context!!, R.layout.spinner_item_layout, timeIntervals)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        trendTimer.adapter = adapter
        val storedInterval = TrendGraphFragmentHelper.TrendInterval.toString()
        // Position in the array that matches the stored interval (ignoring the suffix h)
        val position = timeIntervals.indexOfFirst { it.startsWith(storedInterval) }
        if (position != -1) {
            trendTimer.setSelection(position)
        }
    }

    override fun onStart() {
        super.onStart()
        fragmentHolder.visibility = View.VISIBLE
        CommonEventHandler.registerUiEventCallback(TrendUiEventHandler(), this::class.java)
    }

    fun onSpinnerItemSelected(parent: AdapterView<*>?, position: Int) {
        val selectedHour: String = parent?.getItemAtPosition(position).toString().filter { it.isDigit() }
        if(selectedHour[0].isDigit()) {
            TrendGraphFragmentHelper.TrendInterval = selectedHour.toInt()
            initialDataPlotting()
        }
    }

    private fun initialDataPlotting() {
        val xAxisMax = TimestampUtils.getCurrentTimeMillis()
        trendGraphHelper.extractAndPlotDataFromObjectBox(xAxisMax)
    }

    private fun closeFragment(){
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
        uiScope.cancel()
        trendGraphHelper.onCloseFragment()
        parentFragmentManager.beginTransaction().remove(this).commit()
        fragmentHolder.visibility = View.GONE
    }

    private fun uiStateChangesOnLoading(isLoading: Boolean) {
        trendTimer.isEnabled = !isLoading
        btnPrevious.isEnabled = !isLoading
        btnNext.isEnabled = !isLoading

        val btnBg = if(isLoading)
            context?.resources?.getColorStateList(R.color.gray, context?.theme)
        else {
            trendGraphHelper.uiUpdateOnCompleteLoading()
            context?.resources?.getColorStateList(R.color.lightBlue, context?.theme)
        }

        btnPrevious.backgroundTintList = btnBg
        btnNext.backgroundTintList = btnBg
    }

    inner class TrendUiEventHandler: UiEventCallback {
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            uiScope.launch {
                when(event) {
                    UiEventCallback.UiEventType.TrendDataReceived -> trendGraphHelper.onTrendDataReceived(eventData as List<MeasurementData>)
                    UiEventCallback.UiEventType.TrendLoading -> uiStateChangesOnLoading(eventData as Boolean)
                    else -> return@launch
                }
            }
        }
    }
}