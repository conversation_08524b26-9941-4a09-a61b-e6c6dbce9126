package com.spacelabs.app.alarms

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.interfaces.IOEventCallback

class AlarmReceiver: BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        val eventAndData: Pair<IOEventCallback.IOEventType, Any?> = when (intent?.action) {
            ScheduledEvents.BpMeasurement.action-> Pair(IOEventCallback.IOEventType.StartAutoBp, null)
            ScheduledEvents.TurnDue.action -> Pair(IOEventCallback.IOEventType.TriggerTurnDue, true)
            else -> null
        } ?: return

        CommonEventHandler.postIOEvent(eventAndData.first, eventAndData.second)
    }

    enum class ScheduledEvents(val action: String, val requestCode: Int) {
        BpMeasurement("AutoBp", 10001),
        TurnDue("TurnDue", 10002),
        PeriodicDataClearance("PeriodicDbClearance", 10003)
    }
}