package com.spacelabs.app.iomt

import com.spacelabs.app.adt.PatientStatusResponseModel
import com.spacelabs.app.iomt.data.dataclasses.LoginRequest
import com.spacelabs.app.iomt.data.dataclasses.LoginResponse
import com.spacelabs.app.iomt.data.dataclasses.MappingResponse
import com.spacelabs.app.iomt.data.dataclasses.SensorAuthenticationResponse
import retrofit2.Call
import retrofit2.http.*
import retrofit2.Response

interface ApiService {

    @Headers("Content-Type: application/json")
    @GET("deviceId/{deviceId}/qrtoken/{token}/authenticatedevice")
    fun authenticateDevice(
        @Path("deviceId") deviceId: String,
        @Path("token") token: String
    ): Call<Boolean>

    @Headers("Content-Type: application/json")
    @POST("devicess/{deviceId}/tenant/{tenantId}/companies/{companyID}")
    fun mapDeviceToCompany(
        @Path("deviceId") deviceId: String,
        @Path("companyID") companyID: String,
        @Path("tenantId") tenantId: String,
        ): Call<MappingResponse>

    @Headers("Content-Type: application/json")
    @POST("sensor/{sensorName}/{sensorAddress}/checkSensorAuthentication")
    fun checkSensorAuthentication(
        @Path("sensorName") sensorName: String,
        @Path("sensorAddress") sensorAddress: String,
    ): Call<SensorAuthenticationResponse>
    @Headers("Content-Type: application/json")
    @GET("deviceid/{deviceId}/getthetoken")
    fun getthetoken(
        @Path("deviceId") deviceId: String,
    ): Call<MappingResponse>

    @Headers("Content-Type: application/json")
    @GET("device/{androidId}/getPatientAdmittedStatus")
    suspend fun getPatientAdmittedStatus(
        @Path("androidId") androidId: String,
    ): Response<PatientStatusResponseModel>
    @Headers("Content-Type: application/json")
    @GET("device/{deviceId}/isDeviceRegistered")
    fun isDeviceRegistered(@Path("deviceId") deviceId: String): Call<Boolean>

    @Headers("Content-Type: application/json")
    @POST("device/{devicemacId}/sensor/{sensorName}/{sensormacId}")
    fun mapSensorToDevice(
        @Path("devicemacId") deviceId: String,
        @Path("sensorName") sensorName: String,
        @Path("sensormacId") sensorMacId: String,
    ): Call<MappingResponse>

    @Headers("Content-Type: application/json")
    @PUT("device/{devicemacId}/sensor/{sensorName}/{sensormacId}")
    fun disconnectSensorFromDevice(
        @Path("devicemacId") deviceId: String,
        @Path("sensorName") sensorName: String,
        @Path("sensormacId") sensorMacId: String,
    ): Call<MappingResponse>

/*Secure update api*/
        @Headers("Content-Type: application/json")
        @POST("deviceuserlogin")
        fun login(@Body request: LoginRequest): Call<LoginResponse>

    @Headers("Content-Type: application/json")
    @POST("devicess/{deviceId}/companies/{companyID}")
    fun deviceToCompanyMapping(
        @Path("deviceId") deviceId: String,
        @Path("companyID") companyID: String,
    ): Call<MappingResponse>



}

