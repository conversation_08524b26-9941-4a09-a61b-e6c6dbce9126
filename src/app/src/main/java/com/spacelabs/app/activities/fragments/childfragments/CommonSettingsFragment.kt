package com.spacelabs.app.activities.fragments.childfragments

import android.content.Context
import android.content.SharedPreferences
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.text.Spannable
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.Spinner
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.google.android.material.slider.Slider
import com.spacelabs.app.BuildConfig
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.HospitalName
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.database.objectbox.helpers.DataReplicator
import com.spacelabs.app.iomt.IomtApiManager
import kotlin.math.roundToInt

class CommonSettingsFragment(val settingsHelper: ParamSettingsHelper): Fragment(R.layout.settings_dialog_settings) {

    private lateinit var swVersion: TextView
    private lateinit var androidId: TextView
    private lateinit var wifiSsid: TextView
    private lateinit var timerSpinner: Spinner
    private lateinit var configWfi: Button
    private lateinit var replicateDbBtn: AppCompatButton

    private lateinit var ipAddressEditText: EditText
    private lateinit var editIp: Button
    private lateinit var saveIpButton: Button
    private lateinit var ipAddress_layout: LinearLayout
    private lateinit var hospitalName_layout: LinearLayout

    private lateinit var volumeSlider: Slider
    private lateinit var criticalVolume: TextView
    private lateinit var highVolume: TextView
    private lateinit var mediumVolume: TextView
    private lateinit var lowVolume: TextView
    private lateinit var hospitalName:TextView

    private lateinit var wifiList: MutableList<String>
    var selectedValue: String? = null
    private lateinit var sharedPreferences: SharedPreferences
    private lateinit var settingDaoHelper: SettingDaoHelper

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        wifiList = mutableListOf()
        sharedPreferences = context?.getSharedPreferences("settings", Context.MODE_PRIVATE)!!
        settingDaoHelper = SettingDaoHelper(requireContext())

        swVersion = view.findViewById(R.id.swVersion)
        androidId = view.findViewById(R.id.androidId)
        wifiSsid = view.findViewById(R.id.wifi_ssid)
        configWfi = view.findViewById(R.id.config_wifi)
        replicateDbBtn = view.findViewById(R.id.dbReplicaBtn)
        timerSpinner = view.findViewById(R.id.delete_timer_spinner)
        ipAddress_layout = view.findViewById(R.id.ipAddress_layout)
        hospitalName_layout = view.findViewById(R.id.hospitalName_layout)
        ipAddressEditText = view.findViewById(R.id.ipAddressEditText)
        editIp = view.findViewById(R.id.editIpButton)
        saveIpButton = view.findViewById(R.id.saveIpButton)

        volumeSlider = view.findViewById(R.id.volume)
        criticalVolume = view.findViewById(R.id.criticalVolume)
        highVolume = view.findViewById(R.id.highVolume)
        mediumVolume = view.findViewById(R.id.mediumVolume)
        lowVolume = view.findViewById(R.id.lowVolume)
        hospitalName=view.findViewById(R.id.hospitalName)

        uiActionListeners()
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun onStart() {
        super.onStart()
        postUiInitActions()
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    private fun postUiInitActions() {
        context?.let { setText(swVersion, it.getString(R.string.softwareVersion)) }
        context?.let { setText(androidId, it.getString(R.string.androidId)) }
        bindWifiSSID()
        setupTimerSpinner(CommonDataArea.dataDeletionTimer)
        val volume = SettingDaoHelper.AppSettings.Volume.getValue().toFloat()
        volumeSlider.value = volume
        setupVolume(volume)
    }

    fun uiActionListeners() {
        timerSpinner.onItemSelectedListener = onTimerSpinnerClickAction()
        hospitalName.text = HospitalName
        if(!CommonDataArea.enableSnsApi) {
            ipAddress_layout.visibility = View.VISIBLE
            hospitalName_layout.visibility = View.VISIBLE
        } else {
            ipAddress_layout.visibility = View.INVISIBLE
            hospitalName_layout.visibility = View.INVISIBLE
        }
        // Set initial value and disable editing
        ipAddressEditText.setText(SettingDaoHelper.AppSettings.websocketUrl.getValue())
        ipAddressEditText.isEnabled = false


        configWfi.setOnClickListener {
            Toast.makeText(context, "Coming soon!", Toast.LENGTH_SHORT).show()
        }

        replicateDbBtn.setOnClickListener {
            val dataReplicator = DataReplicator()
            dataReplicator.processLastDayAndCreateData()
        }

        editIp.setOnClickListener {
            // Enable editing and show the Save button
            ipAddressEditText.isEnabled = true
            editIp.visibility = View.GONE
            saveIpButton.visibility = View.VISIBLE
        }

        saveIpButton.setOnClickListener {
            val newIp = ipAddressEditText.text.toString()
            settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.websocketUrl, newIp)
            reconnectToNewUrl(newIp)
        }

        volumeSlider.addOnChangeListener(Slider.OnChangeListener { slider, value, _ ->
            setupVolume(value)
            slider.value = value
        })
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }

    private fun bindWifiSSID() {
        val connectivityManager =
            context?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network)

        val ssid =
            if (networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                val wifiManager = context?.getSystemService(Context.WIFI_SERVICE) as WifiManager
                val wifiInfo = wifiManager.connectionInfo
                wifiInfo.ssid.replace("\"", "")
            } else {
                "No wifi connected"
            }

        "Connected to $ssid".also { wifiSsid.text = it }
    }

    private fun setupTimerSpinner(value: String) {
        val timeIntervals = context?.resources?.getStringArray(R.array.testTimeIntervals)!!
        val adapter: ArrayAdapter<String> =
            ArrayAdapter(requireContext(), R.layout.spinner_item_layout, timeIntervals)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        timerSpinner.adapter = adapter
        timerSpinner.setSelection(timeIntervals.lastIndexOf(value))
    }

    private fun onTimerSpinnerClickAction(): AdapterView.OnItemSelectedListener {
        val settingDaoHelper = SettingDaoHelper(requireContext())
        return object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                val selectedValue = parent?.getItemAtPosition(position).toString()
                if (selectedValue != CommonDataArea.dataDeletionTimer) {
                    settingDaoHelper.updateSettings(
                        SettingDaoHelper.AppSettings.PeriodicDataClearance,
                        selectedValue
                    )
                }
            }

            override fun onNothingSelected(p0: AdapterView<*>?) {
                selectedValue = null
            }
        }
    }

    private fun setText(textView: TextView, prefix: String) {
        val versionText = textView.text.toString()
        if (versionText.isEmpty())
            textView.text = Html.fromHtml(
                "$prefix${getPostText(textView)}",
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
    }

    private fun getPostText(textView: TextView): String {
        return when (textView) {
            swVersion -> BuildConfig.VERSION_NAME
            androidId -> IomtApiManager.DEVICE_UUID/*CommonDataArea.ANDROID_ID*/
            else -> ""
        }
    }

    private fun setupVolume(volume: Float) {
        val originalVolume = volume.roundToInt()
        /*criticalVolume.text = Html.fromHtml("$originalVolume%", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        highVolume.text = Html.fromHtml(calculateVolumePercentageBase50(volume, 80), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        mediumVolume.text = Html.fromHtml(calculateVolumePercentageBase50(volume, 60), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        lowVolume.text = Html.fromHtml(calculateVolumePercentageBase50(volume, 50), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.Volume, originalVolume.toString())*/

        criticalVolume.text = Html.fromHtml("$originalVolume%", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        highVolume.text = Html.fromHtml("$originalVolume%", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        mediumVolume.text = Html.fromHtml("$originalVolume%"/*calculateVolumePercentageBase50(volume, 90)*/, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        lowVolume.text = Html.fromHtml("$originalVolume%"/*calculateVolumePercentageBase50(volume, 80)*/, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.Volume, originalVolume.toString())
    }

    private fun calculateVolumePercentageBase50(volume: Float, percentage: Int): String {
        val calculatedVolume = ((volume/100) * percentage).roundToInt()
        return if(calculatedVolume < 80)
            "80%"
        else
            "$calculatedVolume%"
    }

    private fun reconnectToNewUrl(newIp:Any){
        // Save the new IP address value to CommonDataArea.ipAddress


        // Disable editing and hide the Save button
        ipAddressEditText.isEnabled = false
        editIp.visibility = View.VISIBLE
        saveIpButton.visibility = View.GONE
        CommonDataArea.manualReconnect=true
        if(CommonDataArea.iscmsClientIntialized) {

            CommonDataArea.cmsClient.closeWebSocketConnection()
           // Log.d("ipAddress", "New ipAddress: ${SettingDaoHelper.AppSettings.websocketUrl.getValue()}")
            CommonDataArea.cmsClient.reconnect(SettingDaoHelper.AppSettings.websocketUrl.getValue())
            //Log.d("ipAddress", "New ipAddress: ${SettingDaoHelper.AppSettings.websocketUrl.getValue()}")
        }
        showToast("reconnecting to $newIp")
      //  Log.d("ipAddress", "New ipAddress: $newIp")
        //Log.d("ipAddress", "New ipAddress: ${SettingDaoHelper.AppSettings.websocketUrl.getValue()}")
    }
}