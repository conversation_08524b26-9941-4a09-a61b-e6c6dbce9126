package com.spacelabs.app.ui

import android.app.Dialog
import android.os.Build
import android.text.*
import androidx.annotation.RequiresApi
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.MainActivity
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.ManageSensorsFragment
import com.spacelabs.app.common.CommonDataArea.Companion.isSensorRemoved
import com.spacelabs.app.ui.dialogs.*

class Dialogs(activityContext: MainActivity) {

    private val mainActivityContext: MainActivity
    private val dialogHelper: DialogActionHelper

    private var patchTransferDialog: Dialog? = null

    enum class Popups {
        SensorConnectionUpdate,
        Confirmation,
        Alert,
    }

    init {
        mainActivityContext = activityContext
        dialogHelper = DialogActionHelper(mainActivityContext)
    }

    companion object {
        val ACTIVE_DIALOGS = mutableListOf<Dialog>()
        var ManageSensorDialog: Dialog? = null

        var connectionAlertDialog: Dialog? = null
    }

    class EmojiExcludeFilter: InputFilter{
        override fun filter(
            source: CharSequence?,
            start: Int,
            end: Int,
            dest: Spanned?,
            dstart: Int,
            dend: Int,
        ): CharSequence? {
            for (i in start until end) {
                val type: Int = Character.getType(source?.get(i)!!)
                if (type == Character.SURROGATE.toInt() || type == Character.OTHER_SYMBOL.toInt()) {
                    return ""
                }
            }
            return null
        }
    }

    fun getDataBaseSettingsView(){
        val databaseDialogController = DatabaseDialogController(mainActivityContext)
        DialogManager.SimpleDialog = databaseDialogController.createDialog(DialogManager.SimpleDialog, R.layout.dialog_database_list)
    }

    //end of new

    @RequiresApi(Build.VERSION_CODES.S)
    fun getSensorListDialog(){
        if(mainActivityContext.appManager.hasBluetoothConnectPermission(mainActivityContext)) {
            val sensorListDialogManager = SensorListDialogManager(mainActivityContext)
            ManageSensorDialog = sensorListDialogManager.createDialog(ManageSensorDialog, R.layout.dialog_sensor_list)
            DialogManager.SimpleDialog = ManageSensorDialog
        } else {
            mainActivityContext.appManager.askPermissions(null)
        }
    }

    fun getPatientInformation(){
        val patientInformationDialogManager = PatientInformationDialogManager(mainActivityContext)
        DialogManager.SimpleDialog = patientInformationDialogManager.createDialog(DialogManager.SimpleDialog, R.layout.dialog_patient_information_sns)
    }

    fun getCommonAlarmDialog(alert: CommonAlarmsDialogController.Alerts){
        val commonSettingsDialogController = CommonAlarmsDialogController(mainActivityContext, alert)
        commonSettingsDialogController.createWrapContentDialogs(R.layout.common_alarm_dialog)
    }

    fun getPatchConnectionDialog() {
        if(ManageSensorsFragment.IsActive)
            return

        if(connectionAlertDialog == null && !isSensorRemoved) {
            val patchConnectionDialogManager = PatchConnectionDialogManager(mainActivityContext)
            connectionAlertDialog = patchConnectionDialogManager.createWrapContentDialogs(R.layout.patch_connection_dialog)
        }
    }

    fun getSensorReplaceDialog(sensor: Sensor, currentSensorName: String){
        val sensorReplaceDialogController = SensorReplaceDialogController(mainActivityContext, sensor, currentSensorName)
        patchTransferDialog = sensorReplaceDialogController.createWrapContentDialogs(R.layout.dialog_replace_sensors)
    }

    fun closeAllDialogs(){
        for (dialog in ACTIVE_DIALOGS) {
            if (dialog.isShowing) {
                dialog.dismiss()
            }
        }
        ACTIVE_DIALOGS.clear()
    }
}