package com.spacelabs.app.common

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat

class ApplicationManager(activity: AppCompatActivity) {

    private val activity: AppCompatActivity
    private val context: Context

    init {
        this.activity = activity
        this.context = activity
    }

    private lateinit var permissionToRequest: MutableList<String>

    @RequiresApi(Build.VERSION_CODES.S)
    private val permissionsToAsk = listOf(
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.MANAGE_EXTERNAL_STORAGE,
        Manifest.permission.BLUETOOTH,
        Manifest.permission.BLUETOOTH_ADMIN,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.BLUETOOTH_SCAN,
        Manifest.permission.BLUETOOTH_CONNECT,
        Manifest.permission.BLUETOOTH_PRIVILEGED,
        Manifest.permission.ACCESS_WIFI_STATE,
        Manifest.permission.CHANGE_WIFI_STATE,
        Manifest.permission.SCHEDULE_EXACT_ALARM,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.INTERNET,
        Manifest.permission.ACCESS_NETWORK_STATE,
        Manifest.permission.NFC,
        Manifest.permission.CAMERA
    )

    @RequiresApi(Build.VERSION_CODES.S)
    fun hasBluetoothConnectPermission(context: Context) = ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED

    fun requestPermissions(){
        permissionToRequest = mutableListOf()
        if(Build.VERSION.SDK_INT < Build.VERSION_CODES.S)
            return

        for(permission in permissionsToAsk){
            val hasPermission = ActivityCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
            if(!hasPermission)
                permissionToRequest.add(permission)
        }

        if(permissionToRequest.isNotEmpty())
            askPermissions(null)

        /*if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && activity::class.java == MainActivity::class.java)
            manageAllFilesPermissionActivityForResult()*/
    }

    fun askPermissions(permission: String?){
        if(permission == null) {
            if(permissionToRequest.isNotEmpty())
                activity.let { ActivityCompat.requestPermissions(it, permissionToRequest.toTypedArray(), 0) }
        }else {
            requestPermissionSpecified(permission)
        }
    }

    private fun requestPermissionSpecified(permission: String){
        if(permissionToRequest.isNotEmpty()){
            for(item in permissionToRequest){
                if(item == permission){
                    val permissionRequesting = arrayOf(permission)
                    activity.let { ActivityCompat.requestPermissions(it, permissionRequesting, 0) }
                    break
                }
            }
        }
    }
    fun requestPermissionsSequentially(permissions: Array<String>, requestCode: Int) {
        val pendingPermissions = permissions.filter {
            ActivityCompat.checkSelfPermission(activity, it) != PackageManager.PERMISSION_GRANTED
        }

        if (pendingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(activity, pendingPermissions.toTypedArray(), requestCode)
        }
    }

}