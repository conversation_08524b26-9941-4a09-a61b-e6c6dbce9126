package com.spacelabs.app.ui.activityUIHelpers

import android.widget.TextView
import com.github.mikephil.charting.charts.LineChart
import com.spacelabs.app.R
import com.spacelabs.app.MainActivity
import java.util.ArrayList

class MainActivityCenterConsole(mainActivity: MainActivity): ActivityUiManager(mainActivity) {

    private lateinit var ecgScale: TextView

    private lateinit var ecgAlarmText: TextView
    private lateinit var ecgAlarmPrefix: TextView
    private lateinit var ecgAlarmSilenceText: TextView
    private lateinit var respAlarmText: TextView
    private lateinit var respAlarmPrefix: TextView
    private lateinit var spo2AlarmText: TextView
    private lateinit var spo2AlarmPrefix: TextView

    private lateinit var ecgConnectionAlert: TextView

    lateinit var ecgChart: LineChart
    lateinit var respChart: LineChart
    lateinit var ppgIr: LineChart

    private var chart = ArrayList<LineChart>(3)

    private val mainActivity: MainActivity
    init {
        this.mainActivity = mainActivity
    }

    override fun beforeUiInit() {
        TODO("Not yet implemented")
    }

    override fun initUi() {
        ecgChart = mainActivity.findViewById(R.id.ecgChart)

        ecgAlarmText = mainActivity.findViewById(R.id.ecgAlarmText)
        ecgAlarmPrefix = mainActivity.findViewById(R.id.ecgAlarmPrefix)
        ecgConnectionAlert = mainActivity.findViewById(R.id.ecgConnectionAlerts)
        ecgAlarmSilenceText = mainActivity.findViewById(R.id.ecgAlarmSilenceText)
    }

    override fun postUiInitLooperAction() {
        TODO("Not yet implemented")
    }

    override fun postUiInitAction() {
        ecgChart.setNoDataText("No Chest Sensor Connected")
        ecgChart.setNoDataTextColor(mainActivity.getColor(R.color.lightGreen))
        ecgChart.setScaleEnabled(false)

        chart.add(ecgChart)
    }

    override fun uiActionListeners() {
        TODO("Not yet implemented")
    }
}