package com.spacelabs.app.activities.iomtActivities

import android.os.Build
import android.os.Bundle
import android.transition.Slide
import android.view.Window
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.spacelabs.app.R
import com.spacelabs.app.activities.iomtActivities.activityUi.AccountConfigActivityUi
import com.spacelabs.app.activities.iomtActivities.helpers.AccountConfigActivityHelper
import com.spacelabs.app.common.CommonEventHandler

class AccountConfigActivity : AppCompatActivity() {

    private val accountActivityUi = AccountConfigActivityUi(this)
    val accountConfigHelper = AccountConfigActivityHelper(this)
    @RequiresApi(Build.VERSION_CODES.R)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setWindowProperties()
        setContentView(R.layout.account_config_activity_iomt)

        accountActivityUi.setUpActivityUi()
    }

    override fun onStart() {
        super.onStart()
        CommonEventHandler.registerUiEventCallback(
            accountActivityUi.UiEventHandler(),
            this::class.java
        )
    }

    override fun onResume() {
        super.onResume()
        accountConfigHelper.navigateIfAccountIdExists()
    }

    override fun onStop() {
        super.onStop()
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun setWindowProperties() {
        with(window) {
            requestFeature(Window.FEATURE_ACTIVITY_TRANSITIONS)

            enterTransition = Slide()
            exitTransition = android.transition.Fade()
        }

        window.decorView.windowInsetsController!!.hide(
            android.view.WindowInsets.Type.statusBars()
        )
    }
}