package com.spacelabs.app.database.dao

import androidx.room.*
import com.spacelabs.app.database.entities.*

@Dao
interface SettingDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertData(entity: SettingsTable)

    @Query("SELECT * FROM tblSettings ORDER BY id")
    suspend fun getAllSettingsOrderByDesc(): List<SettingsTable>

    @Query("SELECT * FROM tblSettings WHERE settingsName = :key")
    suspend fun getByKey(key: String): SettingsTable?

    @Update
    suspend fun updateData(entity: SettingsTable)
    @Query("SELECT COUNT(*) FROM tblSettings WHERE `settingsName` = 'modes'")
    suspend fun getModesCount(): Int






}