package com.spacelabs.app.iomt

import android.annotation.SuppressLint
import android.system.ErrnoException
import android.util.Log
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.spacelabs.app.api.ApiPostObservations
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.curActivity
import com.spacelabs.app.common.CommonDataArea.Companion.hasInternetConnection
import com.spacelabs.app.common.CommonDataArea.Companion.manualReconnect
import com.spacelabs.app.common.CommonDataArea.Companion.isServerOk
import com.spacelabs.app.common.CommonDataArea.Companion.iscmsClientIntialized
import com.spacelabs.app.common.CommonDataArea.Companion.url
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.iomt.data.CmsDataSender
import com.spacelabs.app.iomt.data.CmsLiveDataTransactionManager
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import org.java_websocket.client.WebSocketClient
import org.java_websocket.exceptions.WebsocketNotConnectedException
import org.java_websocket.handshake.ServerHandshake
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.net.ConnectException
import java.net.URI
import java.net.URISyntaxException

class CmsClient {
    enum class ParamType {
        STREAM, VALUE
    }

    private val connectionRetryIntervalMillis = 60 * 1000L // 1 minute

    private val TAG = "CMSClient"
    private val cmsDataSender: CmsDataSender = CmsDataSender(mWebSocketClient)
    private val encryptionVitalConverter = EncryptionVitalConverter()


    companion object {
        var mWebSocketClient: WebSocketClient? = null

        const val CMS_PARAMID_ECG = "20001"
        const val CMS_PARAMID_HR = "20002"
        const val CMS_PARAMID_RESPWAV = "20003"
        const val CMS_PARAMID_RESPRATE = "20004"
        const val CMS_PARAMID_PPG = "20005"
        const val CMS_PARAMID_SPO2 = "20006"
        const val CMS_PARAMID_TEMP_BODY = "20007"
        const val CMS_PARAMID_TEMP_SKIN = "20008"
        const val CMS_PARAMID_STEP_COUNT = "20009"
        const val CMS_PARAMID_FALL_COUNT = "20010"
        const val CMS_PARAMID_ACCEL = "20011"
        const val CMS_PARAMID_BP = "20012"
        const val CMS_PARAMID_BODY_POSITION = "20013"
        const val CMS_PARAMID_ANGLE = "20014"
    }

    //public static String url = "ws://***********:4567/source";//ws://localhost:4567/source";
    fun Init(url: String?) {
        try {
            val uri: URI
            uri = try {
                URI(url)
            } catch (e: URISyntaxException) {
                e.printStackTrace()
                Log.e(TAG, "Error initializing URI: ${e.message}")

                return
            } catch (e: IOException) {
                Log.e(TAG, "An unexpected error occurred during connection: ${e.message}")
                return
            } catch (e: ErrnoException) {
                Log.e(TAG, "An unexpected error occurred during connection: ${e.message}")
                return
            }
//        LogWriter.writeLogWbSock("wbSock", "Connecting..")
            mWebSocketClient = object : WebSocketClient(uri) {
                override fun onOpen(handshakedata: ServerHandshake) {
//                LogWriter.writeLogWbSock("wbSock", "Connected to web scok server")
                    onCMSConnect()
                }

                @SuppressLint("SuspiciousIndentation")
                override fun onMessage(message: String) {
                    try {
                        if (!message.contains("{\"Status\":[\"OK\"]}", ignoreCase = true)) {
                            Log.d(TAG, "Received observation response: $message")
                        }
                        if (message.contains("{\"Status\":[\"OK\"]}", ignoreCase = true)) {
                            isServerOk = true
                            //Log.d(TAG, "isServerOk: $isServerOk")
                        }
                        if (message.contains("{\"Status\":[\"FAIL\"]}", ignoreCase = true)) {
                            Log.d(TAG, "Received observation response: $message")
                            closeWebSocketConnection()
                        }

                    } catch (e: JSONException) {
                        throw RuntimeException(e)
                    }
                }

                override fun onClose(code: Int, reason: String, remote: Boolean) {
                    Log.d(TAG, "onClose: $code, $reason, $remote")
                    isServerOk = false

                    if (!manualReconnect && !CommonDataArea.cmsClient.isCmsConnected() && hasInternetConnection) {
                        Log.d(TAG, "onClose:$reason Reason: Server restarted $code, $remote")
                        retryConnectionAfterDelay()
                    }
                    CmsLiveDataTransactionManager.historyJob?.cancel()
                    //ON CLOSE Change websocket icon to Not available
                    CommonEventHandler.postUiEvent(
                        UiEventCallback.UiEventType.ServerConnectionStatusChanged,
                        false
                    )
                    Log.d(TAG, "historyJob : ${CmsLiveDataTransactionManager.historyJob?.isActive}")
                }

                override fun onError(ex: Exception) {
                    if (ex is ConnectException) {
                        // Handle ConnectException (ENETUNREACH) here
                        Log.e(TAG, "ConnectException: Network is unreachable", ex)
                        // Optionally, you can perform specific actions or retry the connection.
                        // closeWebSocketConnection()
                    } else if (ex is IOException) {
                        Log.e(TAG, "An unexpected error occurred during connection: ${ex.message}")

                    } /*{
                        // Handle WebSocket-related exceptions here
                        Log.e(TAG, "WebSocketException: ${ex.message}", ex)
                        // Optionally, you can perform specific actions or retry the connection.
                        closeWebSocketConnection()*/
                    else {
                        // Handle other exceptions if needed
                        Log.e(TAG, "onError: ${ex.stackTraceToString()}")
                        //  closeWebSocketConnection()
                    }
                }


            }
        } catch (e: URISyntaxException) {
            e.printStackTrace()
            Log.e(TAG, "Error initializing URI: ${e.message}")
        } catch (e: IOException) {
            Log.e(TAG, "An unexpected error occurred during connection: ${e.message}")

        }
    }

    private fun Connect() {
        try {
            if (mWebSocketClient == null || !mWebSocketClient!!.isOpen) {
                mWebSocketClient?.connect()
            } else {
                Log.d(TAG, "Connect: ${isCmsConnected()}")
                // reconnect(ipAddress)
            }
        } catch (e: ConnectException) {
            // Handle ConnectException (Network is unreachable) here
            Log.e(TAG, "Error connecting to the server: ")
            // Add your error-handling logic or show a message to the user
        } catch (e: Exception) {
            // Handle other exceptions here
            Log.e(TAG, "An unexpected error occurred during connection:")
        } catch (e: WebsocketNotConnectedException) {
            Log.e(TAG, "WebSocket not connected.", e)
        } catch (e: IOException) {
            Log.e(TAG, "An unexpected error occurred during connection: ")

        }
    }


    fun isCmsConnected(): Boolean {
        return mWebSocketClient != null && mWebSocketClient!!.isOpen
    }

    fun SendDeviceInfo(deviceUUID: String?,token: String) {
        val meseg = JSONObject()

        try {

            meseg.accumulate("PacketID", "WS_DEVICE_INFO")
            meseg.accumulate("DeviceUUID", deviceUUID)
            meseg.accumulate("Token", token)

            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
                mWebSocketClient!!.send(meseg.toString())
                Log.d(TAG, "SendDeviceInfo : ${meseg}")

            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun SendParamInfo(
        paramUUID: String?,
        paramName: String?,
        samplingRate: Int,
        paramType: ParamType,
    ) {
        val message = JSONObject()
        try {
            var paramTypeStr = "Stream"
            if (paramType == ParamType.VALUE) {
                paramTypeStr = "Value"
            }
            message.accumulate("PacketID", "WS_PARAM_INFO")
            message.accumulate("ParamUUID", paramUUID)
            message.accumulate("ParamName", paramName)
            message.accumulate("SamplingRate", samplingRate)
            message.accumulate("ParamType", paramTypeStr)
            message.accumulate("DataType", "float")

            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
                mWebSocketClient!!.send(message.toString())
                Log.d(TAG, "SendParamInfo : ${message}")

            } else {
                Log.d(TAG, "WebSocket connection is not open.")
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun sendPacket(array: FloatArray, measurement: MeasurementData) {
        try {
            when (measurement.paramName) {
                "ECG" -> StreamDataType.ECG
                "PPG" -> StreamDataType.PPG_IR
                "RESP" -> StreamDataType.RR
                else -> null
            } ?: return
            val cmsId = getCmsIdByParamName(measurement.paramName)
            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
                cmsDataSender.sendPacket(array, cmsId, measurement)
            }/* else {
                //Log.d(TAG, "sendPacket: WesbSocket connection ${mWebSocketClient?.isOpen}")
            }*/
        } catch (ex: Exception) {
            Log.e(TAG, ex.stackTraceToString())
        }
    }
  fun sendFhirPacket(array: DoubleArray, measurement: MeasurementData) {
        try {
            val observationData = when(measurement.paramName){
                "ECG"  -> ApiPostObservations.ecgObservation
                "PPG"-> ApiPostObservations.ppgIrObservation
                "RESP"-> ApiPostObservations.respObservation
                else -> null
            }
            if(observationData != null) {
                observationData.uuId = measurement.measurementUuid
                cmsDataSender.sendWaveformObservation(observationData, array, measurement.timestamp,measurement)
            } else{
                Log.d(TAG, "sendFhirPacket: observationData is Null")
            }
        } catch (ex: Exception) {
            Log.e(TAG, ex.stackTraceToString())
        }
    }

    fun sendValue(measurement: MeasurementData) {
        try {
            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
                val value =
                    encryptionVitalConverter.convertToEntityProperty(measurement.value)?.toFloat()
                        ?: return
                val cmsId = getCmsIdByParamName(measurement.paramName)
                cmsDataSender.sendValue(value, cmsId, measurement)
            } else {
                // Log.d(TAG, "sendValue: WesbSocket connection ${mWebSocketClient?.isOpen}")
            }
        } catch (ex: Exception) {
            Log.e(TAG, ex.stackTraceToString())
        }
    }

    fun sendVitalObservation(measurementData: MeasurementData, parameter: Triple<String, String, String>, uuId: String){
        try {
            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
              /*  val value =
                    encryptionVitalConverter.convertToEntityProperty(measurementData.value)?.toFloat()
                        ?: return*/
                val cmsId = getCmsIdByParamName(measurementData.paramName)
             //   cmsDataSender.sendValue(value, cmsId, measurementData)
                cmsDataSender.sendVitalObservation(measurementData, parameter, uuId)

            } else {
                // Log.d(TAG, "sendValue: WesbSocket connection ${mWebSocketClient?.isOpen}")
            }
        } catch (ex: Exception) {
            Log.e(TAG, ex.stackTraceToString())
        }
    }

    fun sendBpValue(measurement: MeasurementData, bpSysAndDia: String) {
        try {
            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
                val cmsId = getCmsIdByParamName(measurement.paramName)
                cmsDataSender.sendBpValue(bpSysAndDia, cmsId, measurement)
            } /*else {
                // Log.d(TAG, "sendValue: WesbSocket connection ${mWebSocketClient?.isOpen}")
            }*/
        } catch (ex: Exception) {
            Log.e(TAG, ex.stackTraceToString())
        }
    }

    fun sendNIBPObservation(
        bpSysAndDia: String,
        timestamp: String,
        uuId: String,
        measurement: MeasurementData
    ) {
        try {
            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
                cmsDataSender.sendNIBPObservation(bpSysAndDia, timestamp,uuId, measurement)
            } /*else {
                // Log.d(TAG, "sendValue: WesbSocket connection ${mWebSocketClient?.isOpen}")
            }*/
        } catch (ex: Exception) {
            Log.e(TAG, ex.stackTraceToString())
        }
    }


    /***
     * Special code to connect to iOrbit CMS
     */
    fun cmsConnect(): CmsClient {
        try {
            // LogWriter.writeLogWbSock("wbSock", "CMS Connecting..")
            Log.d(TAG, "CMS Connecting...")
            val client = CmsClient()
            val webSocketUrl=SettingDaoHelper.AppSettings.websocketUrl.getValue()
           // val webSocketUrl="wss://stream.iorbit.health:8082/"

            val url = "${webSocketUrl}source"
            //val url ="wss://192.168.56.1:4567/source"

            client.Init(url)
            client.Connect()
            Log.d(TAG, "WebSocket Url : $url")

            CommonDataArea.cmsClient = client
            iscmsClientIntialized=true
            return client
        } catch (e: ConnectException) {
            // Handle ConnectException (Network is unreachable) here
            Log.e(TAG, "Error connecting to the CMS server: ")
            // Add your error-handling logic or show a message to the user
            throw e // Re-throw the exception if needed
        } catch (e: Exception) {
            // Handle other exceptions here
            Log.e(TAG, "An unexpected error occurred during CMS connection: ")
            // Add your error-handling logic or show a message to the user
            throw e // Re-throw the exception if needed
        }
    }

    fun onCMSConnect() {
//        LogWriter.writeLogWbSock("wbSock", "CMS Connection setup")
        if (CommonDataArea.cmsClient.isCmsConnected()) {
            //to collect Random UUID of the devce and use it as the device ID
            CommonDataArea.cmsClient.SendDeviceInfo(IomtApiManager.DEVICE_UUID,IomtApiManager.DEVICE_API_TOKEN)
            CommonDataArea.cmsClient.SendFhirDeviceInfo(SettingDaoHelper.AppSettings.DeviceUuid.getValue(),IomtApiManager.DEVICE_API_TOKEN)
            /*CommonDataArea.cmsClient.SendParamInfo("20001", "ECG", 256, ParamType.STREAM)
            CommonDataArea.cmsClient.SendParamInfo("20002", "HR", 1, ParamType.VALUE)
            CommonDataArea.cmsClient.SendParamInfo("20003", "RespWave", 410, ParamType.STREAM)
            CommonDataArea.cmsClient.SendParamInfo("20004", "RespRate", 1, ParamType.VALUE)
            CommonDataArea.cmsClient.SendParamInfo("20005", "PPGWave", 64, ParamType.STREAM)
            CommonDataArea.cmsClient.SendParamInfo("20006", "SPO2", 1, ParamType.VALUE)*/
            CommonEventHandler.postUiEvent(
                UiEventCallback.UiEventType.ServerConnectionStatusChanged,
                true
            )
            CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.BackgroundUpload, null)
        }
    }

    fun reconnect(newIpAddress: String/*, newPort: String*/) {
        try {
           /* authenticationHandler.getAccessToken(ANDROID_ID) { accessToken ->
                if (accessToken != null) {*/
                    // Update the IP address
//                    ipAddress = newIpAddress
                    /*port = newPort*/
                    val newUrl = "${newIpAddress}source"
                    url = newUrl
                    manualReconnect = false
                    Init(url)
                    Connect()
            Log.d(TAG, "reconnecting $newUrl ")
               /* }
            }*/
        } catch (e: ConnectException) {
            when (e.message) {
                "Network is unreachable" -> {
                    // Handle the specific case of "Network is unreachable"
                    Log.e(TAG, "Network is unreachable: ${e.message}")
                    // Add your error-handling logic or show a message to the user
                }

                else -> {
                    // Handle other ConnectException cases
                    Log.e(TAG, "Error connecting to the server: ${e.message}")
                    // Add your general error-handling logic or show a message to the user
                }
            }
        } catch (e: Exception) {
            // Handle other exceptions here
            Log.e(TAG, "An unexpected error occurred: ${e.message}")
        } catch (e: WebsocketNotConnectedException) {
            Log.e(TAG, "WebSocket not connected.", e)
        }
    }


    fun closeWebSocketConnection() {
        if (mWebSocketClient != null) {
            mWebSocketClient!!.close()
            mWebSocketClient = null
        }
    }

    fun retryConnectionAfterDelay() {
        if (mWebSocketClient == null
            || mWebSocketClient?.readyState.toString()=="NOT_YET_CONNECTED"
            ||mWebSocketClient?.readyState.toString()=="CLOSING"
            ||mWebSocketClient?.readyState.toString()=="CLOSED") {
            Log.d(TAG, "retryConnectionAfterDelay: State ${mWebSocketClient?.readyState}")
            curActivity?.ioScope?.launch {
                delay(connectionRetryIntervalMillis)
                reconnect(SettingDaoHelper.AppSettings.websocketUrl.getValue())
            }
        } else {
            Log.d(TAG, "Websocket Status :${mWebSocketClient!!.isOpen} ")
            if ( mWebSocketClient?.isClosed == false){
            closeWebSocketConnection()
            }
        }
    }

    private fun getCmsIdByParamName(paramName: String): String {
        return when (paramName.uppercase()) {
            StreamDataType.ECG.name -> CMS_PARAMID_ECG
            StreamDataType.HR.name -> CMS_PARAMID_HR
            "RESP" -> CMS_PARAMID_RESPWAV
            StreamDataType.RR.name -> CMS_PARAMID_RESPRATE
            "PPG" -> CMS_PARAMID_PPG
            StreamDataType.SPO2.name -> CMS_PARAMID_SPO2
            StreamDataType.TEMP_BODY.name -> CMS_PARAMID_TEMP_BODY
            StreamDataType.TEMP_SKIN.name -> CMS_PARAMID_TEMP_SKIN
            "STEP_COUNT" -> CMS_PARAMID_STEP_COUNT
            "FALL_COUNT" -> CMS_PARAMID_FALL_COUNT
            StreamDataType.ACCEL.name -> CMS_PARAMID_ACCEL
            StreamDataType.BP.name -> CMS_PARAMID_BP
            "BODY_POSITION" -> CMS_PARAMID_BODY_POSITION
            "ANGLE" -> CMS_PARAMID_ANGLE
            else -> ""
        }
    }

    fun SendFhirDeviceInfo(deviceUUID: String?, token: String) {
        val deviceInfoJson = createDeviceInfoFHIR(deviceUUID ?: "")

        try {
            val meseg = JSONObject(deviceInfoJson)
            if (mWebSocketClient != null && mWebSocketClient!!.isOpen) {
                mWebSocketClient!!.send(meseg.toString())
                Log.d(TAG, "SendDeviceInfo : $meseg")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    fun createDeviceInfoFHIR(deviceUUID: String,): String {
        val deviceProfileID = "ECG"
        val deviceID = CommonDataArea.ANDROID_ID
        val deviceInfo = """
        {
            "resourceType": "Device",
            "id": $deviceUUID,
            "status": "active",
            "manufacturer": "ECG Manufacturer Inc.",
            "serialNumber": $deviceID,
            "deviceName": [
                {
                    "name": "12 Lead ECG",
                    "type": "model-name"
                }
            ],
            "modelNumber": $deviceProfileID,
            "type": {
                "coding": [
                    {
                        "system": "http://snomed.info/sct",
                        "code": "86184003",
                        "display": "Electrocardiographic monitor and recorder"
                    }
                ],
                "text": "ECG Device"
            },
            "version": [
                {
                    "value": "1.0"
                }
            ],
            "patient": {
                "reference": "${CommonDataArea.PATIENT.patientId1}",
                "display": "${CommonDataArea.PATIENT.firstName} ${CommonDataArea.PATIENT.lastName}" 
            }
        }
    """.trimIndent()

        return deviceInfo
    }


}