package com.spacelabs.app.database.daoHelper

import android.content.Context
import android.util.Log
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.entities.DefaultAlarmSettingsTable
import com.spacelabs.app.database.entities.PatientAlarmTable
import kotlinx.coroutines.launch

class AlarmDaoHelper(context: Context): DbDaoHelperManager(context) {

    private var activePatientId = 0

    fun fetchCurrentPatientAlarmSettings(){
        dbCoroutine.launch {
            val currentPatientAndAlarm = alarmDao.getActivePatientAlarmSettings()
            if(currentPatientAndAlarm != null){
                activePatientId = currentPatientAndAlarm.patient.patientId!!
                updateAlarmSettingsValues(currentPatientAndAlarm.patientAlarms)
            }
        }
    }

    private suspend fun updateAlarmSettingsValues(patientAlarms: List<PatientAlarmTable>?){
        val patientAlarmParams: MutableList<String> = mutableListOf()
        if(patientAlarms!!.isNotEmpty()){
            for(alarm in patientAlarms){
                val paramName = parametersDao.getParameterNameById(alarm.paramId)
                updateSettingsByCheckingAlarm(paramName, null, alarm)
                patientAlarmParams.add(paramName)
            }
        }
        val defaultAlarms = alarmDao.getDefaultAlarm()
        if(defaultAlarms != null){
            for (alarm in defaultAlarms){
                val paramName = parametersDao.getParameterNameById(alarm.paramId)
                if(!patientAlarmParams.contains(paramName))
                    updateSettingsByCheckingAlarm(paramName, alarm, null)
            }
        }
    }

    private fun updateSettingsByCheckingAlarm(paramName: String, defaultAlarm: DefaultAlarmSettingsTable?, patientAlarm: PatientAlarmTable?){
        val alarmParamDao = when(paramName){
            "HR" -> CommonDataArea.hrAlarmDao
            "RR" -> CommonDataArea.respAlarmDao
            "SpO2" -> CommonDataArea.spo2AlarmDao
            "TEMP_SKIN" -> CommonDataArea.tempAlarmDao
            "BP_SYS" -> CommonDataArea.bpSysAlarmDao
            "BP_DIA" -> CommonDataArea.bpDiaAlarmDao
            else -> null
        }?: return

        updateAlarmSettingsParameter(alarmParamDao, defaultAlarm, patientAlarm)
    }

    fun updatePatientAlarmToDb(alarmParamDao: AlarmParametersDao){
        insertOrUpdatePatientAlarm(alarmParamDao)
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.AlarmThresholdChange, alarmParamDao)
    }

    private fun insertOrUpdatePatientAlarm(alarmParam: AlarmParametersDao){
        dbCoroutine.launch {
            val patientId = patientDao.getActivePatientId()
            val patientAlarm = getCurrentPatientAlarmsByParamName(alarmParam.paramName)
            val alarmStatus = if(alarmParam.alarmStatus) 1 else 0
            if(patientAlarm != null){
                patientAlarm.alarmSound = alarmParam.alarmSound
                patientAlarm.alarmStatus = alarmStatus
                patientAlarm.timeStamp = getCurrentTime()
                patientAlarm.extremeHighValue = alarmParam.extremeHighValue
                patientAlarm.highValue = alarmParam.highValue
                patientAlarm.extremeLowValue = alarmParam.extremeLowValue
                patientAlarm.lowValue = alarmParam.lowValue
                alarmDao.updatePatientAlarm(patientAlarm)
                alarmParam.patientAlarmId = patientAlarm.patientAlarmId
            } else{
                val parameter = parametersDao.getParameterByName(alarmParam.paramName)
                if(parameter != null){
                    val newPatientAlarm = PatientAlarmTable(
                        null,
                        patientId,
                        parameter.paramId!!,
                        null,
                        parameter.paramName,
                        null,
                        alarmParam.lowValue,
                        alarmParam.extremeLowValue,
                        alarmParam.highValue,
                        alarmParam.extremeHighValue,
                        getCurrentTime(),
                        alarmStatus,
                        alarmParam.alarmSound
                    )
                    alarmParam.patientAlarmId = alarmDao.insertPatientAlarm(newPatientAlarm).toInt()
                }
            }
        }
    }

    private suspend fun getCurrentPatientAlarmsByParamName(paramName: String): PatientAlarmTable?{
        var patientAlarm: PatientAlarmTable? = null
        val patientAlarms = alarmDao.getCurrentPatientAlarmUsingParamName(paramName)?.toMutableList()
        if(!patientAlarms.isNullOrEmpty()){
            if(patientAlarms.size > 1){
                patientAlarm = patientAlarms.first()
                patientAlarms.remove(patientAlarm)
                for(alarm in patientAlarms){
                    alarmDao.deletePatientAlarmParameter(alarm)
                }
            } else{
                patientAlarm = patientAlarms.first()
            }
        }
        return patientAlarm
    }

    private fun updateAlarmSettingsParameter(
        alarmParameters: AlarmParametersDao,
        defaultAlarm: DefaultAlarmSettingsTable?,
        patientAlarm: PatientAlarmTable?
    ) {
        try{
            if (patientAlarm != null) {
                alarmParameters.extremeHighValue = patientAlarm.extremeHighValue!!
                alarmParameters.extremeLowValue = patientAlarm.extremeLowValue!!
                alarmParameters.highValue = patientAlarm.highValue!!
                alarmParameters.lowValue = patientAlarm.lowValue!!
                alarmParameters.alarmSound = patientAlarm.alarmSound
                val alarmStatus = patientAlarm.alarmStatus == 1
                alarmParameters.alarmStatus = alarmStatus
            } else {
                alarmParameters.extremeHighValue = defaultAlarm?.defaultAlarmExtremeHighValue!!
                alarmParameters.extremeLowValue = defaultAlarm.defaultAlarmExtremeLowValue!!
                alarmParameters.highValue = defaultAlarm.defaultAlarmHighValue!!
                alarmParameters.lowValue = defaultAlarm.defaultAlarmLowValue!!
                alarmParameters.alarmSound = defaultAlarm.defaultSound
                val alarmStatus = defaultAlarm.defaultAlarmStatus == 1
                alarmParameters.alarmStatus = alarmStatus
            }
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.AlarmThresholdChange, alarmParameters)
        }catch(ex: Exception){
            Log.d("UpdateAlarmSettings", ex.stackTraceToString())
        }
    }
}