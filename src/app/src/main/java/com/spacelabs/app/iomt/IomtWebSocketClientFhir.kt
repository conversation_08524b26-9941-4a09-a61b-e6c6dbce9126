package com.spacelabs.app.iomt

import android.util.Log
import com.spacelabs.app.api.ApiResponseData
import com.spacelabs.app.api.data.ApiDataTransactionManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.PatientDaoHelper
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.net.URI

class IomtWebSocketClientFhir(
     url: URI?,
) : WebSocketClient(url) {

    private val apiRespData: ApiResponseData = createDummyApiResponseData()
    private val apiDataManager = ApiDataTransactionManager()

    private val tag = "FHIRClass"

    override fun onOpen(handshakedata: ServerHandshake?) {
        Log.d(tag, "Connection Opened")
    }

    override fun onMessage(message: String?) {
        try {
            checkResourceTypeAndTakeAction(message!!)
            Log.d(tag, "onMessage: $message")

        } catch (e: JSONException) {
            throw RuntimeException(e)
        }
        if (!message.contains("success", ignoreCase = true))
            Log.d(tag, "Received observation response: $message")
    }

    override fun onClose(code: Int, reason: String?, remote: Boolean) {
        if (CommonDataArea.hasInternetConnection)
            IomtApiManager.connectIomt()
    }

    override fun onError(ex: Exception?) {
        if (ex != null) {
            Log.e(tag, ex.stackTraceToString())
        }
    }

    override fun send(text: String?) {
        if (IomtApiManager.webSocketClient != null)
            super.send(text)
    }


    private fun checkResourceTypeAndTakeAction(message: String) {
        val json = JSONObject(message)
        when (json.getString("resourceType")) {
            "Patient" -> parsePatientAndUpdateDB(json)
            "OperationOutcome" -> onReceiveObservationResponse(json)
        }

   }
    private fun parsePatientAndUpdateDB(json: JSONObject) {
        val patientDaoHelper = PatientDaoHelper(CommonDataArea.curActivity!!)

        val identifier = json.getString("id")
        updatePatientIdentifier(identifier)
        apiRespData.patient.patientID1 = json.getString("id")
//        updatePatientIdentifier(identifier)

        val nameArray = json.getJSONArray("name")
        updatePatientName(nameArray)
        apiRespData.patient.gender = json.getString("gender")
        apiRespData.patient.birthDate = json.getString("birthDate")
        patientDaoHelper.updateCurrentPatientWithAPIPatientInfo(apiRespData)
    }

    private fun onReceiveObservationResponse(json: JSONObject) {
        val uuId = json.getString("id")
        var responseText: String? = null
        val issueArray = json.getJSONArray("issue")
        for (i in 0 until issueArray.length()) {
            val jObject = issueArray.getJSONObject(i)
            val severity = jObject.getString("severity")
            if(!severity.equals("information", true))
                Log.d("onReceiveObservationResponse", jObject.getString("details"))
            if (jObject.has("details")) {
                responseText = jObject.getJSONObject("details").getString("text")
            }
        }
        if (responseText != null) {
            apiDataManager.checkResponseAndUpdateDb(uuId, responseText)
        }
    }

    private fun updatePatientIdentifier(id: String) {
        apiRespData.patient.patientID1 = id
    }

    private fun updatePatientName(nameArray: JSONArray){
        for(i in 0 until nameArray.length()){
            val jObject = nameArray.getJSONObject(i)
            apiRespData.patient.firstName = jObject.getJSONArray("given").getString(0)
            apiRespData.patient.family = jObject.getString("family")
        }
    }

    private fun createDummyApiResponseData(): ApiResponseData {
        // Create dummy data for ApiResponseData
        return ApiResponseData(
            authToken = "dummyAuthToken",
            url = "dummyUrl",
            accountNumber = "dummyAccountNumber",
            userName = "dummyUserName",
            origin = "dummyOrigin",
            token = "dummyToken",
            password = "dummyPassword",
            jwtToken = "dummyJwtToken",
            patientName = "dummyPatientName",
            patient = ApiResponseData.Patient(
                patientID1 = "dummyPatientID1",
                firstName = "dummyFirstName",
                family = "dummyFamily",
                gender = "dummyGender",
                birthDate = "dummyBirthDate"
            )
        )
    }
}
