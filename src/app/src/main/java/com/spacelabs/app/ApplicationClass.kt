package com.spacelabs.app

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import com.spacelabs.app.common.StaticTestDataManager

class ApplicationClass: Application() {
    private var mContext: Context? = null

    companion object{
        @SuppressLint("StaticFieldLeak")
        var applicationInstance: ApplicationClass? = null
        fun getInstance(): ApplicationClass? {
            return applicationInstance
        }
    }

    override fun onCreate() {
        super.onCreate()
        mContext = applicationContext
        applicationInstance = this

        // Initialize static test data manager
        StaticTestDataManager.getInstance().initialize(this)
    }

    override fun getApplicationContext(): Context {
        return super.getApplicationContext()
    }
}