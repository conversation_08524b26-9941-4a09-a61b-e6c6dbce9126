package com.spacelabs.app.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblPatient")
data class PatientTable(
    @PrimaryKey(autoGenerate = true)
    val patientId: Int?,
    val firstName: String,
    val middleName: String?,
    val lastName: String,
    val patientID1: String,
    val patientID2: String?,
    val dob: String?,
    val age: Int,
    val gender: String,
    val height: Double?,
    val weight: Double?,
    val bedName: String?,
    val admitDate: String?,
    val facilityName: String?,
    val createdOn: String?,
    @ColumnInfo(defaultValue = "active")
    val status: String,
    val isTempPatient: Int
)