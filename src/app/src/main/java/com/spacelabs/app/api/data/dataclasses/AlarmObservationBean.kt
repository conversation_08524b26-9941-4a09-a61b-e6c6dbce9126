package com.spacelabs.app.api.data.dataclasses

import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import kotlin.math.roundToInt

data class AlarmObservationBean(
    val resourceType: String = "Observation",
    val observationId: String,
    val status: String = "final",
    val metaProfile: String = "http://hl7.org/fhir/StructureDefinition/vitalsigns",
    val metaTagDisplay1: String = "extreme",
    val metaTagDisplay2: String = "acknowledged",
    val metaTagDisplay3: String = "eventTime",
    val alarmType: String,
    var isAcknowledged: Boolean,
    val identifierSystem: String = "https://acme.org/identifiers",
    val identifierValue: String = "351877479666121",
    val codingSystem: String = "http://terminology.hl7.org/CodeSystem/observation-category",
    val codingCode: String = "vital-signs",
    val codingDisplay: String = "Vital Signs",
    val vitalSystem: String = "http://loinc.org",
    var vitalTriple: Triple<String, String, String>,
    val subjectReference: String = "Patient",
    var alarmStartTime: String,
    var alarmEndTime: String?,
    val performerReference: String = "Practitioner",
    val performerId: String = "f005",
    val interpretationSystem: String = "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation",
    var vitalValue: String,
    var vitalRanges: Map<String, Int>,
    val valueQuantitySystem: String = "http://unitsofmeasure.org",
){
    lateinit var interpretationCode: String
    lateinit var interpretationDisplay: String
    lateinit var interpretationText: String
    var isExtreme: Boolean = false

    val valueQuantityCode: String

    init {
        setInterpretationBasedOnValue()
        valueQuantityCode = setValueQuantityCode()
    }

    companion object {
        const val extremeHigh = "EH"
        const val extremeLow = "EL"
        const val high = "H"
        const val low = "L"
        const val normal = "N"

        val hrRanges = getValueRanges(CommonDataArea.hrAlarmDao)
        val rrRanges = getValueRanges(CommonDataArea.respAlarmDao)
        val spo2Ranges = getValueRanges(CommonDataArea.spo2AlarmDao)
        val tempRanges = getValueRanges(CommonDataArea.tempAlarmDao)
        val bpDiaRanges = getValueRanges(CommonDataArea.bpDiaAlarmDao)
        val bpSysRanges = getValueRanges(CommonDataArea.bpSysAlarmDao)

        private fun getValueRanges(alarmParametersDao: AlarmParametersDao): Map<String, Int>{
            return if(alarmParametersDao.extremeHighValue.toInt() > 0)
                mapOf(
                    extremeHigh to alarmParametersDao.extremeHighValue.toInt(),
                    high to alarmParametersDao.highValue.toInt(),
                    low to alarmParametersDao.lowValue.toInt(),
                    extremeLow to alarmParametersDao.extremeLowValue.toInt()
                )
            else
                mapOf(
                    high to alarmParametersDao.highValue.toInt(),
                    low to alarmParametersDao.lowValue.toInt(),
                )
        }
    }

    private fun setValueQuantityCode(): String{
        return when(vitalTriple.second){
            "beats/minute", "breaths/minute" -> "/min"
            "%" -> "%"
            else -> String()
        }
    }

    private fun setInterpretationBasedOnValue() {
        val value = vitalValue.toDouble().roundToInt()
        if(vitalRanges.size > 2){
            when{
                value >= vitalRanges[extremeHigh]!! -> setInterpretation(extremeHigh)
                value in vitalRanges[high]!! until vitalRanges[extremeHigh]!! -> setInterpretation(high)
                value in (vitalRanges[low]!! + 1) until vitalRanges[high]!! -> setInterpretation(normal)
                value in (vitalRanges[extremeLow]!! + 1) ..vitalRanges[low]!! -> setInterpretation(low)
                value <= vitalRanges[extremeLow]!! -> setInterpretation(extremeLow)
            }
        } else{
            when{
                value >= vitalRanges[high]!! -> setInterpretation(high)
                value <= vitalRanges[low]!! -> setInterpretation(low)
                else -> setInterpretation(normal)
            }
        }
    }

    private fun setInterpretation(code: String){
        interpretationCode = code
        when(code){
            extremeHigh -> {
                interpretationDisplay = "extreme high"
                interpretationText = "Extreme High"
                isExtreme = true
            }
            high -> {
                interpretationDisplay = "high"
                interpretationText = "Above high normal"
                isExtreme = false
            }
            low -> {
                interpretationDisplay = "low"
                interpretationText = "Below low normal"
                isExtreme = false
            }
            extremeLow -> {
                interpretationDisplay = "extreme low"
                interpretationText = "Extreme low"
                isExtreme = true
            }
            else -> {
                interpretationDisplay = "normal"
                interpretationText = "Normal"
                isExtreme = false
            }
        }
    }
}