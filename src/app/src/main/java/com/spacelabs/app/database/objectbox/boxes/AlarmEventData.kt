package com.spacelabs.app.database.objectbox.boxes

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

@Entity
data class AlarmEventData(
    @Id
    var alarmEventId: Long?,
    val alarmEventUUID: String,
    val alarmId: Long,
    val value: Double,
    val updatedTime: String,
    val alarmStatus: String,
    var uploadStatus: Int = 0,
    var uploadTime: String?,
    var tickValue: Long?
)