package com.spacelabs.app.activities.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.Window
import androidx.appcompat.widget.AppCompatButton
import android.widget.ImageView
import android.widget.TextView
import com.spacelabs.app.R

class FullScreenDialog(context: Context, private val message: String,private val subMessage: String, private val isSuccess: Boolean) : Dialog(context) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(R.layout.dialog_full_screen)
        setCanceledOnTouchOutside(false)
        setCancelable(false)

        val companyIcon: ImageView = findViewById(R.id.company_icon)
        val dialogIcon: ImageView = findViewById(R.id.dialogIcon)
        val dialogMessage: TextView = findViewById(R.id.dialogMessage)
        val dialogSubMessage: TextView = findViewById(R.id.dialogSubMessage)
        val okButton: AppCompatButton = findViewById(R.id.okButton)

        dialogIcon.setImageResource(if (isSuccess) R.drawable.ic_device_registered_success else R.drawable.ic_device_registered_unsuccess)
        dialogMessage.text = message
        dialogSubMessage.text = subMessage
        if (isSuccess) okButton.visibility= View.GONE
        if (!isSuccess) companyIcon.visibility= View.INVISIBLE
        okButton.setOnClickListener { dismiss() }
    }
}
