package com.spacelabs.app.activities.snsActivities.helpers

import android.content.Context
import android.util.Log
import com.spacelabs.app.activities.commonActivities.helpers.CommonActivityHelper
import com.spacelabs.app.activities.iomtActivities.QrScannerActivity
import com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.database.daoHelper.SettingDaoHelper

class QrCodeConfigHelperSns( qrCodeActivity: QrScannerActivitySns) : CommonActivityHelper(qrCodeActivity) {

    private val context: Context
    private val qrCodeActivity : QrScannerActivitySns

    init {
        this.qrCodeActivity = qrCodeActivity
        context = qrCodeActivity
    }
    fun navigateIfAccountIdExists() {
        navigateIfSettingExist(SettingDaoHelper.AppSettings.AccountId.key)
    }

    fun saveAccountIdSettingsAndNavigateToNext(value: String) {
        saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.AccountId)
        Log.d("SAVE_ACCOUNT_ID", "saveAccountIdSettingsAndNavigateToNext: $value")
        Log.d("SAVE_ACCOUNT_ID", "Account Id: ${SnsApiManager.ACCOUNT_ID}")
    }
    fun saveAuthSettingsAndNavigateToNext(key: Int,value: String) {
        when (key) {
            0 -> saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.apiUrl)
            1 -> saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.apiToken)
            else -> return
        }
       /* Log.d("SAVE_ACCOUNT_ID", "saveAccountIdSettingsAndNavigateToNext: $value")
        Log.d("SAVE_ACCOUNT_ID", "LOGIN_API_URL: ${SnsApiManager.LOGIN_API_URL}")*/
    }

    fun saveDeviceValues(key: Int, value: String):Boolean {
        when (key) {
            0 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.OauthUsername)
            1 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.OauthPassword)
            2 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.websocketUrl)
            3 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.apiUrl)
            4 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.apiToken)
            5 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.Origin)
            6 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.BodyToken)
            else -> return false
        }
        return true
    }

}
