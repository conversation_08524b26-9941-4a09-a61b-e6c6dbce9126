package com.spacelabs.app.ui.dialogs

import android.content.Context
import android.util.Log
import androidx.appcompat.widget.AppCompatCheckBox
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2BloodPressureMonitor
import com.sibelhealth.bluetooth.sensor.sibel.SibelSensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.MainActivity
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.PatientDaoHelper
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.database.dataClearance.DataDeletionHandler
import com.spacelabs.app.sensor.SibelSensorManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class DialogActionHelper(mainActivity: MainActivity) {

    private val mainActivity: MainActivity
    private val context: Context

    private val patientDaoHelper: PatientDaoHelper
    private val settingDaoHelper: SettingDaoHelper
    private val dataDeletionHandler: DataDeletionHandler


    init {
        this.mainActivity = mainActivity
        context = mainActivity

        patientDaoHelper = PatientDaoHelper(context)
        settingDaoHelper = SettingDaoHelper(context)
        dataDeletionHandler = DataDeletionHandler(context)
    }

    fun onRemoveBtnClick(context: Context, checkBoxesAndSensorTypes: Map<AppCompatCheckBox, SensorType>){
        for(boxAndType in checkBoxesAndSensorTypes){
            val box = boxAndType.key
            val type = boxAndType.value
            val isChecked = box.isChecked

            if(isChecked){
                resetSensorConfigBasedOnType(context, type)
            }
        }
    }

    private fun resetSensorConfigBasedOnType(context: Context, type: SensorType) {
        when(type) {
            SensorType.CHEST -> resetSensorConfiguration(context, CommonDataArea.chestSensor, type)
            SensorType.LIMB -> resetSensorConfiguration(context, CommonDataArea.limbSensor, type)
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> resetBpSensorConfiguration(context, CommonDataArea.bpSensor)
            else -> Log.d("RemoveButtonClick", "Sensor with Unknown Type")
        }
    }

    fun resetSensorConfiguration(context: Context, sensor: SibelSensor?, sensorType: SensorType){
        sensor?.stopStream()
        when(sensorType){
            SensorType.LIMB -> {
                CommonDataArea.limbSensor = null
                CommonDataArea.isUserDisconnectedLimbPatch = true
            }
            SensorType.CHEST -> onChestSensorResetConfiguration(sensor)
            else -> Log.d("SensorResetConfig", "Sensor With Unknown Type")
        }
        sensor?.disconnect(true)
        SibelSensorManager.enrollSensorNameByType(context, "", sensorType)
    }

    private fun onChestSensorResetConfiguration(sensor: SibelSensor?){
        if(sensor!= null)
            CommonDataArea.chestSensor = null

        CommonDataArea.isUserDisconnectedChestPatch = true
    }

    fun resetBpSensorConfiguration(context: Context, bpMonitor: BP2BloodPressureMonitor?){
        bpMonitor?.disconnect(true)
        SibelSensorManager.enrollSensorNameByType(context, "", SensorType.BP2_BLOOD_PRESSURE_MONITOR)
        CommonDataArea.bpSensor = null
        CommonDataArea.BpSys = Double.NaN
        CommonDataArea.BpDia = Double.NaN
        CommonDataArea.bpTime=""
        CommonDataArea.isUserDisconnectedBpPatch = true
    }

    fun createUICoroutineScope(): CoroutineScope {
        return CoroutineScope(Dispatchers.Main + Job())
    }

    fun onDischargePatient() {
        mainActivity.ioScope.launch {
            patientDaoHelper.setAllPatientsToInActive()
            settingDaoHelper.resetPersonalSettingsOnDischarge()
            dataDeletionHandler.deleteDBDataOnDischarge()
        }
    }
}