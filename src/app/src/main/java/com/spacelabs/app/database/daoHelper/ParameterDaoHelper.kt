package com.spacelabs.app.database.daoHelper

import android.content.Context
import android.util.Log
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.database.dao.EventsDao
import com.spacelabs.app.database.entities.DefaultAlarmSettingsTable
import com.spacelabs.app.database.entities.EventListTable
import com.spacelabs.app.database.entities.ParametersTable
import kotlinx.coroutines.*
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.BufferedReader
import java.io.FileNotFoundException
import java.io.InputStreamReader

class ParameterDaoHelper(context: Context) : DbDaoHelperManager(context) {

    fun loadDefaultAlarmValuesToDbFromFile(context: Context) {
        dbCoroutine.launch {
            try {
                val lines = BufferedReader(InputStreamReader(context.assets.open("defaultdbvalues.txt"))).use { it.readText() }
                val parametersJsonArray = JSONObject(lines).getJSONArray("parameters")
                checkAndUpdateParameter(parametersJsonArray)
                val eventsJsonArray = JSONObject(lines).getJSONArray("events")
                checkAndUpdateEvents(eventsJsonArray, eventsDao)
            } catch (ex: FileNotFoundException) {
                LogWriter.writeExceptLog("LoadFromFileToDB", ex.stackTraceToString())
            } catch (ex: JSONException) {
                LogWriter.writeExceptLog("LoadFromFileToDB", ex.stackTraceToString())
            } catch (ex: Exception) {
                LogWriter.writeExceptLog("LoadFromFileToDB", ex.stackTraceToString())
            }
        }
    }

    private suspend fun checkAndUpdateEvents(eventsJSONArray: JSONArray, eventsDao: EventsDao) {
        val allEvents = eventsDao.getAllEvents()
        val eventNames: MutableList<String> = mutableListOf()
        if (allEvents?.isNotEmpty() == true) {
            for (j in allEvents) {
                eventNames.add(j.eventName)
            }
        }
        for (i in 0 until eventsJSONArray.length()) {
            val json: JSONObject = eventsJSONArray.getJSONObject(i)
            val eventName = json.getString("eventName")
            val eventType = json.getString("eventType")
            val newEvent = EventListTable(null, eventName, eventType)
            if (eventNames.isEmpty() || !(eventNames.contains(eventName))) {
                eventsDao.insertEvent(newEvent)
            }
        }
    }

    private suspend fun checkAndUpdateParameter(parametersJsonArray: JSONArray) {
        val paramList = parametersDao.getAllParameters()
        paramList?.let {
            handleNonEmptyParameterList(parametersJsonArray, it)
        }
    }

    private suspend fun handleNonEmptyParameterList(parametersJsonArray: JSONArray, paramList: List<ParametersTable>) {
        for (i in 0 until parametersJsonArray.length()) {
            val json: JSONObject = parametersJsonArray.getJSONObject(i)
            val paramName = json.getString("paramName")

            Log.d("HandleNonEmptyParameterList:", paramName)

            if (paramList.none { param -> param.paramName == paramName }) {
                val alarmParamDao = saveParameterAndAlarmValues(json)
                val id = insertNewParameterAndAlarm(json)
                if(alarmParamDao != null)
                    alarmParamDao.defaultAlarmId = id
            } else {
                updateExistingParameterAndAlarm(json, paramName)
            }
        }
    }

    private suspend fun insertNewParameterAndAlarm(json: JSONObject): Int {
        val parameter = ParametersTable(
            null,
            json.getString("paramName"),
            json.getString("paramCategory"),
            json.optInt("paramType"),
            json.getString("linkedSensorDef")
        )
        val id = parametersDao.insertParameter(parameter)

        val defaultAlarm = createDefaultAlarm(json, id.toInt())
        return alarmDao.insertDefaultAlarm(defaultAlarm).toInt()
    }

    private suspend fun updateExistingParameterAndAlarm(json: JSONObject, paramName: String) {
        val currentParameterAndAlarm = alarmDao.getDefaultAlarmWithParameterOnParamName(paramName)
        val paramId = currentParameterAndAlarm.parameter.paramId

        saveParameterAndAlarmValues(json)

        val parameter = ParametersTable(
            paramId,
            json.getString("paramName"),
            json.getString("paramCategory"),
            json.optInt("paramType"),
            json.getString("linkedSensorDef")
        )
        parametersDao.updateParameter(parameter)

        val defaultAlarm = createDefaultAlarm(json, paramId!!)
        alarmDao.updateDefaultAlarm(defaultAlarm)
    }

    private fun createDefaultAlarm(json: JSONObject, paramId: Int): DefaultAlarmSettingsTable {
        return DefaultAlarmSettingsTable(
            null,
            paramId,
            json.getString("defaultAlarmCategory"),
            json.getDouble("defaultAlarmHighValue"),
            json.getDouble("defaultAlarmLowValue"),
            json.getDouble("defaultAlarmExtremeHighValue"),
            json.getDouble("defaultAlarmExtremeLowValue"),
            json.getInt("defaultAlarmStatus"),
            json.getInt("defaultSound")
        )
    }

    private fun saveParameterAndAlarmValues(json: JSONObject): AlarmParametersDao? {
        return when (json.getString("paramName").uppercase()) {
            StreamDataType.HR.name -> setAlarmParameterDao(json, CommonDataArea.hrAlarmDao)
            StreamDataType.RR.name -> setAlarmParameterDao(json, CommonDataArea.respAlarmDao)
            StreamDataType.SPO2.name -> setAlarmParameterDao(json, CommonDataArea.spo2AlarmDao)
            StreamDataType.TEMP_SKIN.name -> setAlarmParameterDao(json, CommonDataArea.tempAlarmDao)
            StreamDataType.BP_SYS.name -> setAlarmParameterDao(json, CommonDataArea.bpSysAlarmDao)
            StreamDataType.BP_DIA.name -> setAlarmParameterDao(json, CommonDataArea.bpDiaAlarmDao)
            else -> null
        }
    }

    private fun setAlarmParameterDao(json: JSONObject, alarmParamsDao: AlarmParametersDao): AlarmParametersDao {
        val paramName = json.getString("paramName")
        val highMaxAllowedValue = json.getDouble("highMaxAllowed")
        val lowMinAllowedValue = json.getDouble("lowMinAllowed")
        alarmParamsDao.paramName = paramName
        alarmParamsDao.highMaxValue = highMaxAllowedValue
        alarmParamsDao.lowMinValue = lowMinAllowedValue

        return alarmParamsDao
    }
}
