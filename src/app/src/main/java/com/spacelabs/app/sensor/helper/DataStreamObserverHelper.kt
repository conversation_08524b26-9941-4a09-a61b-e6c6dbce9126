package com.spacelabs.app.sensor.helper

import android.annotation.SuppressLint
import android.util.Log
import com.sibelhealth.bluetooth.sensor.sibel.chest.ChestSensor
import com.sibelhealth.bluetooth.sensor.sibel.limb.LimbSensor
import com.sibelhealth.bluetooth.sensor.sibel.model.BodyPosition
import com.sibelhealth.bluetooth.sensorservice.datastream.*
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.charts.XYDataSource
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.common.CommonDataArea.Companion.isECGWritePtrUpdated
import com.spacelabs.app.common.CommonDataArea.Companion.isPPGWritePtrUpdated
import com.spacelabs.app.common.CommonDataArea.Companion.isRESPWritePtrUpdated
import com.spacelabs.app.common.CommonDataArea.Companion.masterStream
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.dataManager.MeasurementDataManager
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.sensor.SibelSensorManager
import kotlinx.coroutines.runBlocking
import java.nio.ByteBuffer
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.roundToInt
import kotlin.math.sqrt

class DataStreamObserverHelper {

    private val measurementDataManager = MeasurementDataManager()

    @SuppressLint("SimpleDateFormat")
    fun onHealthDataUpdatedAction(sensor: Sensor, dataPackage: HealthDataPackage) {
        try {
            val isDetached = isSensorDetached(sensor)

            for (valueArr in dataPackage.data.keys) {
                val val1 = if(!isDetached)
                    dataPackage.data[valueArr]?.get(0) ?: continue
                else
                    Double.NaN

                runBlocking {
                    setDataToParameterAndToDb(valueArr, sensor, val1)
                }
                onVitalDataUpdate(valueArr, val1)
            }
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onHealthDataUpdated", e.message)
        }
    }

    private fun onVitalDataUpdate(stream: DataType, value: Double?) {
        val currentParam: CurrentAlarmParameter? = when(stream) {
            StreamDataType.HR -> CurrentAlarmParameter.Ecg
            StreamDataType.RR -> CurrentAlarmParameter.Resp
            StreamDataType.SPO2 -> CurrentAlarmParameter.Spo2
            StreamDataType.TEMP_SKIN -> CurrentAlarmParameter.Temp
            else -> null
        }
        if(currentParam != null)
            CommonEventHandler.postAlarmEvent(currentParam, value)

        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalReceived, stream)
    }

    @SuppressLint("SimpleDateFormat")
    fun onTempDataUpdatedAction(sensor: Sensor, dataPackage: TempDataPackage) {
        try {
            if (isSensorDetached(sensor))
                return

            for (valueArr in dataPackage.data.keys) {
                val val1 = dataPackage.data[valueArr]

                when (valueArr) {
                    StreamDataType.TEMP_SKIN -> {
                        setReceivedTempValue(sensor, val1!![0], dataPackage.timestamp.first())
                    }
                }
            }
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onTempDataUpdated", e.stackTraceToString())
        }
    }

    fun onEcgDataUpdatedAction(sensor: Sensor, dataPackage: EcgDataPackage) {
        try {
           /* if (isSensorDetached(sensor))
                return*/

            processWaveStream(sensor, dataPackage)

            if (masterStream == XYDataSource.XYDataType.PPG_IR.name && !isECGWritePtrUpdated || masterStream == XYDataSource.XYDataType.Resp.name && !isECGWritePtrUpdated) {
              if(!isSensorDetached(sensor))  CommonDataArea.ecgXYData?.calculateFillingPosition()
            }

        } catch (e: Exception) {
            LogWriter.writeExceptLog("onEcgDataUpdated", e.message)
        }
    }

    /*fun onRRWaveformDataUpdated(sensor: Sensor, dataPackage: RRDataPackage) {
        try {
            *//*if (isSensorDetached(sensor))
                return*//*

            processWaveStream(sensor, dataPackage)
            if(masterStream == XYDataSource.XYDataType.ECG.name && !isRESPWritePtrUpdated || masterStream == XYDataSource.XYDataType.PPG_IR.name && !isRESPWritePtrUpdated) {
                if(!isSensorDetached(sensor))  CommonDataArea.respXYData?.calculateFillingPosition()
            }
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onRRWaveformDataUpdated", e.message)

        }
    }*/

    fun onCommonDataPacketReceivedAction(sensor: Sensor, dataPackage: AttributedDataPackage) {
        if(dataPackage.address != sensor.address) return
       /* val is26hz =  dataPackage.data[StreamDataType.ACCEL_X]?.size == 4
        val samplingRate = if (is26hz) 26.0 else 416.0

        val samplingRate = (dataPackage.attributes["samplingRate"] as UShort?)?.toInt()
        Log.d("samplingRate", "is26hz :${dataPackage.attributes} , samplingRate: $samplingRate ")*/


        try {
            dataPackage.data.keys.forEach { type ->
                when(type) {
                    StreamDataType.RR_WAVEFORM -> {
                        processWaveStream(sensor, dataPackage)
                        if(masterStream != XYDataSource.XYDataType.ECG.name && !isRESPWritePtrUpdated || masterStream != XYDataSource.XYDataType.PPG_IR.name && !isRESPWritePtrUpdated) {
                            CommonDataArea.respXYData?.calculateFillingPosition()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onRRWaveformDataUpdated", e.message)

        }
    }

    private fun processWaveStream(sensor: Sensor, data: Any) {
        val xyDataAndParamNameAndData = getWaveTripleBasedOnDataPacketType(data)
        xyDataAndParamNameAndData ?: return

        val xyData = xyDataAndParamNameAndData.first ?: return
        val paramName = xyDataAndParamNameAndData.second
        val dataPackage = xyDataAndParamNameAndData.third
        val isDetached = isSensorDetached(sensor)
        try {
            val timestampAndMillis = TimestampUtils.getFormattedTimeWithEpochTime(dataPackage.timestamp.first(), sensor.sensorType)
            val valueList = mutableListOf<Double>()
            for (valueArr in dataPackage.data.values) {
                valueList.addAll(valueArr.toMutableList())
                xyData.filling(true)
                val bb: ByteBuffer = ByteBuffer.allocate(valueArr.size * 8)
                synchronized(xyData) {
                    for (value in valueArr) {
                        if (!isDetached) {
                            xyData.addData(value.toFloat())
                            bb.putDouble(value)
                        }
                        else{
                            val value1 = 0.0
                            xyData.addData(value1.toFloat())
                        }
                    }
                }
                xyData.filling(false)
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.StreamChartUpdate, xyData)
            }
            if (!isDetached) {
                parseAndSaveWaveformToDb(sensor, valueList, paramName, timestampAndMillis)
            }
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onWaveDataUpdated", e.message)
        }
    }

    private fun parseAndSaveWaveformToDb(
        sensor: Sensor,
        data: List<Double>?,
        paramName: String,
        timestampAndMillis: Pair<String, Long>,
    ) {
        try {
            data ?: return
            val bufferSize = data.size * java.lang.Double.BYTES
            val byteBuffer: ByteBuffer = ByteBuffer.allocate(bufferSize)
            val doubleBuffer = byteBuffer.asDoubleBuffer()
            doubleBuffer.put(data.toDoubleArray())

            val measurementData = ByteArray(bufferSize)
            byteBuffer[measurementData]
            measurementDataManager.insertByteStreamToDB2(sensor, measurementData, paramName, timestampAndMillis)
        } catch (ex: OutOfMemoryError) {
            Log.d("ParseAndSaveWaveformToDb", ex.stackTraceToString())
        }
    }

    private fun getWaveTripleBasedOnDataPacketType(dataPackage: Any): Triple<XYDataSource?, String, DataPackage>? {
        return when (dataPackage::class.java) {
            EcgDataPackage::class.java -> Triple(
                CommonDataArea.ecgXYData,
                "ECG",
                dataPackage as EcgDataPackage
            )
            /*RRDataPackage::class.java -> Triple(
                CommonDataArea.respXYData,
                "RESP",
                dataPackage as RRDataPackage
            )*/
            else -> Triple(
                CommonDataArea.respXYData,
                "RESP",
                dataPackage as AttributedDataPackage
            )
        }
    }

    fun onPpgDataUpdatedAction(sensor: Sensor, dataPackage: PpgDataPackage) {
        try {
            val isDetached = isSensorDetached(sensor)
            LogWriter.timeCheckPointStart()
            val xyPlot = CommonDataArea.ppgIRXYData ?: return
            val currentTime = TimestampUtils.getFormattedTimeWithEpochTime(dataPackage.timestamp.first(), sensor.sensorType)
            val valueList = mutableListOf<Double>()
            for ((index, valueArr) in dataPackage.data.values.withIndex()) {
                xyPlot.filling(true)
                val wsFloatArray = FloatArray(valueArr.size)
                val bb: ByteBuffer = ByteBuffer.allocate(valueArr.size * 8)
                synchronized(xyPlot) {
                    var i = 0
                    for (value in valueArr) {
                        if (index == 0) {
                            if (!isDetached){
                                val tempVal = value.toFloat()
                                wsFloatArray[i++] = tempVal
                                bb.putDouble(value)
                                xyPlot.addData(value.toFloat())
                                valueList.add(value)
                            }else{
                                val value1=0.0
                                xyPlot.addData(value1.toFloat())
                                //valueList.add(value1)
                            }

                        }
                    }
                }

                xyPlot.filling(false)
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.StreamChartUpdate, CommonDataArea.ppgIRXYData)

                val measurementData1: ByteArray? = bb.array()
                val isSensorDetached = isSensorDetached(sensor)

              /*  if (!isSensorDetached && measurementData1 != null && !enableSnsApi) {
                    measurementDataManager.insertByteStreamToDB2(
                        sensor,
                        measurementData1,
                        "PPG",
                        currentTime,
                    )
                }*/

            }
            if (enableSnsApi && !isDetached) {
                parseAndSaveWaveformToDb(sensor, valueList, "PPG", currentTime)
            }
            if(masterStream == XYDataSource.XYDataType.ECG.name && !isPPGWritePtrUpdated || masterStream == XYDataSource.XYDataType.Resp.name && !isPPGWritePtrUpdated) {
                if(!isSensorDetached(sensor))  xyPlot.calculateFillingPosition()
            }
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onPpgDataUpdated", e.message)
        }

    }

    fun onBioZDataUpdatedAction(sensor: Sensor, dataPackage: BioZDataPackage) {
        if (isSensorDetached(sensor))
            return

        for (valueArr in dataPackage.data.values) {
            for (value in valueArr) {
                Log.d(SibelSensorManager.tag, "Bio value = $value")
                CommonDataArea.biozData[CommonDataArea.biozWritePointer] = value.toFloat()
                CommonDataArea.biozWritePointer++
                if (CommonDataArea.biozWritePointer >= CommonDataArea.biozData.size) {
                    CommonDataArea.biozWritePointer = 0
                }
            }
        }
    }

    fun onAccelDataUpdatedAction(sensor: Sensor, dataPackage: AccelDataPackage) {
        val isDetached = isSensorDetached(sensor)
        var (x, y, z) = Triple(0.0, 0.0, 0.0)
        for (valueMap in dataPackage.data) {
            val avg = getAccelAvg(valueMap.value)
            when (valueMap.key) {
                StreamDataType.ACCEL_X -> x = avg
                StreamDataType.ACCEL_Y -> y = avg
                StreamDataType.ACCEL_Z -> z = avg
            }
        }
        val angle = calculateAngle(Triple(x, y, z)).roundToInt()
        CommonDataArea.angle = if(!isDetached) abs(angle) else 0
        onVitalDataUpdate(StreamDataType.ACCEL, null)
    }

    private fun calculateAngle(triple: Triple<Double, Double, Double>): Double {
        val radians = if (CommonDataArea.patientMode.equals("Bed Bound", true))
            atan2(triple.second, sqrt(triple.first * triple.first + triple.third * triple.third))
        else
            atan2(triple.first, sqrt(triple.second * triple.second + triple.third * triple.third))
        return Math.toDegrees(radians)
    }

    private fun getAccelAvg(doubleArray: DoubleArray): Double {
        var sum = 0.0
        for (value in doubleArray) {
            sum += value
        }
        return sum / doubleArray.size
    }

    fun onFallCountDataUpdatedAction(sensor: Sensor, dataPackage: DataPackage) {
        try {
            if (isSensorDetached(sensor))
                return

            val isAcknowledged = false
            val currentTime = TimestampUtils.getFormattedTimeWithEpochTime(dataPackage.timestamp.first(), sensor.sensorType)
            for (valueArr in dataPackage.data.values) {
                for (value in valueArr) {
                    if (value.toInt() > CommonDataArea.fallCount) {
                        CommonDataArea.fallCount = value.toInt()
                        CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.HasFallen, isAcknowledged)
                    }
                }
            }
           /* measurementDataManager.onVitalDataUpdatedAction(
                sensor,
                CommonDataArea.fallCount.toDouble(),
                StreamDataType.FALL_COUNT,
                currentTime
            )*/
        } catch (e: Exception) {
            LogWriter.writeExceptLog("Exception", e.message)
        }
    }

    fun onStepCountDataUpdatedAction(sensor: Sensor, dataPackage: StepCountDataPackage) {
        try {
            val isDetached = isSensorDetached(sensor)

            val currentTime = TimestampUtils.getCurrentTimeAndMillis()
            for (valueArr in dataPackage.data.values) {
                for (value in valueArr) {
                    val steps = if(!isDetached) value else 0.0
                    CommonDataArea.stepCount = steps.toInt()
                }
            }
            measurementDataManager.onVitalDataUpdatedAction(
                sensor,
                CommonDataArea.stepCount.toDouble(),
                StreamDataType.STEP_COUNT,
                currentTime
            )
            onVitalDataUpdate(StreamDataType.STEP_COUNT, null)
        } catch (ex: Exception) {
            Log.e("StepCountUpdate", ex.stackTraceToString())
        }
    }

    @SuppressLint("SimpleDateFormat")
    private fun setDataToParameterAndToDb(valueArr: DataType, sensor: Sensor, value: Double) {
        try {
            val currentTime = TimestampUtils.getCurrentTimeAndMillis()
            when (valueArr) {
                StreamDataType.PR -> CommonDataArea.PR = value
                StreamDataType.HR -> CommonDataArea.HR = value.toInt()
                StreamDataType.RR -> CommonDataArea.RR = value.toInt()
                StreamDataType.SPO2 -> CommonDataArea.spo2 = value
                StreamDataType.PI -> CommonDataArea.PI = value
                StreamDataType.BODY_POSITION -> {
                    onUpdateBodyPosition(BodyPosition.fromDouble(value)?.name)
                    if(value.isNaN()) return
                    measurementDataManager.onVitalDataUpdatedAction(
                        sensor,
                        CommonDataArea.angle.toDouble(),
                        StreamDataType.ACCEL,
                        currentTime
                    )
                }
                else -> Log.d("setDataToParameterAndDB", "Unknown Stream Type -> $valueArr")
            }
            if (value.isNaN()) return
            measurementDataManager.onVitalDataUpdatedAction(
                sensor,
                value,
                valueArr as StreamDataType,
                currentTime
            )
        } catch (ex: Exception) {
            Log.d("SetDataToParameterAndToDb", ex.stackTraceToString())
        }
    }

    private fun onUpdateBodyPosition(position: String?) {
        if (!position.equals(CommonDataArea.bodyPosition, true)) {
            CommonDataArea.bodyPosition = position ?: CommonDataArea.bodyPosition   /*String()*/
        }
    }

    @SuppressLint("SimpleDateFormat")
    private fun setReceivedTempValue(sensor: Sensor, value: Double, timestamp: Double) {
        val currentTime = TimestampUtils.getCurrentTimeAndMillis()
        val dateFormat = SimpleDateFormat("hh:mm a")
        val tempTime: String =
            dateFormat.format(Calendar.getInstance().time)
        if (sensor.sensorType == CommonDataArea.tempDisplaySensorType)
            setTempValueAndTime(sensor.sensorType, tempTime, value)
        if (!value.isNaN())
            measurementDataManager.onVitalDataUpdatedAction(
                sensor,
                value,
                StreamDataType.TEMP_SKIN,
                currentTime
            )
    }

    private fun setTempValueAndTime(sensorType: SensorType, tempTime: String, value: Double) {
        when (sensorType) {
            SensorType.CHEST -> setTempIfAttached(
                CommonDataArea.hrAlarmDao.isAttached,
                tempTime,
                value,
                sensorType
            )

            SensorType.LIMB -> setTempIfAttached(
                CommonDataArea.spo2AlarmDao.isAttached,
                tempTime,
                value,
                sensorType
            )

            else -> Log.d("OnSetTempValueAndTime", "Unknown Sensor Type -> $sensorType")
        }
    }

    private fun setTempIfAttached(
        isAttached: Boolean,
        tempTime: String,
        value: Double,
        sensorType: SensorType,
    ) {
        val currentTypeText = if (isAttached) {
            CommonDataArea.temperature = value
            CommonDataArea.tempTime = tempTime
            sensorType.name
        } else {
            CommonDataArea.temperature = Double.NaN
            Log.d("SibelAlarmManager", "setTempIfAttached: $isAttached and ${CommonDataArea.temperature}")
            String()
        }

        CommonDataArea.currentSensorWithTemperature = currentTypeText
        onVitalDataUpdate(StreamDataType.TEMP_SKIN, value)
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalCompanionText, StreamDataType.TEMP_SKIN)
    }

    private fun isSensorDetached(sensor: Sensor): Boolean {
        return when (sensor.sensorType) {
            SensorType.CHEST -> {
                val chestSensor = sensor as ChestSensor
                return chestSensor.isLeadOff!!
            }

            SensorType.LIMB -> {
                val limbSensor = sensor as LimbSensor
                return limbSensor.isPoorSkinContact!!
            }

            else -> false
        }
    }
}