package com.spacelabs.app.database.relationships

import androidx.room.Embedded
import androidx.room.Relation
import com.spacelabs.app.database.entities.ParametersTable
import com.spacelabs.app.database.entities.PatientAlarmTable
import com.spacelabs.app.database.entities.PatientTable

data class PatientAlarmWithPatientAndParameter(
    @Embedded
    val patientAlarm: PatientAlarmTable?,
    @Relation(
        parentColumn = "patientId",
        entityColumn = "patientId"
    )
    val patient: PatientTable?,
    @Relation(
        parentColumn = "paramId",
        entityColumn = "paramId"
    )
    val parameter: ParametersTable?
)