package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.ParametersTable

class ParameterAdapter(private var patientList: List<ParametersTable> = emptyList()) : RecyclerView.Adapter<ParameterAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_parameter, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: PatientViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val paramId: TextView = itemView.findViewById(R.id.param_id)
        private val paramName: TextView = itemView.findViewById(R.id.param_name)
        private val paramCategory: TextView = itemView.findViewById(R.id.param_category)
        private val paramType: TextView = itemView.findViewById(R.id.param_type)
        private val linkedSensorDef: TextView = itemView.findViewById(R.id.linked_sensor_def)



        fun bind(patient: ParametersTable) {
            paramId.text = patient.paramId.toString()
            paramName.text = patient.paramName
            paramCategory.text = patient.paramCategory
            paramType.text = patient.paramType.toString()
            linkedSensorDef.text = patient.linkedSensorDef.toString()

        }
    }
}

