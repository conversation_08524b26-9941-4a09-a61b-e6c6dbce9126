package com.spacelabs.app.database.relationships

import androidx.room.Embedded
import androidx.room.Relation
import com.spacelabs.app.database.entities.AlarmEvents2Table
import com.spacelabs.app.database.entities.AlarmsTable

data class AlarmWithAlarmEvents (
    @Embedded
    val alarm: AlarmsTable,
    @Relation(
        parentColumn = "alarmId",
        entityColumn = "alarmId"
    )
    val alarmEvents: List<AlarmEvents2Table>
)