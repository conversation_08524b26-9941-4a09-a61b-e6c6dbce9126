package com.spacelabs.app.activities.iomtActivities.activityUi

import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.spacelabs.app.MainActivity
import com.spacelabs.app.activities.commonActivities.activityUi.ActivityUiController
import com.spacelabs.app.activities.iomtActivities.QrScannerActivity
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.iomt.IomtApiManager


class QrCodeActivityUi(private val qrCodeActivity: QrScannerActivity) :
    ActivityUiController(qrCodeActivity) {

    override fun initUi() {
        TODO("Not yet implemented")
    }

    override fun postInitUiActions() {
        TODO("Not yet implemented")
    }

    override fun uiActionListeners() {
        TODO("Not yet implemented")
    }
    inner class UiEventHandler : UiEventCallback {
        @RequiresApi(Build.VERSION_CODES.P)
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            when (event) {
                UiEventCallback.UiEventType.NavigateToNext -> {
                    navigateToNextActivity(
                        //MainActivity::class.java
                        if (enableSnsApi) {
                            if (SnsApiManager.ACCOUNT_ID.isEmpty()) QrScannerActivity::class.java else MainActivity::class.java
                        } else {
                            if (IomtApiManager.ACCOUNT_ID.isEmpty()) QrScannerActivity::class.java else MainActivity::class.java
                        }
                    )
                }
                else -> Log.d("UiEvent", "QRCODE_UNKNOWN EVENT -> $event")
            }
        }
    }

}
