package com.spacelabs.app.common

import android.app.Activity
import android.util.Log
import com.spacelabs.app.MainActivity
import com.spacelabs.app.activities.iomtActivities.AccountConfigActivity
import com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
import com.spacelabs.app.activities.iomtActivities.QrScannerActivity
import com.spacelabs.app.activities.commonActivities.WifiConfigActivity
import com.spacelabs.app.activities.fragments.ManageSensorsFragment
import com.spacelabs.app.activities.fragments.TrendGraphFragment
import com.spacelabs.app.activities.fragments.childfragments.BpSettingsFragment
import com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.interfaces.AlarmEventCallback
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.interfaces.UiEventCallback

class CommonEventHandler {

    companion object{
        private var wifiConfigEvents: UiEventCallback? = null
        private var accountConfigEvents: UiEventCallback? = null
        private var snsAccountConfigEvents: UiEventCallback? = null
        private var snsqrcodeConfigEvents: UiEventCallback? = null
        private var mainActivityEvents: UiEventCallback? = null
        private var qrcodeConfigEvents: UiEventCallback? = null
        private var trendGraphFragmentEvents: UiEventCallback? = null
        private var manageSensorsFragmentEvents: UiEventCallback? = null
        private var bpSettingsFragmentEvents: UiEventCallback? = null

        private var alarmEvent: AlarmEventCallback? = null

        private var mainActivityIoEvents: IOEventCallback? = null

        fun registerUiEventCallback(callback: UiEventCallback, activity: Class<*>){
            setCallback(callback, activity)
            //Log.d("COMMON_EVENT_HANDLER", "callback: $callback activity: $activity")
        }

        fun unRegisterUiEventCallback(activity: Class<*>){
            setCallback(null, activity)
        }

        fun <T: Activity> registerAlarmEventCallback(callback: AlarmEventCallback, activity: Class<T>) {
            setAlarmCallback(callback, activity)
        }

        fun <T: Activity> unRegisterAlarmEventCallback(activity: Class<T>){
            setAlarmCallback(null, activity)
        }

        fun <T: Activity> registerIOEventCallback(callback: IOEventCallback, activity: Class<T>) {
            setIOCallback(callback, activity)
        }

        fun <T: Activity> unregisterIOEventCallback(activity: Class<T>) {
            setIOCallback(null, activity)
        }

        fun postUiEvent(eventType: UiEventCallback.UiEventType, eventData: Any?){
            wifiConfigEvents?.uiEvent(eventType, eventData)
            accountConfigEvents?.uiEvent(eventType, eventData)
            snsAccountConfigEvents?.uiEvent(eventType, eventData)
            mainActivityEvents?.uiEvent(eventType, eventData)
            qrcodeConfigEvents?.uiEvent(eventType, eventData)
            snsqrcodeConfigEvents?.uiEvent(eventType, eventData)
            trendGraphFragmentEvents?.uiEvent(eventType, eventData)
            manageSensorsFragmentEvents?.uiEvent(eventType, eventData)
            bpSettingsFragmentEvents?.uiEvent(eventType, eventData)
           // Log.d("COMMON_EVENT_HANDLER", "postUiEvent: ${eventType} eventData :${eventData}")
        }

        fun postAlarmEvent(param: CurrentAlarmParameter, value: Double?) {
            val data = value ?: 0.0
            alarmEvent?.alarmEvent(param, data)
        }

        fun postIOEvent(eventType: IOEventCallback.IOEventType, eventData: Any?) {
            mainActivityIoEvents?.ioEvent(eventType, eventData)
        }

        private fun setCallback(callback: UiEventCallback?, activity: Class<*>){
            when(activity){
                WifiConfigActivity::class.java -> wifiConfigEvents = callback
                AccountConfigActivity::class.java -> accountConfigEvents = callback
                AccountConfigActivitySns::class.java -> snsAccountConfigEvents = callback
                MainActivity::class.java -> mainActivityEvents = callback
                QrScannerActivity::class.java -> qrcodeConfigEvents = callback
                QrScannerActivitySns::class.java -> snsqrcodeConfigEvents = callback
                TrendGraphFragment::class.java -> trendGraphFragmentEvents = callback
                ManageSensorsFragment::class.java -> manageSensorsFragmentEvents = callback
                BpSettingsFragment::class.java -> bpSettingsFragmentEvents = callback
            }
           // Log.d("COMMON_EVENT_HANDLER", "activity: ${activity} eventData :${callback}")

        }

        private fun <T: Activity> setAlarmCallback(callback: AlarmEventCallback?, activity: Class<T>) {
            when(activity) {
                MainActivity::class.java -> alarmEvent = callback
                else -> return
            }
        }

        private fun <T: Activity> setIOCallback(callback: IOEventCallback?, activity: Class<T>) {
            when (activity) {
                MainActivity::class.java -> mainActivityIoEvents = callback
                else -> return
            }
        }
    }
}