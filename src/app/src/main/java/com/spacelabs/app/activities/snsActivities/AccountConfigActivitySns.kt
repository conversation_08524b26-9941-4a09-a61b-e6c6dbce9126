package com.spacelabs.app.activities.snsActivities

import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.transition.Slide
import android.util.Log
import android.view.Window
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.spacelabs.app.R
import com.spacelabs.app.activities.snsActivities.activityUi.AccountConfigActivityUiSns
import com.spacelabs.app.activities.snsActivities.helpers.AccountConfigActivityHelperSns
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.SettingDaoHelper

class AccountConfigActivitySns: AppCompatActivity() {

    private val accountActivityUi = AccountConfigActivityUiSns(this)
    val accountConfigHelper = AccountConfigActivityHelperSns(this)
    private var qrCodeDisplayed = false
    private var activityResumed = false
    @RequiresApi(Build.VERSION_CODES.R)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setWindowProperties()
        setContentView(R.layout.account_config_activity)

        Log.d("SNS_NAVIGATION_DEBUG", "onCreate: Setting up activity UI")
        accountActivityUi.setUpActivityUi()
        Log.d("SNS_NAVIGATION_DEBUG", "onCreate: Activity UI setup completed")
    }

    override fun onStart() {
        super.onStart()
        CommonEventHandler.registerUiEventCallback(
            accountActivityUi.UiEventHandler(),
            this::class.java
        )
    }

    override fun onResume() {
        super.onResume()
        activityResumed = true
        Log.d("SNS_NAVIGATION_DEBUG", "onResume: Called")
        Log.d("SNS_NAVIGATION_DEBUG", "onResume: HasAuthenticated value = '${SettingDaoHelper.AppSettings.HasAuthenticated.getValue()}'")
        Log.d("SNS_NAVIGATION_DEBUG", "onResume: AccountId value = '${SettingDaoHelper.AppSettings.AccountId.getValue()}'")
        Log.d("SNS_NAVIGATION_DEBUG", "onResume: QR code displayed = $qrCodeDisplayed")

        // If AccountId is empty, always show QR code regardless of HasAuthenticated status
        val accountId = SettingDaoHelper.AppSettings.AccountId.getValue()
        if (accountId.isEmpty()) {
            Log.d("SNS_NAVIGATION_DEBUG", "onResume: AccountId is empty, ensuring QR code is shown")
            // Only navigate if QR code has been displayed or if SNS API is disabled
            if (qrCodeDisplayed || !CommonDataArea.enableSnsApi) {
                Log.d("SNS_NAVIGATION_DEBUG", "onResume: Calling navigateIfAccountIdExists()")
                accountConfigHelper.navigateIfAccountIdExists()
            } else {
                Log.d("SNS_NAVIGATION_DEBUG", "onResume: Waiting for QR code to be displayed before navigation")
            }
        } else {
            Log.d("SNS_NAVIGATION_DEBUG", "onResume: AccountId exists, proceeding to navigation")
            accountConfigHelper.navigateIfAccountIdExists()
        }
    }

    override fun onPause() {
        super.onPause()
        activityResumed = false
    }

    fun onQrCodeDisplayed() {
        Log.d("SNS_NAVIGATION_DEBUG", "onQrCodeDisplayed: QR code has been displayed")
        qrCodeDisplayed = true

        // Give users time to scan the QR code before allowing navigation
        Handler(Looper.getMainLooper()).postDelayed({
            Log.d("SNS_NAVIGATION_DEBUG", "onQrCodeDisplayed: Delay completed, checking navigation")
            if (activityResumed) {
                Log.d("SNS_NAVIGATION_DEBUG", "onQrCodeDisplayed: Activity is resumed, checking navigation")
                accountConfigHelper.navigateIfAccountIdExists()
            }
        }, 3000) // 3 second delay to allow QR code scanning
    }

    override fun onStop() {
        super.onStop()
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
    }


    @RequiresApi(Build.VERSION_CODES.R)
    private fun setWindowProperties(){
        with(window){
            requestFeature(Window.FEATURE_ACTIVITY_TRANSITIONS)

            enterTransition = Slide()
            exitTransition = android.transition.Fade()
        }

        window.decorView.windowInsetsController!!.hide(
            android.view.WindowInsets.Type.statusBars()
        )
    }
}