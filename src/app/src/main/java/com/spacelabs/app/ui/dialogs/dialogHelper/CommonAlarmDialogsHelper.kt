package com.spacelabs.app.ui.dialogs.dialogHelper

import android.app.Dialog
import android.content.Context
import android.content.SharedPreferences
import android.graphics.drawable.Drawable
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.Spannable
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import com.spacelabs.app.R
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.ui.activityUIHelpers.MainActivityBottomBarUi
import com.spacelabs.app.ui.dialogs.CommonAlarmsDialogController
import kotlinx.coroutines.launch
import java.util.*

class CommonAlarmDialogsHelper(alert: CommonAlarmsDialogController.Alerts, context: Context) {

    private val alert: CommonAlarmsDialogController.Alerts

    private lateinit var countDownTimer: CountDownTimer

    private var isTimerRunning = false
    private var startTime: Long = 0
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var timerRunnable: Runnable
    private val sharedPreferences: SharedPreferences

    lateinit var alertTitle: String
    lateinit var btnText: String
    lateinit var imgSrc: Drawable

    init {
        this.alert = alert
        sharedPreferences = context.getSharedPreferences("settings", Context.MODE_PRIVATE)
        initParameters(alert, context)
    }

    private fun initParameters(alert: CommonAlarmsDialogController.Alerts, context: Context){
        when(alert){
            CommonAlarmsDialogController.Alerts.TurnTimer -> {
                alertTitle = "Turn Due"
                imgSrc = ResourcesCompat.getDrawable(context.resources, R.drawable.ic_arrows_turn, context.theme)!!
                btnText = "Reset"
            }
            CommonAlarmsDialogController.Alerts.Fall -> {
                alertTitle = "Patient has fallen"
                imgSrc = ResourcesCompat.getDrawable(context.resources, R.drawable.ic_person_fall, context.theme)!!
                btnText = "Acknowledge"
            }
            CommonAlarmsDialogController.Alerts.PauseAlarm ->{
                alertTitle = "All Alarms Paused"
                imgSrc = ResourcesCompat.getDrawable(context.resources, R.drawable.pause, context.theme)!!
                btnText = "Resume"
            }
        }
    }

    fun onAcknowledgeBtnClick(dialog: Dialog){
        when(alert){
            CommonAlarmsDialogController.Alerts.PauseAlarm -> onCancelCountDownTimer(dialog)
            CommonAlarmsDialogController.Alerts.Fall -> onFallAcknowledged(dialog)
            CommonAlarmsDialogController.Alerts.TurnTimer -> onUpdateTurnTime(dialog)
        }
    }

    fun updateAlarmTextView(textView: TextView, dialog: Dialog){
        when(alert){
            CommonAlarmsDialogController.Alerts.PauseAlarm -> updatePauseAlarmText(textView, dialog)
            CommonAlarmsDialogController.Alerts.Fall -> startTimer(textView, "Past time", "")
            CommonAlarmsDialogController.Alerts.TurnTimer -> {
                MainActivityBottomBarUi.positionCardColorTheme = R.color.red
                startTimer(textView, "Turn Past time", "")
            }
        }
    }

    private fun updatePauseAlarmText(textView: TextView, dialog: Dialog): CountDownTimer{
        countDownTimer = object : CountDownTimer(120000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                setAllAlarmAcknowledged(true)
                val minutes = ((millisUntilFinished / 1000) / 60).toInt()
                val seconds = ((millisUntilFinished / 1000) % 60).toInt()
                val timeLeftFormatted = String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds)
                textView.text = Html.fromHtml(
                    "Resuming Alarms in <strong>$timeLeftFormatted</strong>",
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            override fun onFinish() {
                dialog.dismiss()
                setAllAlarmAcknowledged(false)
            }

        }.start()
        return countDownTimer
    }

    private fun setAllAlarmAcknowledged(status: Boolean){
        CommonDataArea.hrAlarmDao.isAcknowledged = status
        CommonDataArea.respAlarmDao.isAcknowledged = status
        CommonDataArea.spo2AlarmDao.isAcknowledged = status
        CommonDataArea.tempAlarmDao.isAcknowledged = status
        CommonDataArea.bpSysAlarmDao.isAcknowledged = status
        CommonDataArea.bpDiaAlarmDao.isAcknowledged = status
        CommonDataArea.connectivityAlarmDao.isAcknowledged = status
    }

    private fun onCancelCountDownTimer(dialog: Dialog){
        countDownTimer.cancel()
        setAllAlarmAcknowledged(false)
        dialog.dismiss()
    }

    private fun onFallAcknowledged(dialog: Dialog){
        stopTimer()
        CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.HasFallen, true)
        dialog.dismiss()
    }
    private fun onUpdateTurnTime(dialog: Dialog) {
        CommonDataArea.isTurnDialogShowing = false
        stopTimer()
        CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.TriggerTurnDue, false)
        dialog.dismiss()
    }

    private fun startTimer(textView: TextView, postText: String,  eventTime: String) {
        isTimerRunning = true
        startTime = TimestampUtils.getCurrentTimeMillis()
        timerRunnable = Runnable {
            val millis: Long = TimestampUtils.getCurrentTimeMillis() - startTime
            var seconds = (millis / 1000).toInt()
            var minutes = seconds / 60
            val hours = minutes / 60
            seconds %= 60
            minutes %= 60
            val time = String.format("%02d:%02d:%02d", hours, minutes, seconds)
            textView.text = Html.fromHtml("<strong>$time</strong> $postText", Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            if (isTimerRunning) {
                handler.postDelayed(timerRunnable, 1000)
            }
        }
        handler.postDelayed(timerRunnable, 0)
    }

    private fun stopTimer() {
        isTimerRunning = false
        handler.removeCallbacks(timerRunnable)
    }

}