package com.spacelabs.app.alarms.alarmDAO

import com.spacelabs.app.R
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.alarms.SibelAlarmManager
import com.spacelabs.app.common.CommonDataArea

class AlarmParametersDaoHelper(alarmParametersDao: AlarmParametersDao) {

    private val alarmParamDao: AlarmParametersDao

    init {
        alarmParamDao = alarmParametersDao
    }

    private val criticalAlarmColor = R.color.red
    private val highAlarmColor = R.color.orange
    private val mediumAlarmColor = R.color.mediumYellow
    private val lowAlarmColor = R.color.lowBlue

    private val backgroundColor = R.color.gray
    private val whiteText = R.color.white
    private val blackText = R.color.black

    private fun setCriticality(alarmCriticality: Int){
        alarmParamDao.alarmCriticality = alarmCriticality
        onSetAlarmCriticality(alarmCriticality)
    }

    fun onSetCurrentAlarmProperty(currentAlarm: CurrentAlarmParameter){
        when(currentAlarm){
            CurrentAlarmParameter.HighExtremeAlarm -> setAlarmProperty("${alarmParamDao.paramName} > ${alarmParamDao.extremeHighValue.toInt()}", "EXTREME HIGH ${alarmParamDao.paramName}", SibelAlarmManager.CRITICAL)
            CurrentAlarmParameter.LowExtremeAlarm -> {
                alarmParamDao.alarmText = "HR < ${alarmParamDao.extremeLowValue.toInt()}"
                alarmParamDao.alarmPrefix = "EXTREME LOW ${alarmParamDao.paramName}"
                setCriticality(SibelAlarmManager.CRITICAL)
            }
            CurrentAlarmParameter.HighHighAlarm -> setAlarmProperty("${alarmParamDao.paramName} > ${alarmParamDao.highValue.toInt()}", "HIGH ${alarmParamDao.paramName}", SibelAlarmManager.HIGH)
            CurrentAlarmParameter.LowHighAlarm -> setAlarmProperty("${alarmParamDao.paramName} < ${alarmParamDao.lowValue.toInt()}", "LOW ${alarmParamDao.paramName}", SibelAlarmManager.HIGH)

            CurrentAlarmParameter.HighMediumAlarm -> setAlarmProperty("${alarmParamDao.paramName} > ${alarmParamDao.highValue.toInt()}", "HIGH ${alarmParamDao.paramName}", SibelAlarmManager.MEDIUM)
            CurrentAlarmParameter.LowMediumAlarm -> setAlarmProperty("${alarmParamDao.paramName} < ${alarmParamDao.lowValue.toInt()}", "LOW ${alarmParamDao.paramName}", SibelAlarmManager.MEDIUM)

            CurrentAlarmParameter.HighLowAlarm -> setAlarmProperty("${alarmParamDao.paramName} > ${alarmParamDao.highValue.toInt()}", "HIGH ${alarmParamDao.paramName}", SibelAlarmManager.LOW)
            CurrentAlarmParameter.LowLowAlarm -> setAlarmProperty("${alarmParamDao.paramName} < ${alarmParamDao.lowValue.toInt()}", "LOW ${alarmParamDao.paramName}", SibelAlarmManager.LOW)

            CurrentAlarmParameter.HighAlarm -> setCriticality(SibelAlarmManager.HIGH)
            CurrentAlarmParameter.LowAlarm, CurrentAlarmParameter.ConnectivityAlarm,CurrentAlarmParameter.CriticalBatteryAlarm,CurrentAlarmParameter.CriticalDeviceBatteryAlarm -> setCriticality(SibelAlarmManager.LOW)
            else -> {
                alarmParamDao.isBlinking = false
                alarmParamDao.isAcknowledged = false
                alarmParamDao.isAcknowledgeTimerRunning = false
                alarmParamDao.alarmBgColor = backgroundColor
                alarmParamDao.alarmTextColor = whiteText
                alarmParamDao.acknowledgeTimer?.cancel()
            }
        }
    }

    private fun onSetAlarmCriticality(alarmCriticality: Int){
        alarmParamDao.isBlinking = true
        when(alarmCriticality){
            SibelAlarmManager.CRITICAL -> {
                alarmParamDao.alarmBgColor = criticalAlarmColor
                alarmParamDao.alarmTextColor = whiteText
                alarmParamDao.alarmTone = R.raw.critical_alarm
                alarmParamDao.alarmInterval = 1
            }
            SibelAlarmManager.HIGH -> {
                alarmParamDao.alarmBgColor = highAlarmColor
                alarmParamDao.alarmTextColor = whiteText
                alarmParamDao.alarmTone = R.raw.high_alarm
                alarmParamDao.alarmInterval = 3
            }
            SibelAlarmManager.MEDIUM -> {
                alarmParamDao.alarmBgColor = mediumAlarmColor
                alarmParamDao.alarmTextColor = blackText
                alarmParamDao.alarmTone = R.raw.medium_alarm
                alarmParamDao.alarmInterval = 8
            }
            SibelAlarmManager.LOW -> {
                alarmParamDao.alarmBgColor = lowAlarmColor
                alarmParamDao.alarmTextColor = whiteText
                alarmParamDao.alarmTone = R.raw.low_alarm
                alarmParamDao.alarmInterval = 15
            }
        }
    }

    private fun setAlarmProperty(alarmText: String?, alarmPrefix: String, priority: Int){
        if(alarmText != null)
            alarmParamDao.alarmText = alarmText

        alarmParamDao.alarmPrefix = alarmPrefix
        setCriticality(priority)
    }

}