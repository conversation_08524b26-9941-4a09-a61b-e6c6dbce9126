package com.spacelabs.app.ui.dialogs.dialogHelper

import android.graphics.drawable.InsetDrawable
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatCheckedTextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonDataArea.Companion.sensorTypeList
import com.spacelabs.app.ui.dialogs.CommonNotificationDialogs
import com.spacelabs.app.ui.dialogs.SensorListDialogManager

class SensorRecyclerViewAdapter(private var items: ArrayList<SensorListItems>, private val dialogManager: SensorListDialogManager): RecyclerView.Adapter<SensorRecyclerViewAdapter.ViewHolder>() {

    private var isLimbSensorSelected = false
    private var isChestSensorSelected = false
    private var isBpSensorSelected = false



    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.sensor_list_row, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(items[position])
    }

    override fun getItemCount() = items.size
    fun filterList(filteredList: List<SensorListItems>) {
        items = filteredList as ArrayList<SensorListItems>
        notifyDataSetChanged()
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val currentActivity = CommonDataArea.curActivity
        private val sensorIcon: ImageView = itemView.findViewById(R.id.sensorIcon)
        private val sensorName: AppCompatCheckedTextView = itemView.findViewById(R.id.sensorName)
        private  val commonNotification=CommonNotificationDialogs(currentActivity!!)

        private fun onItemClickedAction() {
            sensorName.isChecked = !sensorName.isChecked
            val sensor = items[absoluteAdapterPosition].getSensor()
            if (sensorName.isChecked) {
                onCheckedAction(sensor)

                dialogManager.updateSaveButtonColor()

            }
            else {
                CommonDataArea.isAlertShowing = false
                onUnCheckedAction(sensor)
                dialogManager.updateSaveButtonColor()
            }
        }

        init {
            sensorName.setOnClickListener {
                onItemClickedAction()
            }

            sensorIcon.setOnClickListener {
                onItemClickedAction()
            }
        }

        fun bind(sensorListItem: SensorListItems) {
            val selectedSensorName = CommonDataArea.SelectedSensorName
            if (sensorListItem.getSensorName().contains(selectedSensorName, ignoreCase = true)) {
                sensorIcon.setImageResource(sensorListItem.getIcon())
                sensorName.text = sensorListItem.getSensorName()
                sensorTypeList=sensorListItem.getSensorName()
                val drawableBatteryIcon = AppCompatResources.getDrawable(itemView.context, sensorListItem.getBatteryIcon())
                drawableBatteryIcon?.let {
                    val drawableWithMargin = InsetDrawable(it, 0, 0, 20, 0)
                    sensorName.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, drawableWithMargin, null)
                }
            } else {
                // Hide the item if it doesn't match the selected sensor name
                itemView.visibility = View.GONE
                val layoutParams = itemView.layoutParams as RecyclerView.LayoutParams
                layoutParams.height = 0
                layoutParams.width = 0
                itemView.layoutParams = layoutParams
            }
        }



        private fun onCheckedAction(sensor: Sensor?) {
            if (sensor != null) {
                when (sensor.sensorType) {
                    SensorType.CHEST -> {
                        if (!isChestSensorSelected) {
                            CommonDataArea.chestSensorSelected = sensor.name
                            CommonDataArea.chestSensorAddress=sensor.address
                            isChestSensorSelected = sensorName.isChecked
                            commonNotification.showChestAlert(currentActivity!!.getString(R.string.sensor_selection), currentActivity.getString(R.string.sensor_selection_message),null,
                                sensor.name,5000)

                        } else {
                            sensorName.isChecked = false
                            Toast.makeText(itemView.context, "You Already selected a CHEST Sensor", Toast.LENGTH_SHORT).show()
                        }
                    }
                    SensorType.LIMB -> {
                        if (!isLimbSensorSelected) {
                            CommonDataArea.limbSensorSelected = sensor.name
                            CommonDataArea.limbSensorAddress=sensor.address
                            isLimbSensorSelected = sensorName.isChecked
                            commonNotification.showLimbAlert(currentActivity!!.getString(R.string.sensor_selection), currentActivity.getString(R.string.sensor_selection_message),null,
                                sensor.name, 5000)

                        } else {
                            sensorName.isChecked = false
                            Toast.makeText(itemView.context, "You Already selected a LIMB Sensor", Toast.LENGTH_SHORT).show()
                        }
                    }
                    SensorType.BP2_BLOOD_PRESSURE_MONITOR -> {
                        if (!isBpSensorSelected) {
                            CommonDataArea.bpSensorSelected = sensor.name
                            CommonDataArea.bpSensorAddress=sensor.address
                            isBpSensorSelected = sensorName.isChecked
                            commonNotification.showBpAlert(currentActivity!!.getString(R.string.sensor_selection), currentActivity.getString(R.string.sensor_selection_message),currentActivity.getString(R.string.bp_additional_message),
                                sensor.name, 5000)

                        } else {
                            sensorName.isChecked = false
                            Toast.makeText(itemView.context, "You Already selected a BP Sensor", Toast.LENGTH_SHORT).show()
                        }
                    }
                    else -> {}
                }
            }
        }

        private fun pairSensor(sensor: Sensor) {

        }

        private fun onUnCheckedAction(sensor: Sensor?) {
            if (sensor != null) {
                when (sensor.sensorType) {
                    SensorType.CHEST -> {
                        isChestSensorSelected = false
                        CommonDataArea.chestSensorSelected=""
                    }
                    SensorType.LIMB ->{
                        isLimbSensorSelected = false
                        CommonDataArea.limbSensorSelected=""
                    }
                    SensorType.BP2_BLOOD_PRESSURE_MONITOR ->{
                        isBpSensorSelected = false
                        CommonDataArea.bpSensorSelected=""
                    }
                    else -> {}
                }

            }
        }
    }
}