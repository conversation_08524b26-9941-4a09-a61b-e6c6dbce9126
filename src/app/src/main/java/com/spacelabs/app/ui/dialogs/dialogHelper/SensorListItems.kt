package com.spacelabs.app.ui.dialogs.dialogHelper

import com.spacelabs.app.R
import com.sibelhealth.core.sensor.Sensor

class SensorListItems {
    private var icon = 0
    private var sensorName = ""
    private var sensor: Sensor? = null
    private var battery = 0.0
    private var batteryIcon = 0

    fun setIcon(icon: Int){
        this.icon = icon
    }

    fun getIcon(): Int{
        return icon
    }

    fun setSensorName(sensorName: String){
        this.sensorName = sensorName
    }

    fun getSensorName(): String{
        return sensorName
    }

    fun setSensor(sensor: Sensor){
        this.sensor = sensor
    }

    fun getSensor(): Sensor?{
        return sensor
    }

    fun setBattery(battery: Double){
        this.battery = battery
        setBatteryIcon(getBatteryIconByBatteryLevel(battery))
    }

    fun getBattery(): Double{
        return battery
    }

    private fun setBatteryIcon(batteryIcon: Int){
        this.batteryIcon = batteryIcon
    }

    fun getBatteryIcon(): Int{
        return batteryIcon
    }

    private fun getBatteryIconByBatteryLevel(batteryLevel: Double): Int{
        return when(batteryLevel){
            in 0.5..1.0 -> R.drawable.ic_battery_full_solid
            in 0.1..0.5 -> R.drawable.ic_battery_intermediate_solid
            in 0.0..0.1 -> R.drawable.ic_battery_verylow_solid
            else -> R.drawable.ic_battery_unknown
        }
    }
}