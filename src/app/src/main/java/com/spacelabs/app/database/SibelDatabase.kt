package com.spacelabs.app.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.spacelabs.app.database.dao.*
import com.spacelabs.app.database.entities.*
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SupportFactory
import com.spacelabs.app.iomt.data.Converters
import java.io.File

@Database(
    entities = [
        PatientTable::class,
        ParametersTable::class,
        DefaultAlarmSettingsTable::class,
        PatientAlarmTable::class,
        SensorDefTable::class,
        SensorTable::class,
        VisitTable::class,
        MeasurementDataTable::class,
        EventListTable::class,
        EventsLogTable::class,
        SettingsTable::class,
        AlarmsTable::class,
        AlarmEvents2Table::class
    ],
    version = 20,
    exportSchema = false
)
@TypeConverters(Converters::class)

abstract class SibelDatabase : RoomDatabase() {
    abstract val patientDao: PatientDao
    abstract val parametersDao: ParametersDao
    abstract val alarmDao: AlarmDao
    abstract val measurementDao: MeasurementDataDao
    abstract val eventsDao: EventsDao
    abstract val sensorDao: SensorDao
    abstract val settingsDao: SettingDao
    abstract val visitDao: VisitDao


    companion object {

        @Volatile
        private var INSTANCE: SibelDatabase? = null

        fun getInstance(context: Context, passphrase: String): SibelDatabase {
            synchronized(this) {
                return INSTANCE ?: Room.databaseBuilder(
                    context.applicationContext,
                    SibelDatabase::class.java,
                    "dbSibelPatch"
                ).openHelperFactory(SupportFactory(SQLiteDatabase.getBytes(passphrase.toCharArray())))
                    .setJournalMode(JournalMode.WRITE_AHEAD_LOGGING)
                    .build().also {
                        INSTANCE = it
                        it.openHelper.writableDatabase.execSQL("PRAGMA synchronous = OFF;")
                    }
            }
        }
    }

    fun rotatePassphrase(context: Context, currentPassphrase: String, newPassphrase: String) {
        val existingDB = getInstance(context, currentPassphrase)
        val backupFile = File(context.getDatabasePath("dbSibelPatch").path + ".bak")
        existingDB.close()
        INSTANCE?.close()
        File(context.getDatabasePath("dbSibelPatch").path).copyTo(backupFile, true)
        getInstance(context, newPassphrase)
        backupFile.delete()
    }
}