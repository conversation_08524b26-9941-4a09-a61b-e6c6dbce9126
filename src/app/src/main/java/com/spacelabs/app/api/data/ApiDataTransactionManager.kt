package com.spacelabs.app.api.data

import android.util.Log
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.spacelabs.app.api.ApiPostObservations
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.api.data.observations.ObservationManager
import com.spacelabs.app.database.objectbox.encryption.EncryptionConverter
import com.spacelabs.app.database.objectbox.encryption.EncryptionVitalConverter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.objectbox.helpers.MeasurementDataDaoHelper
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.dao.AlarmBoxDao
import com.spacelabs.app.database.objectbox.helpers.AlarmBoxDaoHelper
import com.spacelabs.app.iomt.CmsClient
import com.spacelabs.app.iomt.data.CmsLiveDataTransactionManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.nio.ByteBuffer
import java.util.AbstractMap.SimpleEntry
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.CoroutineContext

class ApiDataTransactionManager {

    private val postObservations = ApiPostObservations()
    private val encryptionVitalConverter = EncryptionVitalConverter()

    companion object {
        private val postDataJob = Job()
        private val postDataCoroutine = CoroutineScope(Dispatchers.IO + postDataJob)
        var historyJob: CoroutineContext? = null

        //private lateinit var measurementDaoHelper: MeasurementDataDaoHelper
        private var measurementDaoHelper: MeasurementDataDaoHelper? = null
        private lateinit var alarmBoxDaoHelper: AlarmBoxDaoHelper

        var uuIdAndData: ConcurrentHashMap<String, SimpleEntry<Any, Class<*>>> = ConcurrentHashMap()

        private var pendingMeasurements: List<MeasurementData>? = null

    }
    private val encryptionConverter = EncryptionConverter()

    fun checkDbAndUploadPendingDataToServer(){
        alarmBoxDaoHelper = AlarmBoxDaoHelper()
        /*measurementDaoHelper = MeasurementDataDaoHelper()
        checkDbAndUploadPendingMeasurementToServer()*/
        checkAndUploadPendingAlarm()
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun checkAndUploadPendingAlarm() {
        GlobalScope.launch {
            while (true){
                if(SnsApiManager.webSocketClient == null || !SnsApiManager.webSocketClient!!.isOpen)
                    continue

                delay(1000)
                val pendingAlarmsAndEventsList = alarmBoxDaoHelper.getPendingAlarmsToSend()
                if(pendingAlarmsAndEventsList.isNotEmpty()){
                    for(alarmWithEvents in pendingAlarmsAndEventsList){
                        postAlarmsToSns(alarmWithEvents)
                    }
                }
            }
        }
    }

 /*   @OptIn(DelicateCoroutinesApi::class)
    private fun checkDbAndUploadPendingMeasurementToServer() {
        GlobalScope.launch {
            try {
                while (true) {
                    if(SnsApiManager.webSocketClient == null || SnsApiManager.webSocketClient?.isClosed!!) {
                        delay(1000)
                        continue
                    }

                    val pendingMeasurements = measurementDaoHelper?.getMeasurementListToSend().takeIf { it?.isNotEmpty()!! }
                        ?: getSendDataOfTypeWithStatus1(MeasurementData::class.java).map { it as MeasurementData }

                    if (pendingMeasurements.isEmpty()) {
                        delay(1000)
                        continue
                    }

                    pendingMeasurements.forEach { measure ->
                        measure.measurementData?.let { uploadReceivedWaveData(measure) } ?: uploadReceivedVitals(measure)
                    }
                    // Mark measurement as uploading -- for the time being mark it as uploaded
                }
            } catch (ex: Exception) {
                Log.d("checkDbAndUploadPendingMeasurementToServer", ex.stackTraceToString())
            }
        }
    }
*/
    fun uploadHistoricalData() {
      historyJob =postDataCoroutine.launch {
            if (SnsApiManager.webSocketClient?.isClosed == true)
                return@launch

            val startTime = TimestampUtils.getCurrentTimeMillis() - 30000
            measurementDaoHelper = getMeasurementDaoHelper()
            pendingMeasurements = measurementDaoHelper?.getLastNNumberDataByTimeMillis(startTime, 1000)

            if (!pendingMeasurements.isNullOrEmpty()) {
                val (waveforms, vitals) = pendingMeasurements!!.partition { it.measurementData != null }
                uploadReceivedWaveData(waveforms)
                uploadReceivedVitals(vitals)
                if(historyJob?.isActive == true)
                    uploadHistoricalData()
            } else {
                Log.d(
                    "UploadHistoricalData",
                    "pendingMeasurements Empty:${pendingMeasurements.isNullOrEmpty()}  ,History Job : ${historyJob?.isActive}"
                )
                return@launch
            }
        }
    }


    fun uploadLiveMeasurementData(measurement: MeasurementData, measurementData: ByteArray?) {
        try {
            if (SnsApiManager.webSocketClient == null || SnsApiManager.webSocketClient?.isClosed!!)
                return
            else {
                if (measurement.measurementData != null)
                /* parseAndUploadWaveData(measurement, measurementData)*/
                    uploadReceivedWaveData(listOf(measurement))

              //  parseAndUploadFhirWaveData(measurement, measurementData)
                else
                    uploadReceivedVitals(listOf(measurement))
            }
            //Log.d("uploadLiveMeasurementData", "${measurement.measurementData?.isEmpty() ?: "Not_Empty"}: ")
        } catch (ex: Exception) {
            Log.d("checkDbAndUploadPendingMeasurementToServer", ex.stackTraceToString())
        }
    }

    private fun getMeasurementDaoHelper(): MeasurementDataDaoHelper? {
        if (measurementDaoHelper == null)
           measurementDaoHelper = MeasurementDataDaoHelper()

        return measurementDaoHelper
    }


    private fun getSendDataOfTypeWithStatus1(type: Class<*>): List<Any> {
        return uuIdAndData.values
            .filter { it.value == type }
            .map { it.key as MeasurementData }
            .filter { it.tickValue!! < TimestampUtils.getCurrentTimeMillis() - 2000 }
    }

  /*  private fun uploadReceivedVitals(measurement: MeasurementData?) {
        measurement ?: return

        val uuId = measurement.measurementUuid
        when(measurement.paramName){
            StreamDataType.BP.toString() -> postNIBPToSns(measurement, uuId)
            else -> postVitalsToSns(measurement, uuId)
        }
        uuIdAndData[uuId] = SimpleEntry<Any, Class<*>>(measurement, MeasurementData::class.java)

        uuIdAndData[uuId]?.let {
            measurementDaoHelper?.updateMeasurementUploadStatusByUuid(it.key, 1)
        }
    }

    private fun uploadReceivedWaveData(measurement: MeasurementData?) {
        measurement ?: return


        val byteArray = measurement.measurementData?.let { encryptionConverter.convertToEntityProperty(it) }
        if (byteArray != null) {
            val byteBuffer = ByteBuffer.wrap(byteArray)
            val sampleArray = DoubleArray(byteBuffer.remaining() / 8)
            for (i in sampleArray.indices) {
                sampleArray[i] = byteBuffer.double
            }

            val streamDataType = when(measurement.paramName){
                "ECG" -> StreamDataType.ECG
                "PPG" -> StreamDataType.PPG_IR
                "RESP" -> StreamDataType.RR_WAVEFORM
                else -> null
            }

            if (streamDataType != null && measurement.measurementId != null) {
                val uuId = measurement.measurementUuid
                uuIdAndData[uuId] = SimpleEntry<Any, Class<*>>(measurement, MeasurementData::class.java)
                postWaveStreamToSns(sampleArray, streamDataType, measurement.timestamp, uuId)
                runBlocking {
                    uuIdAndData[uuId]?.let {
                        measurementDaoHelper?.updateMeasurementUploadStatusByUuid(it.key, 1)
                    }
                }
            }
        }
    }
*/
    private fun uploadReceivedVitals(measurements: List<MeasurementData>?) {
        measurements ?: return

        for (measurement in measurements) {
            val uuId = measurement.measurementUuid
            when (measurement.paramName) {
                StreamDataType.BP.toString() -> postNIBPToSns(measurement, uuId)
                else -> postVitalsToSns(measurement, uuId)
            }
            uuIdAndData[uuId] = SimpleEntry<Any, Class<*>>(measurement, MeasurementData::class.java)

           /* uuIdAndData[uuId]?.let {
                measurementDaoHelper?.updateMeasurementUploadStatusByUuid(it.key, 1)
            }*/
        }
    }

    private fun uploadReceivedWaveData(measurements: List<MeasurementData>?) {
        measurements ?: return

        for (measurement in measurements) {
            val byteArray = measurement.measurementData?.let { encryptionConverter.convertToEntityProperty(it) }
            if (byteArray != null) {
                val byteBuffer = ByteBuffer.wrap(byteArray)
                val sampleArray = DoubleArray(byteBuffer.remaining() / 8)
                for (i in sampleArray.indices) {
                    sampleArray[i] = byteBuffer.double
                }

                val streamDataType = when (measurement.paramName) {
                    "ECG" -> StreamDataType.ECG
                    "PPG" -> StreamDataType.PPG_IR
                    "RESP" -> StreamDataType.RR_WAVEFORM
                    else -> null
                }

                if (streamDataType != null && measurement.measurementId != null) {
                    val uuId = measurement.measurementUuid
                    uuIdAndData[uuId] = SimpleEntry<Any, Class<*>>(measurement, MeasurementData::class.java)
                    postWaveStreamToSns(sampleArray, streamDataType, measurement.timestamp, uuId)
                   /* runBlocking {
                        uuIdAndData[uuId]?.let {
                            measurementDaoHelper?.updateMeasurementUploadStatusByUuid(it.key, 1)
                        }
                    }*/
                }
            }
        }
    }


    private fun postVitalsToSns(measurementData: MeasurementData, uuId: String){
        postDataCoroutine.launch {
            val triple = getApiParamTriple(measurementData.paramName, measurementData.sensorId)
            if(triple != null) {
                postObservations.sendVitalObservation(measurementData, triple, uuId)
            }
            else
                uuIdAndData.remove(uuId)
        }
    }

    private fun postWaveStreamToSns(samples: DoubleArray, type: StreamDataType, timestamp: String, uuId: String){
        postDataCoroutine.launch {
            val observationData = when(type){
                StreamDataType.ECG -> ApiPostObservations.ecgObservation
                StreamDataType.RR_WAVEFORM -> ApiPostObservations.respObservation
                StreamDataType.PPG_IR -> ApiPostObservations.ppgIrObservation
                else -> null
            }
//            Log.d("postWaveStreamToSns", "StreamDataType: $type" )
            if(observationData != null) {
                observationData.uuId = uuId
                postObservations.sendWaveformObservation(observationData, samples, timestamp)
            } else{
               // uuIdAndData.remove(uuId)
            }
        }
    }

    private fun postAlarmsToSns(alarmAndEvents: AlarmBoxDao.AlarmDataWithEvents){
        postObservations.sendAlarmObservation(alarmAndEvents)
    }

    private fun postNIBPToSns(vitalData: MeasurementData, uuId: String){
        val decryptedValue = encryptionVitalConverter.convertToEntityProperty(vitalData.value!!)
        val combinedBp: Long = decryptedValue!!.toLong()
        val sys: Int = getSys(combinedBp).toInt()
        val dia: Int = getDia(combinedBp).toInt()
        val bpSysAndDia = "$sys/$dia"
        val timestamp = vitalData.timestamp
        postDataCoroutine.launch {
            postObservations.sendNIBPObservation(bpSysAndDia, timestamp, uuId)
        }
    }

    private fun getSys(combinedValue: Long): Double {
        val factor = 1_000_000L
        val combinedX = combinedValue shr 32
        return (combinedX.toDouble() / factor)
    }

    private fun getDia(combinedValue: Long): Double {
        val factor = 1_000_000L
        val combinedY = combinedValue and 0xFFFFFFFFL
        return (combinedY.toDouble() / factor)
    }

    private fun getApiParamTriple(type: String, sensorId: Int?): Triple<String, String, String>?{
        sensorId ?: return null
       if(type!="STEP_COUNT") Log.d("getApiParamTriple", "${type}")

        return when(type.uppercase()){
            StreamDataType.HR.name -> ObservationManager.HR
            StreamDataType.RR.name -> ObservationManager.RR
            StreamDataType.SPO2.name -> ObservationManager.SpO2
            StreamDataType.TEMP_SKIN.name -> ObservationManager.getTempTriple(sensorId)
            "ANGLE" -> ObservationManager.ANGLE
            StreamDataType.BODY_POSITION.name -> ObservationManager.BODY_POSITION
            else -> null
        }
    }

    fun checkResponseAndUpdateDb(uuId: String, response: String){
        when(response){
            "Success creating vitals",
            "Success creating waveform entry" -> measurementDaoHelper?.updateMeasurementUploadStatusByUuId(uuId)
            "Success creating alarms" -> alarmBoxDaoHelper.updateAlarmEventUploadStatusByUuid(uuId = uuId, status = 2)
        }
        //Log.d("SNS_ApiPostObservations", "Observation Response: $response")
    }

    private fun updateUploadStatusBasedOnUuId(uuId: String){
        try{
            if(uuIdAndData.contains(uuId)){
                val dataAndType = uuIdAndData[uuId]
                when(dataAndType!!.value){
                    MeasurementData::class.java -> measurementDaoHelper?.updateMeasurementUploadStatusByUuid(dataAndType.key, 2)
                }
            }
        } catch(ex: Exception){
            Log.e("ExceptionOnStatusUpdate", ex.stackTraceToString())
        }
    }

    /*private fun saveToCsv(uuId: String, fileName: String) {

        val csvFile = File("${ Environment.getExternalStorageDirectory() }/SLHub/", "$fileName.csv")

        // val csvFile = File("path/to/your/csvfile.csv")  // Replace this with your actual path
        val fileWriter = FileWriter(csvFile, true)  // true for append mode
        fileWriter.append("$uuId\n")

        fileWriter.flush()
        fileWriter.close()
    }*/

}