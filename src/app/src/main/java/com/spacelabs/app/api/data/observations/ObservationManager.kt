package com.spacelabs.app.api.data.observations

import com.spacelabs.app.common.CommonDataArea

open class ObservationManager {

    //Triple(<loincCode>, <unit>, <dataDisplay>)
    protected val bp = Triple("85354-9", "mmHg", "Blood pressure panel with all children optional")
    protected val bpSys = Triple("150017", "mmHg", "MDC_PRESS_BLD_SYS")
    protected val bpDia = Triple("150018", "mmHg", "MDC_PRESS_BLD_DIA")

    protected val loincSysValue = "http://loinc.org"
    protected val hl7SysValue = "http://terminology.hl7.org/CodeSystem/observation-category"
    protected val hl7VitalSignUrl = "http://hl7.org/fhir/StructureDefinition/vitalsigns"

    companion object{
        //Triple(<loincCode>, <unit>, <dataDisplay>)
       /* val HR = Triple("8867-4", "beats/minute", "Heart rate")
        val RR = Triple("9279-1", "breaths/minute", "Respiratory rate")
        val SpO2 = Triple("2708-6", "%", "Oxygen saturation in Arterial blood")
        val TempForChest = Triple("8310-5", "degF", "Temperature")//MDC_TEMP_BODY("150364"),
        val ANGLE = Triple("8478-0", "°", "Mean")*//*MDC_AI_TYPE_BASE_COORD_ANGLE("8519760")*//*
        val BODY_POSITION = Triple("8361-8", "-", "Body Position")*//*MDC_BODY_POSITION("126977")*//*
        val bpSys = Triple("150017", "mmHg", "MDC_PRESS_BLD_SYS")
        val bpDia = Triple("150018", "mmHg", "MDC_PRESS_BLD_DIA")*/

        //Triple(<MDCCode>, <unit>, <dataDisplay>)

        val HR = Triple("8867-4", "beats/minute", "Heart rate")
        val RR = Triple("9279-1", "breaths/minute", "Respiratory rate")
        val SpO2 = Triple("2708-6", "%", "Oxygen saturation in Arterial blood")
        val TempForChest = Triple("8310-5", "degF", "Temperature")//MDC_TEMP_BODY("150364"),
        val ANGLE = Triple("8519760", "degree", "MDC_AI_TYPE_BASE_COORD_ANGLE")/*MDC_AI_TYPE_BASE_COORD_ANGLE("8519760")*/
        val BODY_POSITION = Triple("126977", "", "MDC_BODY_POSITION")/*MDC_BODY_POSITION("126977")*/
        val bpSys = Triple("150017", "mmHg", "MDC_PRESS_BLD_SYS")
        val bpDia = Triple("150018", "mmHg", "MDC_PRESS_BLD_DIA")

        fun getTempTriple(sensorId: Int?): Triple<String, String, String>? {
            sensorId ?: return null
            val loincId =  if(sensorId == CommonDataArea.currentChestSensorId) "8310-5" else "98657-0"
            return Triple(loincId, "degF", "Temperature")
        }
        fun getMdcTempTriple(sensorId: Int?): Triple<String, String, String>? {
            sensorId ?: return null
            val mdcCode =  if(sensorId == CommonDataArea.currentChestSensorId) "150364" else "150388"
            val mdcDisplay =  if(sensorId == CommonDataArea.currentChestSensorId) "MDC_TEMP_BODY" else "MDC_TEMP_SKIN"
            return Triple(mdcCode, "degF", mdcDisplay)
        }//MDC_TEMP_SKIN("150388"),
    }

}