package com.spacelabs.app.interfaces

interface IOEventCallback {

    enum class IOEventType {
        EnrollSensor,
        ScheduleAlarm,
        CancelAlarm,
        StartAutoBp,
        StartManualBp,
        TriggerTurnDue,
        HasFallen,
        StreamUpload,
        BackgroundUpload,
        StreamTypeStart,
        ConnectionStatusUpdate,
        AskPermission,
        GetTrendData,
        ApiTokenUpdate
    }

    fun ioEvent(event: IOEventType, eventData: Any?)
}