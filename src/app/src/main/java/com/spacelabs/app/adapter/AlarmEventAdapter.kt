package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.AlarmEvents2Table
import com.spacelabs.app.database.objectbox.boxes.AlarmEventData

class AlarmEventAdapter(private var patientList: List<AlarmEventData> = emptyList()) : RecyclerView.Adapter<AlarmEventAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_event_alarm, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON>ViewHolder, position: Int) {
        val alarmEvent = patientList[position]
        holder.bind(alarmEvent)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val alarmEventId: TextView = itemView.findViewById(R.id.alarmEventId)
        private val alarmUuid: TextView = itemView.findViewById(R.id.alarmEventUuid)
        private val alarmId: TextView = itemView.findViewById(R.id.alarmId)
        private val value: TextView = itemView.findViewById(R.id.value)
        private val updatedTime: TextView = itemView.findViewById(R.id.updatedTime)
        private val alarmStatus: TextView = itemView.findViewById(R.id.alarmStatus)
        private val uploadStatus: TextView = itemView.findViewById(R.id.uploadStatus)
        private val uploadTime: TextView = itemView.findViewById(R.id.uploadTime)


        fun bind(alarmEvent: AlarmEventData) {
            alarmEventId.text = alarmEvent.alarmEventId.toString()
            alarmUuid.text = alarmEvent.alarmEventUUID
            alarmId.text = alarmEvent.alarmId.toString()
            value.text = alarmEvent.value.toString()
            updatedTime.text = alarmEvent.updatedTime
            alarmStatus.text = alarmEvent.alarmStatus
            uploadStatus.text = alarmEvent.uploadStatus.toString()
            uploadTime.text = alarmEvent.uploadTime.toString()
        }
    }
}

