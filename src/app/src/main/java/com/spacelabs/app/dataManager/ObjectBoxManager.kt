package com.spacelabs.app.dataManager

import android.util.Log
import com.spacelabs.app.BuildConfig
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.objectbox.boxes.AlarmData
import com.spacelabs.app.database.objectbox.boxes.AlarmEventData
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.boxes.MyObjectBox
import io.objectbox.Box
import io.objectbox.BoxStore
import io.objectbox.android.Admin

open class ObjectBoxManager {

    companion object {
        private lateinit var boxStore: BoxStore
        private fun initObjectBox() {
            if (!::boxStore.isInitialized) {
                boxStore = MyObjectBox.builder().androidContext(CommonDataArea.curActivity)
                    .maxSizeInKByte(25 * 1024 * 1024).build()

                if (BuildConfig.DEBUG) {
                    val started: Boolean = Admin(boxStore).start(CommonDataArea.curActivity)
                    Log.i("ObjectBoxAdmin", "Started: $started")
                }
            }
        }

        fun getObjectBoxStore(): BoxStore {
            if (!::boxStore.isInitialized) {
                initObjectBox()
            }
            return boxStore
        }

        private var measurementBox: Box<MeasurementData>? = null
        private var alarmBox: Box<AlarmData>? = null
        private var alarmEventData: Box<AlarmEventData>? = null

        fun getMeasurementBox(): Box<MeasurementData> {
            if (measurementBox == null)
                measurementBox = getObjectBoxStore().boxFor(MeasurementData::class.java)

            return measurementBox!!
        }

        fun getAlarmBox(): Box<AlarmData> {
            if(alarmBox == null)
                alarmBox = getObjectBoxStore().boxFor(AlarmData::class.java)

            return alarmBox!!
        }

        fun getAlarmEventBox(): Box<AlarmEventData> {
            if(alarmEventData == null)
                alarmEventData = getObjectBoxStore().boxFor(AlarmEventData::class.java)

            return alarmEventData!!
        }
    }


}