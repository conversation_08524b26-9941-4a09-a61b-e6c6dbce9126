package com.spacelabs.app.database.objectbox.boxes

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

@Entity
data class AlarmData(
    @Id
    var alarmId: Long?,
    val alarmUUID: String,
    val patientId: Long,
    val defaultAlarmId: Int?,
    val patientAlarmId: Long?,
    val alarmName: String,
    val limitExceeded: Double,
    val startTime: String,
    var endTime: String?,
    var duration: Long?,
    var isAcknowledged: Int,
    var acknowledgedTime: String?,
    var uploadStatus: Int,
    val uploadTimeMillis: Long
)