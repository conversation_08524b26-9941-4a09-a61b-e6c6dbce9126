package com.spacelabs.app.database.dataClearance

import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresApi

class ClearDataService : Service() {

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    @Requires<PERSON>pi(Build.VERSION_CODES.S)
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Thread {
            val dataDeletionHandler = DataDeletionHandler(this)
            dataDeletionHandler.startDeletionPeriodicTask()
        }.start()
        return START_STICKY
    }

}
