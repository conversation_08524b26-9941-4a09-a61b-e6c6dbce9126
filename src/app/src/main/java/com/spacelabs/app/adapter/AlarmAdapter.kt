package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.AlarmsTable
import com.spacelabs.app.database.objectbox.boxes.AlarmData

class AlarmAdapter(alarmList: List<AlarmData>): RecyclerView.Adapter<AlarmAdapter.PatientViewHolder>() {

    private val alarmList: List<AlarmData>
    init {
        this.alarmList = alarmList
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_alarm, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON>ViewHolder, position: Int) {
        val patient = alarmList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return alarmList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val alarmId: TextView = itemView.findViewById(R.id.alarmId)
        private val alarmUuid: TextView = itemView.findViewById(R.id.alarmUuid)
        private val patientId: TextView = itemView.findViewById(R.id.patientId)
        private val defaultAlarmId: TextView = itemView.findViewById(R.id.defaultAlarmId)
        private val patientAlarmId: TextView = itemView.findViewById(R.id.patientAlarmId)
        private val alarmName: TextView = itemView.findViewById(R.id.alarmName)
        private val limitExceeded: TextView = itemView.findViewById(R.id.limit)
        private val startTime: TextView = itemView.findViewById(R.id.startTime)
        private val endTime: TextView = itemView.findViewById(R.id.endTime)
        private val duration: TextView = itemView.findViewById(R.id.duration)
        private val isAcknowledged: TextView = itemView.findViewById(R.id.isAcknowledged)
        private val acknowledgeTime: TextView = itemView.findViewById(R.id.timeAcknowledged)
        private val uploadStatus: TextView = itemView.findViewById(R.id.uploadStatus)


        fun bind(alarm: AlarmData) {
            alarmId.text = alarm.alarmId.toString()
            alarmUuid.text = alarm.alarmUUID
            patientId.text = alarm.patientId.toString()
            defaultAlarmId.text = alarm.defaultAlarmId.toString()
            patientAlarmId.text = alarm.patientAlarmId.toString()
            alarmName.text = alarm.alarmName
            limitExceeded.text = alarm.limitExceeded.toString()
            startTime.text = alarm.startTime
            endTime.text = alarm.endTime
            duration.text = alarm.duration.toString()
            isAcknowledged.text = alarm.isAcknowledged.toString()
            acknowledgeTime.text = alarm.acknowledgedTime.toString()
            uploadStatus.text = alarm.uploadStatus.toString()

        }
    }
}