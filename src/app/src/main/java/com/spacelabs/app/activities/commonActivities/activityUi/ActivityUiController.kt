package com.spacelabs.app.activities.commonActivities.activityUi

import android.app.Activity
import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

abstract class ActivityUiController(activity: Activity) {

    protected val uiScope = CoroutineScope(Dispatchers.Main + Job())

    protected val context: Context
    private val activity: Activity

    init {
        this.activity = activity
        this.context = activity
    }

    fun setUpActivityUi(){
        initUi()
        postInitUiActions()
        uiActionListeners()
    }
    abstract fun initUi()
    abstract fun postInitUiActions()
    abstract fun uiActionListeners()

    protected fun showToast(msg: String){
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show()
    }

    protected fun navigateToNextActivity(activityClass: Class<out Activity>){
        uiScope.launch {
            try{
                Log.d("activity", "CurrentActivity -> ${activityClass.name}")
                val intent = Intent(context, activityClass)
                activity.startActivity(intent, ActivityOptions.makeSceneTransitionAnimation(activity).toBundle())
            } catch(ex: Exception){
                Log.d("OnNavigation", ex.stackTraceToString())
            }
        }
    }

}