package com.spacelabs.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.spacelabs.app.R
import com.spacelabs.app.database.entities.DefaultAlarmSettingsTable

class DefaultAlarmSettingsAdapter(private var patientList: List<DefaultAlarmSettingsTable> = emptyList()) : RecyclerView.Adapter<DefaultAlarmSettingsAdapter.PatientViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PatientViewHolder {
        val itemView = LayoutInflater.from(parent.context).inflate(R.layout.single_row_tbl_default_alarm_settings, parent, false)
        return PatientViewHolder(itemView)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON>ViewHolder, position: Int) {
        val patient = patientList[position]
        holder.bind(patient)
    }

    override fun getItemCount(): Int {
        return patientList.size
    }

    inner class PatientViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val alarmId: TextView = itemView.findViewById(R.id.alaram_id)
        private val paramId: TextView = itemView.findViewById(R.id.param_id)
        private val defaultAlarmCategory: TextView = itemView.findViewById(R.id.default_alarm_category)
        private val defaultAlarmHv: TextView = itemView.findViewById(R.id.default_alarm_hv)
        private val defaultAlarmLv: TextView = itemView.findViewById(R.id.default_alarm_lv)
        private val defaultAlarmEhv: TextView = itemView.findViewById(R.id.default_alarm_ehv)
        private val defaultAlarmElv: TextView = itemView.findViewById(R.id.default_alarm_elv)
        private val defaultAlarmStatus: TextView = itemView.findViewById(R.id.default_alarm_status)
        private val defaultSound: TextView = itemView.findViewById(R.id.default_sound)



        fun bind(patient: DefaultAlarmSettingsTable) {
            alarmId.text = patient.alarmId.toString()
            paramId.text = patient.paramId.toString()
            defaultAlarmCategory.text = patient.defaultAlarmCategory
            defaultAlarmHv.text = patient.defaultAlarmHighValue.toString()
            defaultAlarmLv.text = patient.defaultAlarmLowValue.toString()
            defaultAlarmEhv.text = patient.defaultAlarmExtremeHighValue.toString()
            defaultAlarmElv.text = patient.defaultAlarmExtremeLowValue.toString()
            defaultAlarmStatus.text = patient.defaultAlarmStatus.toString()
            defaultSound.text = patient.defaultSound.toString()
        }
    }
}

