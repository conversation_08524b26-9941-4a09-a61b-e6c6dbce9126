package com.spacelabs.app.database.relationships

import androidx.room.Embedded
import androidx.room.Relation
import com.spacelabs.app.database.entities.MeasurementDataTable
import com.spacelabs.app.database.entities.PatientTable
import com.spacelabs.app.database.entities.VisitTable

data class PatientWithVisitAndMeasurementData(
    @Embedded
    val patient: VisitTable?,
    @Relation(
        parentColumn = "visitId",
        entityColumn = "visitId"
    )
    val measurementDataTable: List<MeasurementDataTable>?,
    @Relation(
        parentColumn = "patientId",
        entityColumn = "patientId"
    )
    val patientTable: PatientTable?
)