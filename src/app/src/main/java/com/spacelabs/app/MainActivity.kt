package com.spacelabs.app

import android.app.Activity
import android.content.*
import android.net.wifi.WifiManager
import android.nfc.NfcAdapter
import android.os.BatteryManager
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.transition.Slide
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.Window
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.sibelhealth.bluetooth.sensorservice.DefaultSensorService
import com.sibelhealth.bluetooth.sensorservice.SensorService
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.adt.AdtAPI
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.alarms.SibelAlarmManager
import com.spacelabs.app.api.data.ApiDataTransactionManager
import com.spacelabs.app.common.ApplicationManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.database.daoHelper.*
import com.spacelabs.app.database.dataClearance.ClearDataService
import com.spacelabs.app.database.objectbox.helpers.AlarmBoxDaoHelper
import com.spacelabs.app.connectivity.NfcDiscoveryHelper
import com.spacelabs.app.sensor.SibelSensorManager
import com.spacelabs.app.sensor.helper.BpSensorObserverHelper
import com.spacelabs.app.sensor.helper.ChestSensorObserverHelper
import com.spacelabs.app.sensor.helper.LimbSensorObserverHelper
import com.spacelabs.app.ui.Dialogs
import com.spacelabs.app.ui.IbsmFragments
import com.spacelabs.app.ui.MainActivityUIs
import com.spacelabs.app.ui.dialogs.CommonNotificationDialogs
import com.spacelabs.app.ui.dialogs.DialogManager
import kotlinx.coroutines.*
import java.time.Instant
import java.time.format.DateTimeFormatter
import kotlin.coroutines.CoroutineContext

val sensorService: SensorService by lazy { DefaultSensorService() }

@Suppress("DEPRECATION")
class MainActivity : AppCompatActivity() {

    lateinit var dialog: Dialogs
    lateinit var fragments: IbsmFragments

    val appManager = ApplicationManager(this)
    private lateinit var viewModelJob: CoroutineContext
    private lateinit var ioViewModelJob: CoroutineContext

    lateinit var mainActivityHelper: MainActivityHelper
    private lateinit var nfcDiscoveryHelper: NfcDiscoveryHelper

    lateinit var uiScope: CoroutineScope
    lateinit var ioScope: CoroutineScope
    lateinit var mainActivityUIs: MainActivityUIs
    lateinit var alarmManager: SibelAlarmManager
    var iconServer: MenuItem? = null
    lateinit var iconBattery: MenuItem
    private var iconWifi: MenuItem? = null
    private lateinit var wifiManager: WifiManager
    private lateinit var commonNotification: CommonNotificationDialogs
    private lateinit var alarmDaoHelper: AlarmDaoHelper

    private var nfcAdapter: NfcAdapter? = null


    private val crashHAndlerTag = "Global Crash Handler"

    @RequiresApi(Build.VERSION_CODES.R)
    private fun manageAllFilesPermissionActivityForResult() {
        val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && !Environment.isExternalStorageManager()) {
            resultLauncher.launch(intent)
        }
    }

    private var resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                // There are no request codes
                val data: Intent? = result.data
            }
        }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun onCreate(savedInstanceState: Bundle?) {
        try {
            super.onCreate(savedInstanceState)
            setTheme(R.style.Theme_SibelPatch)
            setWindowProperties()
            setContentView(R.layout.activity_main)

            dialog = Dialogs(this@MainActivity)
            fragments = IbsmFragments(this@MainActivity)

            CommonDataArea.curActivity = this
            mainActivityHelper = MainActivityHelper(this@MainActivity)

            mainActivityUIs = MainActivityUIs(this@MainActivity)
            alarmManager = SibelAlarmManager(this@MainActivity)
            nfcDiscoveryHelper = NfcDiscoveryHelper(this)
            wifiManager = getSystemService(Context.WIFI_SERVICE) as WifiManager
            commonNotification = CommonNotificationDialogs(this@MainActivity)
            alarmDaoHelper = AlarmDaoHelper(this)
            appManager.requestPermissions()

            createCoRoutineScope()
            createIoCoroutineScope()

            mainActivityUIs.uiInit()
            mainActivityUIs.setupUiActionHandlers()

            if (!CommonDataArea.runModeEmulator)
                SibelSensorManager.initSibelSensors(this)

            mainActivityHelper.enableBluetoothIfTurnedOff()
            mainActivityUIs.startDisplayWatchDogThread()

            openSensorDialogIfNotConfigured()

            if (enableSnsApi)
                ApiDataTransactionManager().checkDbAndUploadPendingDataToServer()

            checkAndUpdateAlarmEventsToDbCoroutine()
            if (!CommonDataArea.runModeEmulator) sensorService.startScan(5000)

            Thread.setDefaultUncaughtExceptionHandler { _, paramThrowable ->
                Log.e(
                    "Alert",
                    "Global Crash handler" + paramThrowable.message
                )
                LogWriter.writeException("${DateTimeFormatter.ISO_INSTANT.format(Instant.now())} --> ${paramThrowable.stackTraceToString()}")
                Log.d(crashHAndlerTag, paramThrowable.stackTraceToString())
                mainActivityUIs.triggerRebirth(applicationContext)
            }

            CommonDataArea.nfcHelper = NfcDiscoveryHelper(this)
            nfcAdapter = CommonDataArea.nfcHelper.nfcInit()

            val intent = Intent(this, ClearDataService::class.java)
            startService(intent)
        } catch (e: Exception) {
            LogWriter.writeExceptLog("Main OnCreate init ", e.stackTraceToString())
            Log.d("MAIN_ACTIVITY_EXCEPTION", "Main OnCreate init ${ e.stackTraceToString()}")
           mainActivityUIs.triggerRebirth(applicationContext)
        }

        if (!enableSnsApi) AdtAPI(this).startApiCallTimer()

    }

    private val wifiStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == WifiManager.WIFI_STATE_CHANGED_ACTION || intent.action == WifiManager.NETWORK_STATE_CHANGED_ACTION || intent.action == WifiManager.RSSI_CHANGED_ACTION) {
                val wifiManager =
                    context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
                val wifiInfo = wifiManager.connectionInfo
                val rssi = wifiInfo.rssi
                val iconResId = calculateRangeFromRssi(rssi)
                iconWifi?.setIcon(iconResId)
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    override fun onStart() {
        super.onStart()
       manageAllFilesPermissionActivityForResult()
        // SibelSensorManager.startSensorLog() /*Log from Sibel Sdk*/
        CommonEventHandler.registerUiEventCallback(
            mainActivityUIs.UiEventHandler(),
            this::class.java
        )
        CommonEventHandler.registerAlarmEventCallback(
            alarmManager.AlarmEventHandler(),
            this::class.java
        )
        CommonEventHandler.registerIOEventCallback(
            mainActivityHelper.IoEventHandler(),
            this::class.java
        )
        mainActivityHelper.internetConnectionCallback()
        mainActivityHelper.ibsmBluetoothManager.registerReceiver()
        alarmDaoHelper.fetchCurrentPatientAlarmSettings()

    }

    override fun onStop() {
        super.onStop()
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
        CommonEventHandler.unRegisterAlarmEventCallback(this::class.java)
        CommonEventHandler.unregisterIOEventCallback(this::class.java)
        mainActivityHelper.ibsmBluetoothManager.unregisterReceiver()
        dialog.closeAllDialogs()
    }

    @RequiresApi(Build.VERSION_CODES.S)
    override fun onResume() {
        //manageAllFilesPermissionActivityForResult()
        DialogManager.SimpleDialog = null
        if (nfcAdapter != null)
            CommonDataArea.nfcHelper.onResumeAction()
        super.onResume()
        val filter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
            addAction(WifiManager.RSSI_CHANGED_ACTION)
        }
        registerReceiver(wifiStateReceiver, filter)
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        super.onBackPressed()
        moveTaskToBack(true)
        finishAndRemoveTask()
    }


//    override fun onNewIntent(intent: Intent?) {
//        if (nfcAdapter != null)
//            CommonDataArea.nfcHelper.onNewIntentAction(intent)
//        super.onNewIntent(intent)
//    }

    private val batteryBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            mainActivityHelper.onCreateChargingBattery(iconBattery)
        }

    }

    private fun startBatteryMonitoringCoroutine() {
        uiScope.launch {
            while (true) {
                mainActivityHelper.onCreateOptionsMenuBatteryAction(iconBattery)
                delay(60000)
            }
        }
    }


    private fun createCoRoutineScope() {
        viewModelJob = Dispatchers.Main + Job()
        uiScope = CoroutineScope(viewModelJob)
    }

    private fun createIoCoroutineScope() {
        ioViewModelJob = Dispatchers.IO + Job()
        ioScope = CoroutineScope(ioViewModelJob)
    }

    private fun checkAndUpdateAlarmEventsToDbCoroutine() {
        ioScope.launch {
            while (true) {
                synchronized(this) {
                    AlarmBoxDaoHelper().checkAndUpdateAllAlarms()
                }
                delay(1000)
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu, menu)
        iconServer = menu.findItem(R.id.server)
        iconBattery = menu.findItem(R.id.battery)
        iconWifi = menu.findItem(R.id.wifi)
        iconWifi?.setOnMenuItemClickListener {
            getFragment(IbsmFragments.Windows.GENERAL_SETTINGS)
            true
        }
        val batteryStatusFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        registerReceiver(batteryBroadcastReceiver, batteryStatusFilter)
        startBatteryMonitoringCoroutine()
        return true
    }

    override fun onOptionsMenuClosed(menu: Menu?) {
        super.onOptionsMenuClosed(menu)
        unregisterReceiver(batteryBroadcastReceiver)
    }

    private fun calculateRangeFromRssi(rssi: Int): Int {
        return when {
            rssi >= -50 -> R.drawable.wifi
            rssi >= -60 -> R.drawable.wifi
            rssi >= -75 -> R.drawable.wifi_fair
            rssi >= -95 -> R.drawable.wifi_weak
            else -> R.drawable.wifi_slash
        }
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun openSensorDialogIfNotConfigured() {
        val chestSensorName = SibelSensorManager.getEnrolledSensorNameByType(this, SensorType.CHEST)
        val limbSensorName = SibelSensorManager.getEnrolledSensorNameByType(this, SensorType.LIMB)
        if (chestSensorName!!.isEmpty() && limbSensorName!!.isEmpty()) {
            SibelSensorManager.setSensorScanMode(SibelSensorManager.SensorScanMode.NormalScan)
            appManager.requestPermissions()
            dialog.getSensorListDialog()
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun setWindowProperties() {
        with(window) {
            requestFeature(Window.FEATURE_ACTIVITY_TRANSITIONS)
            enterTransition = Slide()
            exitTransition = android.transition.Fade()
        }

        window.decorView.windowInsetsController!!.hide(
            android.view.WindowInsets.Type.statusBars()
        )
    }

    fun getFragment(window: IbsmFragments.Windows) {
        fragments.getFragment(window, mainActivityUIs.fullFrameLayout)
    }

    private fun cancelActivityCoroutineJobs() {
        viewModelJob.cancel()
        ioViewModelJob.cancel()
    }

    override fun onDestroy() {
        super.onDestroy()
        cancelActivityCoroutineJobs()
    }
}