package com.spacelabs.app.database.objectbox.helpers

import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.dataManager.ObjectBoxManager
import com.spacelabs.app.database.objectbox.boxes.MeasurementData
import com.spacelabs.app.database.objectbox.boxes.MeasurementData_
import io.objectbox.Box
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

class DataReplicator {

    private val box: Box<MeasurementData> = ObjectBoxManager.getObjectBoxStore().boxFor(
        MeasurementData::class.java)
    private val dateFormat: SimpleDateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXX")

    private fun getLastDayDataWithTickValues(): List<MeasurementData> {
        val currentTimeMillis = TimestampUtils.getCurrentTimeMillis()
        val oneDayInMillis = /*24 * */60 * 60 * 1000 // Change the time here to test

        val thresholdTime = currentTimeMillis - oneDayInMillis
        return box.query()
            .greater(MeasurementData_.tickValue, thresholdTime)
            .orderDesc(MeasurementData_.__ID_PROPERTY)  // Assuming there's an ID property for ordering
            .build()
            .find()
    }

    // Function to create new MeasurementData based on last day's data and increased timestamp
    private fun createMeasurementDataFromLastDay(
        lastDayData: MeasurementData,
        newTimestamp: Long
    ): MeasurementData {
        return MeasurementData(
            measurementId = null,
            measurementUuid = lastDayData.measurementUuid,
            patientId = lastDayData.patientId,
            visitId = lastDayData.visitId,
            sensorId = lastDayData.sensorId,
            patientID1 = lastDayData.patientID1,
            valueType = lastDayData.valueType,
            paramName = lastDayData.paramName,
            value = lastDayData.value,
            measurementData = lastDayData.measurementData,
            numberOfSamples = lastDayData.numberOfSamples,
            timestamp = dateFormat.format(Date(newTimestamp)),
            uploadStatus = lastDayData.uploadStatus,
            uploadTimestamp = lastDayData.uploadTimestamp,
            retryCount = lastDayData.retryCount,
            tickValue = newTimestamp,
            sln = lastDayData.sln
        )
    }

    fun processLastDayAndCreateData() {
        // Get the last day's data with tickValues
        val lastDayDataList = getLastDayDataWithTickValues()

        for (lastDayData in lastDayDataList) {
            val currentDate = dateFormat.parse(lastDayData.timestamp)
            val calendar = Calendar.getInstance()

            for (i in 1..14) {
                // Increment the date by one day
                calendar.time = currentDate
                calendar.add(Calendar.DAY_OF_MONTH, i)

                val newMeasurementData =
                    createMeasurementDataFromLastDay(lastDayData, calendar.timeInMillis)

                box.put(newMeasurementData)
            }
        }
    }
}