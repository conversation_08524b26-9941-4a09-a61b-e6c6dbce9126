package com.spacelabs.app.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblPatientAlarm")
data class PatientAlarmTable(
    @PrimaryKey(autoGenerate = true)
    val patientAlarmId: Int?,
    val patientId: Int,
    val paramId: Int,
    val alarmCategory: String?,
    val alarmName: String?,
    val value: String?,
    var lowValue: Double?,
    var extremeLowValue: Double?,
    var highValue: Double?,
    var extremeHighValue: Double?,
    var timeStamp: String,
    var alarmStatus: Int,
    var alarmSound: Int
)