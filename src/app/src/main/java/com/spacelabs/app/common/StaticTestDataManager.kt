package com.spacelabs.app.common

import android.content.Context
import android.util.Log
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import java.io.InputStream
import java.util.*

/**
 * Utility class to manage static test data for testing purposes.
 * Reads FHIR observation data from JSON file in assets and provides methods
 * to return static data when CommonDataArea.useStaticTestData is true.
 */
class StaticTestDataManager private constructor() {
    
    companion object {
        private const val TAG = "StaticTestDataManager"
        private const val STATIC_DATA_FILE = "static_medical_test_data.json"
        
        @Volatile
        private var INSTANCE: StaticTestDataManager? = null
        
        fun getInstance(): StaticTestDataManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: StaticTestDataManager().also { INSTANCE = it }
            }
        }
    }
    
    private var staticDataJson: JSONObject? = null
    private var waveformObservations: JSONArray? = null
    private var vitalObservations: JSONArray? = null
    private var alarmObservations: JSONArray? = null
    private var technicalAlarms: JSONArray? = null
    
    /**
     * Initialize the static data manager by loading JSON from assets
     */
    fun initialize(context: Context) {
        try {
            val inputStream: InputStream = context.assets.open(STATIC_DATA_FILE)
            val size = inputStream.available()
            val buffer = ByteArray(size)
            inputStream.read(buffer)
            inputStream.close()
            
            val jsonString = String(buffer, Charsets.UTF_8)
            staticDataJson = JSONObject(jsonString)
            
            // Parse different data types
            waveformObservations = staticDataJson?.optJSONArray("waveformObservations")
            vitalObservations = staticDataJson?.optJSONArray("vitalObservations")
            alarmObservations = staticDataJson?.optJSONArray("alarmObservations")
            technicalAlarms = staticDataJson?.optJSONArray("technicalAlarms")
            
            Log.d(TAG, "Static test data loaded successfully")
        } catch (e: IOException) {
            Log.e(TAG, "Error loading static test data: ${e.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing static test data: ${e.message}")
        }
    }
    
    /**
     * Check if static test data should be used
     */
    fun shouldUseStaticData(): Boolean {
        return CommonDataArea.useStaticTestData
    }
    
    /**
     * Get static waveform observation data for ECG
     */
    fun getStaticECGWaveformObservation(): String? {
        if (!shouldUseStaticData() || waveformObservations == null) return null
        
        return try {
            // Find ECG waveform observation (code: 131143)
            for (i in 0 until waveformObservations!!.length()) {
                val observation = waveformObservations!!.getJSONObject(i)
                val component = observation.getJSONArray("component").getJSONObject(0)
                val coding = component.getJSONObject("code").getJSONArray("coding").getJSONObject(0)
                if (coding.getString("code") == "131143") {
                    return observation.toString()
                }
            }
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting ECG waveform data: ${e.message}")
            null
        }
    }
    
    /**
     * Get static waveform observation data for SpO2
     */
    fun getStaticSpO2WaveformObservation(): String? {
        if (!shouldUseStaticData() || waveformObservations == null) return null
        
        return try {
            // Find SpO2 waveform observation (code: 150452)
            for (i in 0 until waveformObservations!!.length()) {
                val observation = waveformObservations!!.getJSONObject(i)
                val component = observation.getJSONArray("component").getJSONObject(0)
                val coding = component.getJSONObject("code").getJSONArray("coding").getJSONObject(0)
                if (coding.getString("code") == "150452") {
                    return observation.toString()
                }
            }
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting SpO2 waveform data: ${e.message}")
            null
        }
    }
    
    /**
     * Get static waveform observation data for Respiration
     */
    fun getStaticRespirationWaveformObservation(): String? {
        if (!shouldUseStaticData() || waveformObservations == null) return null
        
        return try {
            // Find Respiration waveform observation (code: 151562)
            for (i in 0 until waveformObservations!!.length()) {
                val observation = waveformObservations!!.getJSONObject(i)
                val component = observation.getJSONArray("component").getJSONObject(0)
                val coding = component.getJSONObject("code").getJSONArray("coding").getJSONObject(0)
                if (coding.getString("code") == "151562") {
                    return observation.toString()
                }
            }
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Respiration waveform data: ${e.message}")
            null
        }
    }
    
    /**
     * Get static vital signs observation data
     */
    fun getStaticVitalObservation(): String? {
        if (!shouldUseStaticData() || vitalObservations == null) return null
        
        return try {
            if (vitalObservations!!.length() > 0) {
                vitalObservations!!.getJSONObject(0).toString()
            } else null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting vital observation data: ${e.message}")
            null
        }
    }
    
    /**
     * Get static alarm observation data
     */
    fun getStaticAlarmObservation(alarmType: String = "HR_LOW"): String? {
        if (!shouldUseStaticData() || alarmObservations == null) return null
        
        return try {
            // Return first alarm observation for now, can be enhanced to filter by type
            if (alarmObservations!!.length() > 0) {
                alarmObservations!!.getJSONObject(0).toString()
            } else null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting alarm observation data: ${e.message}")
            null
        }
    }
    
    /**
     * Get static technical alarm communication data
     */
    fun getStaticTechnicalAlarm(alarmType: String = "BATTERY"): String? {
        if (!shouldUseStaticData() || technicalAlarms == null) return null
        
        return try {
            // Return first technical alarm for now, can be enhanced to filter by type
            if (technicalAlarms!!.length() > 0) {
                technicalAlarms!!.getJSONObject(0).toString()
            } else null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting technical alarm data: ${e.message}")
            null
        }
    }
    
    /**
     * Get static waveform sample data as DoubleArray for testing
     */
    fun getStaticWaveformSamples(waveformType: String): DoubleArray? {
        if (!shouldUseStaticData()) return null
        
        return try {
            val observation = when (waveformType.uppercase()) {
                "ECG" -> getStaticECGWaveformObservation()
                "SPO2", "PPG" -> getStaticSpO2WaveformObservation()
                "RESP", "RESPIRATION" -> getStaticRespirationWaveformObservation()
                else -> null
            }
            
            if (observation != null) {
                val jsonObj = JSONObject(observation)
                val component = jsonObj.getJSONArray("component").getJSONObject(0)
                val sampledData = component.getJSONObject("valueSampledData")
                val dataString = sampledData.getString("data")
                
                // Parse space-separated values into DoubleArray
                val values = dataString.split(" ")
                DoubleArray(values.size) { i -> values[i].toDouble() }
            } else null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting waveform samples: ${e.message}")
            null
        }
    }
    
    /**
     * Get static vital value for specific parameter
     */
    fun getStaticVitalValue(parameterCode: String): Double? {
        if (!shouldUseStaticData()) return null
        
        return try {
            val vitalObservation = getStaticVitalObservation()
            if (vitalObservation != null) {
                val jsonObj = JSONObject(vitalObservation)
                val components = jsonObj.getJSONArray("component")
                
                for (i in 0 until components.length()) {
                    val component = components.getJSONObject(i)
                    val coding = component.getJSONObject("code").getJSONArray("coding").getJSONObject(0)
                    if (coding.getString("code") == parameterCode) {
                        return component.getJSONObject("valueQuantity").getDouble("value")
                    }
                }
            }
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error getting static vital value: ${e.message}")
            null
        }
    }
    
    /**
     * Generate unique UUID for test observations
     */
    fun generateTestUUID(): String {
        return "test-${UUID.randomUUID()}"
    }
    
    /**
     * Get current timestamp in FHIR format for test data
     */
    fun getCurrentTestTimestamp(): String {
        return java.time.Instant.now().toString()
    }
}
