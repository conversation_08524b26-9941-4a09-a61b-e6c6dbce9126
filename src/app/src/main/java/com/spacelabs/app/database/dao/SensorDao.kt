package com.spacelabs.app.database.dao

import androidx.room.Dao
import androidx.room.Query
import com.spacelabs.app.database.entities.SensorDefTable
import com.spacelabs.app.database.entities.SensorTable

@Dao
interface SensorDao {

    @Query("SELECT * FROM tblSensors ORDER BY sensorId DESC")
    suspend fun getAllSensorOrderByDesc(): List<SensorTable>

    @Query("SELECT * FROM tblSensorDef")
    suspend fun getAllSensorDef(): List<SensorDefTable>

    @Query("DELETE FROM tblSensors WHERE patientId != :patientId")
    suspend fun deleteAllSensors(patientId: Int): Int
    @Query("DELETE FROM tblSensors WHERE patientId IN (:inactivePatientIds)")
    suspend fun deleteAllSensorsOnDischarge(inactivePatientIds: List<Int>): Int

    @Query("DELETE FROM tblSensorDef")
    suspend fun deleteAllSensorDef(): Int

}