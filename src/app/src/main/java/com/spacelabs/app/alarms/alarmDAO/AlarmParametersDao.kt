package com.spacelabs.app.alarms.alarmDAO

import android.os.CountDownTimer
import android.text.Editable
import android.text.Html
import android.text.Spannable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.TextView
import com.spacelabs.app.alarms.CurrentAlarmParameter
import com.spacelabs.app.alarms.SibelAlarmManager
import com.spacelabs.app.common.CommonDataArea
import kotlinx.coroutines.launch
import java.util.Locale

data class AlarmParametersDao(
    var paramName: String = "",
    var highValue: Double = 0.0,
    var lowValue: Double = 0.0,
    var extremeHighValue: Double = 0.0,
    var extremeLowValue: Double = 0.0,
    var alarmSound: Int = 0,

    var defaultAlarmId: Int = 0,
    var patientAlarmId: Int? = null,

    var highMaxValue: Double = 0.0,
    var highMinValue: Double = 0.0,
    var lowMaxValue: Double = 0.0,
    var lowMinValue: Double = 0.0,

    var alarmStatus: Boolean = false,
    var isAcknowledged: Boolean = false,
    var isAttached: Boolean = true,
    var acknowledgeTimer: CountDownTimer? = null,
    var isAcknowledgeTimerRunning: Boolean = false,
    var isBlinking: Boolean = false,
    var alarmText: String = "",
    var alarmPrefix: String = "",
    var alarmBgColor: Int = 0,
    var alarmTextColor: Int = 0,
    var alarmCriticality: Int = 0,
    var currentAlarmParameter: CurrentAlarmParameter = CurrentAlarmParameter.NoAlarm,
    var alarmTone: Int = 0,
    var alarmInterval: Long = 1,
    var isPlaying: Boolean = false
    ) {

    private val alarmParamsDaoHelper: AlarmParametersDaoHelper = AlarmParametersDaoHelper(this)
    var paramViews: ParameterViewsDao? = null

    fun setAlarmViewAndProperties(alarmViewAndProperties: ParameterViewsDao){
        this.paramViews = alarmViewAndProperties
        alarmBoxActionListener()
        countDownTextVisibilityManager()
    }

    fun setAlarmParameter(currentAlarmParameter: CurrentAlarmParameter){
        this.currentAlarmParameter = currentAlarmParameter
        alarmParamsDaoHelper.onSetCurrentAlarmProperty(currentAlarmParameter)
    }

    private fun alarmBoxActionListener() {
        val alarmBox = paramViews?.alarmBox ?: return
        alarmBox.setOnClickListener {
            if(!isAcknowledgeTimerRunning) {
                acknowledgeTimer = startCountDown()
            }
        }
    }

    private fun countDownTextVisibilityManager() {
        val silenceText = paramViews?.timerTextView ?: return
        silenceText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                //Action tobe performed before text change
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                //Action tobe performed while text is being changed
            }

            override fun afterTextChanged(s: Editable?) {
                val text = s.toString()
                if(text.isEmpty())
                    silenceText.visibility = View.GONE
                else
                    silenceText.visibility = View.VISIBLE
            }
        })
    }

    private fun startCountDown(): CountDownTimer {
        val silenceText = paramViews?.timerTextView
        val currentAlarmParameter = currentAlarmParameter
        if(CommonDataArea.currentAlarmPriority == alarmCriticality) {
            CommonDataArea.mediaPlayer?.stop()
            CommonDataArea.currentAlarmPriority = SibelAlarmManager.NO_ALARM
        }
        isAcknowledgeTimerRunning = true
        val countDownTimer = object : CountDownTimer(60000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                isAcknowledged = true
                val minutes = ((millisUntilFinished / 1000) / 60).toInt()
                val seconds = ((millisUntilFinished / 1000) % 60).toInt()
                val timeLeftFormatted = String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds)
                setFormattedCountDownTime(
                    silenceText,
                    timeLeftFormatted
                )
            }
            override fun onFinish() {
                isAcknowledgeTimerRunning = false
                isAcknowledged = false
                Log.i("AlarmCounter ","Finished ->Alarm counter type->"+currentAlarmParameter.name)
            }

        }.start()
        return countDownTimer
    }

    private fun setFormattedCountDownTime(textView: TextView?, time: String){
        textView?.text = Html.fromHtml(
            "Silenced : <strong>$time</strong>",
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
    }

}