package com.spacelabs.app.database.dao

import androidx.room.*
import com.spacelabs.app.database.entities.AlarmEvents2Table
import com.spacelabs.app.database.entities.AlarmsTable
import com.spacelabs.app.database.entities.DefaultAlarmSettingsTable
import com.spacelabs.app.database.entities.PatientAlarmTable
import com.spacelabs.app.database.relationships.AlarmWithAlarmEvents
import com.spacelabs.app.database.relationships.ParametersAndDefaultAlarmSettings
import com.spacelabs.app.database.relationships.PatientAlarmWithPatientAndParameter
import com.spacelabs.app.database.relationships.PatientWithPatientAlarm

@Dao
interface AlarmDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDefaultAlarm(defaultAlarmSettings: DefaultAlarmSettingsTable): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPatientAlarm(patientAlarm: PatientAlarmTable): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlarm(alarm: AlarmsTable): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlarmEvent(alarmEvent: AlarmEvents2Table): Long

    @Update
    suspend fun updateDefaultAlarm(defaultAlarmSettings: DefaultAlarmSettingsTable)

    @Update
    suspend fun updatePatientAlarm(patientAlarmSettings: PatientAlarmTable): Int

    @Update
    suspend fun updateAlarmsTable(alarm: AlarmsTable): Int

    @Update
    suspend fun updateAlarmEvent(alarmEventsTable: AlarmEvents2Table): Int

    @Query("SELECT * FROM tblPatientAlarm ORDER BY patientAlarmId DESC")
    suspend fun getAllPatientAlarm(): List<PatientAlarmTable>

    @Query("SELECT * FROM tblAlarmEvents2 ORDER BY alarmEventId DESC")
    suspend fun getAllAlarmEvent(): List<AlarmEvents2Table>

    @Query("SELECT * FROM tblDefaultAlarmSettings ORDER BY alarmId DESC")
    suspend fun getAllDefaultAlarmSettings(): List<DefaultAlarmSettingsTable>

    @Query("select * from tblDefaultAlarmSettings")
    suspend fun getDefaultAlarm(): List<DefaultAlarmSettingsTable>?

    @Transaction
    @Query("SELECT * FROM tblParameters WHERE paramName = :paramName")
    suspend fun getDefaultAlarmWithParameterOnParamName(paramName: String): ParametersAndDefaultAlarmSettings

    @Transaction
    @Query("SELECT * FROM tblPatient p INNER JOIN tblpatientalarm a ON p.patientId = a.patientId WHERE p.patientId = :patientId AND a.paramId = :paramId")
    suspend fun getActivePatientAlarmSettingsByParamId(paramId: Int, patientId: Int): PatientWithPatientAlarm?

    @Transaction
    @Query("SELECT * FROM tblPatient WHERE status = 'active'")
    suspend fun getActivePatientAlarmSettings(): PatientWithPatientAlarm?

    @Transaction
    @Query("SELECT defaultSound FROM tblDefaultAlarmSettings WHERE paramId = (SELECT paramId FROM tblParameters WHERE paramName = :paramName)")
    suspend fun getDefaultSoundByParamName(paramName: String): Int

    @Transaction
    @Query("SELECT * FROM tblPatientAlarm WHERE paramId = :paramId AND patientId = :patientId")
    suspend fun selectPatientAlarmByParamId(paramId: Int, patientId: Int): PatientAlarmTable?

    @Transaction
    @Query("SELECT * FROM tblParameters pr INNER JOIN tblPatientAlarm pa ON pr.paramId = pa.paramId INNER JOIN tblPatient pt ON pt.patientId = pa.patientId WHERE pr.paramName = :paramName AND pt.patientId = (SELECT patientId FROM tblPatient WHERE status = 'active')")
    suspend fun getPatientAlarmCompleteDetailsByParameterName(paramName: String): PatientAlarmWithPatientAndParameter?

    @Transaction
    @Query("SELECT * FROM tblAlarms ORDER BY alarmId DESC")
    suspend fun getAllAlarms(): List<AlarmsTable>

    @Transaction
    @Query("SELECT MAX(alarmId) FROM tblAlarms WHERE alarmName = :alarmName")
    suspend fun getLastEnteredAlarmIdByAlarmName(alarmName: String): Int?

    @Transaction
    @Query("SELECT * FROM tblAlarms WHERE alarmName = :alarmName ORDER BY alarmId DESC LIMIT 1")
    suspend fun getLastEnteredAlarmByAlarmName(alarmName: String) :AlarmsTable

    @Transaction
    @Query("SELECT JULIANDAY(:endTime) - JULIANDAY(:startTime)")
    suspend fun getDateDifferenceAsDuration(startTime: String, endTime: String): String

    @Transaction
    @Query("SELECT value FROM tblAlarmEvents2 WHERE alarmId = :alarmId ORDER BY alarmEventId DESC LIMIT 1")
    suspend fun getLastEnteredEventValueByAlarmId(alarmId: Int): Double

    @Transaction
    @Query("SELECT * FROM tblAlarms al INNER JOIN tblAlarmEvents2 ae ON al.alarmId = ae.alarmId WHERE al.patientId = (SELECT patientId FROM tblPatient) AND ae.uploadStatus = 0")
    suspend fun getAllPendingAlarmToSend(): List<AlarmWithAlarmEvents>

    @Transaction
    @Query("SELECT * FROM tblAlarms WHERE alarmId = :alarmId")
    suspend fun getAlarmById(alarmId: Int): AlarmsTable?

    @Transaction
    @Query("SELECT * FROM tblPatientAlarm WHERE paramId = (SELECT paramId FROM tblParameters WHERE paramName = :paramName) AND patientId = (SELECT patientId FROM tblPatient WHERE status = 'active')")
    suspend fun getCurrentPatientAlarmUsingParamName(paramName: String): List<PatientAlarmTable>?

    @Delete
    suspend fun deletePatientAlarmParameter(patientAlarm: PatientAlarmTable): Int

    @Query("DELETE FROM tblAlarmEvents2 WHERE  tickValue <= :tick AND uploadStatus = 1")
    suspend fun deleteAllAlarmEvents2(tick:Long): Int

    @Query("DELETE FROM tblPatientAlarm  WHERE patientId != :patientId")
    suspend fun deleteAllPatientAlarmEvent(patientId: Int): Int

    @Query("DELETE FROM tblAlarmEvents2 WHERE  uploadStatus = 1")
    suspend fun deleteAlarmEvent2OnDischarge(): Int

    @Query("DELETE FROM tblAlarms WHERE  uploadStatus = 2")
    suspend fun deleteAllAlarmsOnDischarge(): Int

    @Query("DELETE FROM tblAlarms WHERE patientId != :patientId AND uploadStatus = 2")
    suspend fun deleteAllAlarms(patientId: Int): Int
}