package com.spacelabs.app.api.data.observations

import com.google.gson.GsonBuilder
import com.spacelabs.app.api.SnsApiManager
import com.spacelabs.app.api.data.dataclasses.VitalObservationBeanOld
import com.spacelabs.app.iomt.IomtApiManager

class VitalObservationDataOld: ObservationManager() {

    data class Observation(
        val resourceType: String,
        val id: String,
        val meta: Meta,
        val status: String,
        val category: List<Category>,
        val code: Code,
        val subject: Subject,
        val effectiveDateTime: String,
        val performer: List<Performer>,
        val device: Device,
        val component: List<Component>
    )

    data class Meta(
        val profile: List<String>
    )

    data class Category(
        val coding: List<Coding>,
        val text: String
    )

    data class Coding(
        val system: String,
        val code: String,
        val display: String
    )

    data class Code(
        val coding: List<Coding>,
        val text: String
    )

    data class Subject(
        val reference: String,
        val display: String
    )
    data class Performer(
        val reference: String,
        val display: String
    )

    data class Device(
        val reference: String,
        val display: String
    )

    data class ValueQuantity(
        val value: String,
        val unit: String,
        val system: String,
        val code: String
    )


    data class Component( // New data class for component
        val code: Code,
        val valueQuantity: ValueQuantity
    )

    fun getObservation(observationData: VitalObservationBeanOld): String {
        val observation = Observation(
            /*resourceType =  observationData.resourceType,
            id = observationData.uuId,
            meta =  Meta(listOf(hl7VitalSignUrl)),
            status = observationData.status,
            category =  listOf(Category(listOf(Coding(hl7SysValue, observationData.categoryCode, observationData.categoryDisplay)), observationData.categoryDisplay)),
            code =  Code(listOf(Coding(loincSysValue, observationData.vitalCode, observationData.vitalDisplay)), observationData.vitalDisplay),
            subject =  Subject("${observationData.subjectReference}/${SnsApiManager.apiResponseData.patient.patientID1}", SnsApiManager.apiResponseData.patientName),
            effectiveDateTime =  observationData.timestamp,
            device =  Device("${observationData.deviceReference}/${SnsApiManager.IMEI}", observationData.vitalDisplay),
            valueQuantity =  ValueQuantity(observationData.value, observationData.valueUnit, observationData.valueQuantitySystem, observationData.valueQuantityCode)*/
            resourceType = observationData.resourceType,
            id = observationData.uuId,
            meta = Meta(listOf(hl7VitalSignUrl)),
            status = observationData.status,
            category = listOf(Category(listOf(Coding(hl7SysValue, observationData.categoryCode, observationData.categoryDisplay)), observationData.categoryDisplay)),
            code = Code(listOf(Coding(loincSysValue, observationData.vitalCode, observationData.vitalDisplay)), observationData.vitalDisplay),
            subject = Subject("${observationData.subjectReference}/${SnsApiManager.apiResponseData.patient.patientID1}", SnsApiManager.apiResponseData.patient.firstName),
            effectiveDateTime = observationData.timestamp,
            performer = listOf(
                Performer(
                    reference = "Practitioner/f005",
                    display = "A. Langeveld"
                )
            ),
            device = Device("${observationData.deviceReference}/${IomtApiManager.DEVICE_UUID}", observationData.vitalDisplay),
            component = listOf( // Add component list here
                Component(
                    code = Code(listOf(Coding(loincSysValue, observationData.vitalCode, observationData.vitalDisplay)), observationData.vitalDisplay),
                    valueQuantity = ValueQuantity(observationData.value, observationData.valueUnit, observationData.valueQuantitySystem, observationData.valueQuantityCode)
                )
            )
        )

        val gson = GsonBuilder().create()
        return gson.toJson(observation)
    }

}