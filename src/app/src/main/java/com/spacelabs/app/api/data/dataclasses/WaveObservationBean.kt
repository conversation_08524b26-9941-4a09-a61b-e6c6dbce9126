package com.spacelabs.app.api.data.dataclasses

data class WaveObservationBean(
    var origin: Int,
    var period: Double,
    var factor: Double,
    var originalUpperLimit: Double,
    var originalLowerLimit: Double,
    var apiUpperLimit: Int,
    var apiLowerLimit: Int,
    val dimensions: Int = 1,
    var categoryCode: Map<String, String> = mapOf("system" to "http://terminology.hl7.org/CodeSystem/observation-category", "code" to "procedure", "display" to "Procedure"),
    var code: Map<String, String>,
    var deviceDisplay: String,
    val componentCodeSystem: String = "urn:oid:2.16.840.1.113883.6.24",
    var componentCode: String,
    var componentDisplay: String = String(),
    var timestamp: String = String(),
    var uuId: String = String()
)