package com.spacelabs.app.activities.fragments.childfragments

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.Spinner
import android.widget.Switch
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.sibelhealth.bluetooth.sensor.bloodpressuremonitor.bp2bloodpressuremonitor.BP2DeviceStatus
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.ParamSettingsFragment
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.AlarmDaoHelper
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.sensor.SibelSensorManager
import java.util.AbstractMap

class BpSettingsFragment(val settingsHelper: ParamSettingsHelper): Fragment(R.layout.settings_dialog_bp) {

    //Ui Variables
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private lateinit var bpAlarmSwitch: Switch

    private lateinit var bpSysHigh: EditText
    private lateinit var highSysIncBtn: AppCompatButton
    private lateinit var highSysDecBtn: AppCompatButton

    private lateinit var bpSysLow: EditText
    private lateinit var lowSysIncBtn: AppCompatButton
    private lateinit var lowSysDecBtn: AppCompatButton

    private lateinit var bpDiaHigh: EditText
    private lateinit var highDiaIncBtn: AppCompatButton
    private lateinit var highDiaDecBtn: AppCompatButton

    private lateinit var bpDiaLow: EditText
    private lateinit var lowDiaIncBtn: AppCompatButton
    private lateinit var lowDiaDecBtn: AppCompatButton
    private lateinit var bpStartButton: AppCompatButton
    private lateinit var bpMeasuringStatusTv: TextView

    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private lateinit var autoBpSwitch: Switch
    private lateinit var spinnerLayout: LinearLayout
    private lateinit var timerSpinner: Spinner

    //Normal Variables
    private lateinit var settingDaoHelper: SettingDaoHelper
    private lateinit var alarmDaoHelper: AlarmDaoHelper
    private lateinit var bpSysParametersDao: AlarmParametersDao
    private lateinit var bpDiaParametersDao: AlarmParametersDao
    private lateinit var incButtonsAndEditTexts: Map<AppCompatButton, EditText>
    private lateinit var decButtonsAndEditTexts: Map<AppCompatButton, EditText>
    private lateinit var  sharedPreferences: SharedPreferences

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        settingDaoHelper = SettingDaoHelper(context!!)
        alarmDaoHelper = AlarmDaoHelper(context!!)

        bpSysParametersDao = CommonDataArea.bpSysAlarmDao
        bpDiaParametersDao = CommonDataArea.bpDiaAlarmDao
        sharedPreferences = context?.getSharedPreferences("settings", Context.MODE_PRIVATE)!!

        bpAlarmSwitch = view.findViewById(R.id.bpAlarmSwitch)

        bpSysHigh = view.findViewById(R.id.highBpSys)
        highSysIncBtn = view.findViewById(R.id.highSysInc)
        highSysDecBtn = view.findViewById(R.id.highSysDec)

        bpSysLow = view.findViewById(R.id.lowBpSys)
        lowSysIncBtn = view.findViewById(R.id.lowSysInc)
        lowSysDecBtn = view.findViewById(R.id.lowSysDec)

        bpDiaHigh = view.findViewById(R.id.highBpDia)
        highDiaIncBtn = view.findViewById(R.id.highDiaInc)
        highDiaDecBtn = view.findViewById(R.id.highDiaDec)

        bpDiaLow = view.findViewById(R.id.lowBpDia)
        lowDiaIncBtn = view.findViewById(R.id.lowDiaInc)
        lowDiaDecBtn = view.findViewById(R.id.lowDiaDec)
        bpStartButton = view.findViewById(R.id.bpStartButton)
        bpMeasuringStatusTv = view.findViewById(R.id.bpMeasuringStatusTv)

        autoBpSwitch = view.findViewById(R.id.autoBpSwitch)
        spinnerLayout = view.findViewById(R.id.spinnerLayout)
        timerSpinner = view.findViewById(R.id.timer_spinner)

        incButtonsAndEditTexts = mapOf(highSysIncBtn to bpSysHigh, lowSysIncBtn to bpSysLow, highDiaIncBtn to bpDiaHigh, lowDiaIncBtn to bpDiaLow)
        decButtonsAndEditTexts = mapOf(highSysDecBtn to bpSysHigh, lowSysDecBtn to bpSysLow, highDiaDecBtn to bpDiaHigh, lowDiaDecBtn to bpDiaLow)
        uiActionListeners()
    }

    override fun onStart() {
        super.onStart()
        postUiInitActions()
        CommonEventHandler.registerUiEventCallback(BpSettingsUiHandler(),this::class.java)
    }
    override fun onStop() {
        super.onStop()
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
    }

    fun postUiInitActions() {
        manageAutoBpSpinnerVisibility(CommonDataArea.autoBpMode)

        autoBpSwitch.isChecked = CommonDataArea.autoBpMode
        bpAlarmSwitch.isChecked = bpSysParametersDao.alarmStatus

        settingsHelper.switchStyleChangeOnCheckedChange(bpAlarmSwitch, bpSysParametersDao.alarmStatus)
        settingsHelper.switchStyleChangeOnCheckedChange(autoBpSwitch, CommonDataArea.autoBpMode)

        bpSysHigh.setText("${bpSysParametersDao.highValue.toInt()}")
        bpSysLow.setText("${bpSysParametersDao.lowValue.toInt()}")
        bpDiaHigh.setText("${bpDiaParametersDao.highValue.toInt()}")
        bpDiaLow.setText("${bpDiaParametersDao.lowValue.toInt()}")

        settingsHelper.setupTimerSpinner(CommonDataArea.bpMeasurementPeriodicTime, timerSpinner)
    }

    fun uiActionListeners() {
        bpAlarmSwitch.setOnCheckedChangeListener { _, isChecked ->
            if(CommonDataArea.bpSensor?.isConnected == true) {
                bpSysParametersDao.alarmStatus = isChecked
                bpDiaParametersDao.alarmStatus = isChecked
                alarmDaoHelper.updatePatientAlarmToDb(bpSysParametersDao)
                alarmDaoHelper.updatePatientAlarmToDb(bpDiaParametersDao)
                settingsHelper.uiEventOnSwitchActionForBP(bpSysParametersDao, isChecked)
                settingsHelper.uiEventOnSwitchActionForBP(bpDiaParametersDao, isChecked)

                if(!isChecked)
                    settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.BpTimer, CommonDataArea.bpMeasurementPeriodicTime)


                settingsHelper.switchStyleChangeOnCheckedChange(bpAlarmSwitch, isChecked)
                if(CommonDataArea.bpSensor != null)
                    SibelSensorManager.sensorManagerHelper.measurementDataStreamController(isChecked, CommonDataArea.bpSensor as Sensor, arrayOf(
                        StreamDataType.BP_SYS, StreamDataType.BP_DIA))
            } else {
                bpAlarmSwitch.isChecked = !isChecked
                Toast.makeText(context, "Action Not Allowed, No Sensor Found", Toast.LENGTH_SHORT).show()
            }
        }

        autoBpSwitch.setOnCheckedChangeListener { _, isChecked ->
            settingsHelper.switchStyleChangeOnCheckedChange(autoBpSwitch, isChecked)
            onAtoBpSwitchStateChanged(isChecked)
        }

        settingsHelper.incDecButtonsActionListeners(incButtonsAndEditTexts, decButtonsAndEditTexts, 1)

        bpStartButton.setOnClickListener {
            startBpMeasurement()
        }

        val sysHighEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(bpSysHigh, ParamSettingsHelper.SettingsType.High)
        val sysLowEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(bpSysLow, ParamSettingsHelper.SettingsType.Low)
        settingsHelper.settingsValueTextChangedActionListener(sysHighEditText, bpSysParametersDao, alarmDaoHelper)
        settingsHelper.settingsValueTextChangedActionListener(sysLowEditText, bpSysParametersDao, alarmDaoHelper)

        val diaHighEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(bpDiaHigh, ParamSettingsHelper.SettingsType.High)
        val diaLowEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(bpDiaLow, ParamSettingsHelper.SettingsType.Low)
        settingsHelper.settingsValueTextChangedActionListener(diaHighEditText, bpDiaParametersDao, alarmDaoHelper)
        settingsHelper.settingsValueTextChangedActionListener(diaLowEditText, bpDiaParametersDao, alarmDaoHelper)

        timerSpinner.onItemSelectedListener = settingsHelper.onTimerSpinnerClickAction(
            CommonDataArea.bpMeasurementPeriodicTime, SettingDaoHelper.AppSettings.BpTimer)
        bpMeasuringStatusUpdate()
    }

    private fun startBpMeasurement(){
        if(CommonDataArea.bpSensor == null || !CommonDataArea.bpSensor!!.isConnected || !CommonDataArea.bpSysAlarmDao.alarmStatus)
            return

        if (CommonDataArea.bpSensor!!.deviceStatus != BP2DeviceStatus.BP_MEASURING){
            CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.StartManualBp, null)
        }
    }

    private fun onAtoBpSwitchStateChanged(isChecked: Boolean){
        val value = if(isChecked) "ON" else "OFF"
        settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.AutoBp, value)
        manageAutoBpSpinnerVisibility(isChecked)
    }

    private fun manageAutoBpSpinnerVisibility(isOnAutoBp: Boolean){
        if(isOnAutoBp)
            spinnerLayout.visibility = View.VISIBLE
        else
            spinnerLayout.visibility = View.GONE
    }
    private fun bpMeasuringStatusUpdate() {
        Log.d("bpStatus", "bpMeasuringStatusUpdate: ${CommonDataArea.bpTime}")
        if (CommonDataArea.bpTime == "Measuring..."){
            bpMeasuringStatusTv.visibility = View.VISIBLE
        }else{
            bpMeasuringStatusTv.visibility = View.GONE
        }
    }

    inner class BpSettingsUiHandler: UiEventCallback {
        override fun uiEvent(event: UiEventCallback.UiEventType, eventData: Any?) {
            when(event) {
                UiEventCallback.UiEventType.VitalCompanionText -> bpMeasuringStatusUpdate()
                else -> return
            }
        }

    }
}