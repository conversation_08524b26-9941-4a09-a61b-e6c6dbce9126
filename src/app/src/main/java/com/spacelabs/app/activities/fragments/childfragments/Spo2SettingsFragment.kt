package com.spacelabs.app.activities.fragments.childfragments

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.widget.EditText
import android.widget.Switch
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.fragment.app.Fragment
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.alarms.alarmDAO.AlarmParametersDao
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.AlarmDaoHelper
import java.util.AbstractMap

class Spo2SettingsFragment(val parameterHelper: ParamSettingsHelper): Fragment(R.layout.settings_dialog_spo2) {

    //Ui Variables
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private lateinit var spo2AlarmSwitch: Switch

    private lateinit var spo2High: EditText
    private lateinit var highIncButton: AppCompatButton
    private lateinit var highDecButton: AppCompatButton

    private lateinit var spo2Low: EditText
    private lateinit var lowIncButton: AppCompatButton
    private lateinit var lowDecButton: AppCompatButton

    //Normal Variables
    private lateinit var alarmDaoHelper: AlarmDaoHelper
    private lateinit var alarmParametersDao: AlarmParametersDao
    private lateinit var incButtons: Map<AppCompatButton, EditText>
    private lateinit var decButtons: Map<AppCompatButton, EditText>

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        alarmDaoHelper = AlarmDaoHelper(context!!)
        alarmParametersDao = CommonDataArea.spo2AlarmDao

        spo2AlarmSwitch = view.findViewById(R.id.spo2AlarmSwitch)

        spo2High = view.findViewById(R.id.spo2High)
        highIncButton = view.findViewById(R.id.spo2HighIncButton)
        highDecButton = view.findViewById(R.id.spo2HighDecButton)

        spo2Low = view.findViewById(R.id.spo2Low)
        lowIncButton = view.findViewById(R.id.spo2LowIncButton)
        lowDecButton = view.findViewById(R.id.spo2LowDecButton)

        postUiInitActions()
        uiActionListeners()
    }

    fun postUiInitActions() {
        spo2AlarmSwitch.isChecked = alarmParametersDao.alarmStatus

        if(alarmParametersDao.alarmStatus){
            spo2AlarmSwitch.thumbTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
            spo2AlarmSwitch.trackTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
        }else{
            spo2AlarmSwitch.thumbTintList =
                context?.resources?.getColorStateList(R.color.white, context!!.theme)
            spo2AlarmSwitch.trackTintList =
                context?.resources?.getColorStateList(R.color.white, context!!.theme)
        }

        incButtons = mapOf(highIncButton to spo2High, lowIncButton to spo2Low)
        decButtons = mapOf(highDecButton to spo2High, lowDecButton to spo2Low)

        spo2High.setText("${alarmParametersDao.highValue.toInt()}")
        spo2Low.setText("${alarmParametersDao.lowValue.toInt()}")
    }

    fun uiActionListeners() {
        spo2AlarmSwitch.setOnCheckedChangeListener { _, isChecked ->
            val streamTypes = arrayOf(StreamDataType.SPO2, StreamDataType.PPG_IR)
            val hasActionCompleted = parameterHelper.alarmSwitchActionListener(spo2AlarmSwitch, isChecked, streamTypes, CommonDataArea.limbSensor as Sensor?)
            if(hasActionCompleted) {
                alarmParametersDao.alarmStatus = isChecked
                alarmDaoHelper.updatePatientAlarmToDb(alarmParametersDao)
                parameterHelper.uiEventOnSwitchAction(alarmParametersDao)
            } else {
                spo2AlarmSwitch.isChecked = !isChecked
                Toast.makeText(context, "Action Not Allowed, No Sensor Found", Toast.LENGTH_SHORT).show()
            }
        }

        parameterHelper.incDecButtonsActionListeners(incButtons, decButtons, 1)

        val highEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(spo2High, ParamSettingsHelper.SettingsType.High)
        val lowEditText: Map.Entry<EditText, ParamSettingsHelper.SettingsType> = AbstractMap.SimpleEntry(spo2Low, ParamSettingsHelper.SettingsType.Low)
        parameterHelper.settingsValueTextChangedActionListener(highEditText, alarmParametersDao, alarmDaoHelper)
        parameterHelper.settingsValueTextChangedActionListener(lowEditText, alarmParametersDao, alarmDaoHelper)
    }

}