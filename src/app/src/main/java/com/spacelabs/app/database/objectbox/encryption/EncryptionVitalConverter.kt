package com.spacelabs.app.database.objectbox.encryption

import io.objectbox.converter.PropertyConverter

class EncryptionVitalConverter : PropertyConverter<Double, ByteArray> {

    override fun convertToDatabaseValue(entityProperty: Double): ByteArray? {
        return AESVitalCrypt.encrypt(entityProperty)
    }

    override fun convertToEntityProperty(databaseValue: ByteArray?): Double? {
        return if (databaseValue != null) {
            AESVitalCrypt.decrypt(databaseValue)
        } else {
            null
        }
    }
}
