package com.spacelabs.app.sensor.helper

import android.os.Handler
import android.os.Looper
import com.spacelabs.app.R
import com.sibelhealth.bluetooth.sensor.sibel.limb.LimbSensor
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.status.ConnectionStatus
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.charts.XYDataSource
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.sensor.SibelSensorManager

class LimbSensorObserverHelper: SensorObserverHelper() {

    override fun onSensorConnectingAction(sensor: Sensor) {
        if(isSensorSavedOnTheSharedPreference(sensor))
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorConnecting, sensor.sensorType)
    }

    override fun onSensorConnectedAction(sensor: Sensor) {
        if(!isSensorSavedOnTheSharedPreference(sensor)) {
            sensor.disconnect(true)
            return
        }
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.CONNECTED)

        patientDataManager.updateSensorsAgainstPatient(sensor)

        val spo2StreamArray = arrayOf(StreamDataType.SPO2, StreamDataType.PPG_IR, StreamDataType.FALL_COUNT)

        SibelSensorManager.sensorManagerHelper.measurementDataStreamController(CommonDataArea.spo2AlarmDao.alarmStatus, sensor, spo2StreamArray)
        SibelSensorManager.sensorManagerHelper.measurementDataStreamController(CommonDataArea.tempAlarmDao.alarmStatus, sensor, arrayOf(StreamDataType.TEMP_SKIN))

        val limbSensor: LimbSensor = sensor as LimbSensor
        CommonDataArea.limbSensor = limbSensor

        updateSensorConnectedTime(sensor.sensorType)
        LogWriter.writeSensorLog("Limb  sensor Connected", sensor.name)
    }

    override fun onSensorConnectionLostAction(sensor: Sensor) {
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.CONNECTION_LOST)
        CommonDataArea.limbSensorConnectingStatus = SibelSensorManager.SensorScanMode.Reconnecting
        val savedSensorName = SibelSensorManager.getEnrolledSensorNameByType(mnActivity.applicationContext!!, sensor.sensorType)
        if(!savedSensorName.equals(sensor.name)){
            sensor.disconnect(true)
            return
        }

        Handler(Looper.getMainLooper()).postDelayed({
            SibelSensorManager.sensorManagerHelper.reconnectWhenInRange(
                sensor
            )
        }, 60000)
        LogWriter.writeSensorLog("Limb Connection lost", sensor.name)
    }

    override fun onSensorDisconnectAction(sensor: Sensor) {
        sensorConnectionUiUpdate(sensor.sensorType, ConnectionStatus.DISCONNECTED)
        LogWriter.writeSensorLog("Limb DisConnected",""+sensor.name)
    }

    fun onPoorSkinContactStatusUpdatedAction(isPoorSkinContact: Boolean, limbSensor: LimbSensor){
        try {
            val poorSkinMessage = mnActivity.getString(R.string.poorSkinContact)
            onAttachDetachStatusUpdate(isPoorSkinContact, limbSensor.sensorType, poorSkinMessage)
        } catch (e: Exception) {
            LogWriter.writeExceptLog("onPoorSkinContactStatusUpdated", e.message)

        }
    }

    fun resetPointerAndMasterStreamForPoorSkinContact(isPoorSkinContact: Boolean) {
        if (isPoorSkinContact){
            CommonDataArea.isPPGWritePtrUpdated = false
            CommonDataArea.masterStream = XYDataSource.XYDataType.ECG.name
        }
    }

}