package com.spacelabs.app.activities.snsActivities.helpers

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * Debug logger for QR code analysis that writes to local files
 * when device debugging is not available
 */
class QrDebugLogger(private val context: Context) {
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    private val fileNameFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    companion object {
        private const val LOG_DIR = "qr_debug_logs"
        private const val MAX_LOG_FILES = 7 // Keep logs for 7 days
        private const val TAG = "QrDebugLogger"
    }
    
    private fun getLogDirectory(): File {
        val logDir = File(context.filesDir, LOG_DIR)
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        return logDir
    }
    
    private fun getCurrentLogFile(): File {
        val logDir = getLogDirectory()
        val fileName = "qr_debug_${fileNameFormat.format(Date())}.log"
        return File(logDir, fileName)
    }
    
    fun logQrAnalysis(
        rawData: String,
        detectedFormat: String,
        extractedFields: Map<String, String?>,
        errors: List<String>,
        success: Boolean
    ) {
        try {
            val timestamp = dateFormat.format(Date())
            val logEntry = buildString {
                appendLine("=" * 80)
                appendLine("QR CODE ANALYSIS - $timestamp")
                appendLine("=" * 80)
                appendLine("SUCCESS: $success")
                appendLine()
                appendLine("RAW DATA:")
                appendLine(rawData)
                appendLine()
                appendLine("DETECTED FORMAT: $detectedFormat")
                appendLine()
                appendLine("EXTRACTED FIELDS:")
                extractedFields.forEach { (key, value) ->
                    appendLine("  $key: ${value ?: "null"}")
                }
                appendLine()
                if (errors.isNotEmpty()) {
                    appendLine("ERRORS:")
                    errors.forEach { error ->
                        appendLine("  - $error")
                    }
                    appendLine()
                }
                appendLine("END OF ANALYSIS")
                appendLine()
            }
            
            writeToFile(logEntry)
            Log.d(TAG, "QR analysis logged to file")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write QR analysis to file: ${e.message}")
        }
    }
    
    fun logError(message: String, exception: Throwable? = null) {
        try {
            val timestamp = dateFormat.format(Date())
            val logEntry = buildString {
                appendLine("ERROR - $timestamp")
                appendLine("Message: $message")
                if (exception != null) {
                    appendLine("Exception: ${exception.message}")
                    appendLine("Stack trace:")
                    appendLine(exception.stackTraceToString())
                }
                appendLine()
            }
            
            writeToFile(logEntry)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write error to file: ${e.message}")
        }
    }
    
    private fun writeToFile(content: String) {
        try {
            val logFile = getCurrentLogFile()
            FileWriter(logFile, true).use { writer ->
                writer.write(content)
                writer.flush()
            }
            
            // Clean up old log files
            cleanupOldLogs()
            
        } catch (e: IOException) {
            Log.e(TAG, "IOException writing to log file: ${e.message}")
        }
    }
    
    private fun cleanupOldLogs() {
        try {
            val logDir = getLogDirectory()
            val files = logDir.listFiles() ?: return
            
            val cutoffTime = System.currentTimeMillis() - (MAX_LOG_FILES * 24 * 60 * 60 * 1000L)
            
            files.filter { it.lastModified() < cutoffTime }.forEach { file ->
                if (file.delete()) {
                    Log.d(TAG, "Deleted old log file: ${file.name}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up old logs: ${e.message}")
        }
    }
    
    fun getLogFiles(): List<File> {
        return try {
            val logDir = getLogDirectory()
            logDir.listFiles()?.sortedByDescending { it.lastModified() }?.toList() ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting log files: ${e.message}")
            emptyList()
        }
    }
    
    fun getLatestLogContent(): String {
        return try {
            val latestFile = getLogFiles().firstOrNull()
            latestFile?.readText() ?: "No log files found"
        } catch (e: Exception) {
            Log.e(TAG, "Error reading latest log: ${e.message}")
            "Error reading log file: ${e.message}"
        }
    }
    
    fun clearLogs() {
        try {
            val logDir = getLogDirectory()
            logDir.listFiles()?.forEach { file ->
                file.delete()
            }
            Log.d(TAG, "All log files cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing logs: ${e.message}")
        }
    }
}

private operator fun String.times(n: Int): String = this.repeat(n)
