package com.spacelabs.app.activities.iomtActivities

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.google.zxing.integration.android.IntentIntegrator
import com.google.zxing.integration.android.IntentResult
import com.spacelabs.app.R
import com.spacelabs.app.activities.iomtActivities.activityUi.QrCodeActivityUi
import com.spacelabs.app.activities.iomtActivities.helpers.QrCodeConfigHelper
import com.spacelabs.app.activities.iomtActivities.helpers.QrSecurityAnalyzer
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.iomt.IomtApiManager
import com.spacelabs.app.iomt.NetworkUtils
import com.spacelabs.app.iomt.apiHandler.AuthenticationHandler
import com.spacelabs.app.ui.dialogs.ProgressDialogHandler

class QrScannerActivity : AppCompatActivity() {

    private lateinit var btnScan: Button
    private lateinit var uuidTextView: TextView
    private val qrConfigHelper = QrCodeConfigHelper(this)
    private val authenticationHandler = AuthenticationHandler()
    private val qrCodeActivityUi = QrCodeActivityUi(this)
    private var companyID: String = ""
    private var tenantID: String = ""
    private val progressDialogHandler = ProgressDialogHandler(this)
    private val deviceUuid = IomtApiManager.DEVICE_UUID
    //private lateinit var qrCodeAnalyzer: QrCodeAnalyzer   /*Old way of scanning and handle data */
    private lateinit var qrSecurityAnalyzer: QrSecurityAnalyzer  /*New Encrypted Qr Code Scanning and handling*/

    private val scanResultLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            handleActivityResult(result.resultCode, result.data)
        }

    @SuppressLint("MissingInflatedId")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_qr_scanner)
        uuidTextView = findViewById(R.id.uuidEditText)
        btnScan = findViewById(R.id.btnScan)
        btnScan.setOnClickListener { initiateScan() }
        uiActionListeners()
    }

    fun uiActionListeners() {
       // qrCodeAnalyzer = QrCodeAnalyzer(this,qrConfigHelper,progressDialogHandler)/*Old way of scanning and handle data */
        qrSecurityAnalyzer = QrSecurityAnalyzer(this,qrConfigHelper,progressDialogHandler)/*New Encrypted Qr Code Scanning and handling*/
        handleUUIDVisibility(deviceUuid)
    }

    private fun handleUUIDVisibility(deviceUuid:String){
        val uuidParts = deviceUuid.split("-")
        val firstPart = uuidParts.first().take(4) + "x".repeat(uuidParts.first().length - 4)
        val lastPart = "x".repeat(uuidParts.last().length - 6) + uuidParts.last().takeLast(6)
        // Replace inner parts with 'x'
        val modifiedParts = uuidParts.mapIndexed { index, part ->
            when (index) {
                0 -> firstPart
                uuidParts.size - 1 -> lastPart
                else -> "x".repeat(part.length)
            }}
        val modifiedUuid = modifiedParts.joinToString("-")
        // Display the modified UUID
        uuidTextView.text = modifiedUuid

        //encryptionTesting()
    }

    /* Function to Hardcode encryted string and test*/
   /*

    fun encryptionTesting(){
        val encryptedString = "kLW8u+GxmHM6y4VBGwfJxIP7sMXKjMdJbss5/ApJ5Q63v8ag1UxX1zyt5Op4TDoVka5/MKS6NeGjo5vky77DfZ+w3VinCOhA7TMn+H4qZWgP8DkpuBzoFCtzIO8osgiWJDkXywF3j9PePmDCKXU7qtJspyOLJP+O6Thduby0vhtc/KKrnWXZnIgEt9YCs2Kpfiek2u5etfh/zJNvRl+ce2bDcphf6tqRdYOo/QN633MEHlM6A5UeF+nmrKGIFvgN"
        val passphrase = IomtApiManager.DEVICE_PASSPHRASE

        val decryptedString = decryptWithPassphrase(encryptedString, passphrase)
        Log.d("Decrypted String:","Data : $decryptedString")
        showToast(decryptedString)
    }*/
    private fun initiateScan() {
        if (NetworkUtils.isNetworkAvailable(this)) {
            val integrator = IntentIntegrator(this)
            integrator.setOrientationLocked(false) // Allow rotation
            integrator.setPrompt("Scan a QR Code")
            integrator.setBeepEnabled(true)
            integrator.setBarcodeImageEnabled(true)
            scanResultLauncher.launch(integrator.createScanIntent())
        } else {
            showToast("No internet connection.")
        }
    }

    private fun handleActivityResult(resultCode: Int, data: Intent?) {
        val result: IntentResult? =
            IntentIntegrator.parseActivityResult(resultCode, data)
        if (result != null) {
            if (result.contents == null) {
                Toast.makeText(this, "Cancelled", Toast.LENGTH_SHORT).show()
            } else {
                progressDialogHandler.showProgressDialog("Loading...")
                //qrCodeAnalyzer.handleScannedData(result.contents)  /*Old way of scanning and handle data */

                qrSecurityAnalyzer.handleEncScannedData(result.contents)  /*New Encrypted Qr Code Scanning and handling*/
               // handleEncScannedData(result.contents)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        CommonEventHandler.registerUiEventCallback(
            qrCodeActivityUi.UiEventHandler(),
            this::class.java
        )
    }

    override fun onResume() {
        super.onResume()
        qrConfigHelper.navigateIfAccountIdExists()
    }

    override fun onStop() {
        super.onStop()
        CommonEventHandler.unRegisterUiEventCallback(this::class.java)
    }

    private fun showToast(message: String) {
        Toast.makeText(applicationContext, message, Toast.LENGTH_SHORT).show()
    }
}
