package com.spacelabs.app.database.relationships

import androidx.room.Embedded
import androidx.room.Relation
import com.spacelabs.app.database.entities.DefaultAlarmSettingsTable
import com.spacelabs.app.database.entities.ParametersTable

data class ParametersAndDefaultAlarmSettings(
    @Embedded val parameter: ParametersTable,
    @Relation(
        parentColumn = "paramId",
        entityColumn = "paramId"
    )
    val defaultAlarmSettings: DefaultAlarmSettingsTable?
)