package com.spacelabs.app.activities.fragments.fragmenthelpers

import android.content.Context
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatCheckBox
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.R

class ManageSensorsFragmentHelper(val context: Context?) {

    //UI variables
    private lateinit var chestBtn: AppCompatButton
    private lateinit var limbBtn: AppCompatButton
    private lateinit var bpBtn: AppCompatButton

    private lateinit var connectedChestUis: ConnectedSensorUi
    private lateinit var connectedLimbUi: ConnectedSensorUi
    private lateinit var connectedBpSensorUi: ConnectedSensorUi

    private lateinit var removeBtn: AppCompatButton

    //Normal Variables
    lateinit var adapter: SensorRecyclerViewAdapter2

    fun initMngSensorUi(view: View) {
        adapter = SensorRecyclerViewAdapter2(mutableListOf())

        chestBtn = view.findViewById(R.id.chestBtn)
        limbBtn = view.findViewById(R.id.limbBtn)
        bpBtn = view.findViewById(R.id.bpBtn)

        removeBtn = view.findViewById(R.id.remove)
        connectedChestUis = ConnectedSensorUi(
            SensorType.CHEST,
            view.findViewById(R.id.chestBoxOuter),
            view.findViewById(R.id.chestText),
            view.findViewById(R.id.chestText),
            view.findViewById(R.id.chestBox)
        )
        connectedLimbUi = ConnectedSensorUi(
            SensorType.LIMB,
            view.findViewById(R.id.limbBoxOuter),
            view.findViewById(R.id.limbText),
            view.findViewById(R.id.limbText),
            view.findViewById(R.id.limbBox)
        )
        connectedChestUis = ConnectedSensorUi(
            SensorType.BP2_BLOOD_PRESSURE_MONITOR,
            view.findViewById(R.id.bpBoxOuter),
            view.findViewById(R.id.bpText),
            view.findViewById(R.id.bpText),
            view.findViewById(R.id.bpBox)
        )

        sensorUiActionListeners()
    }

    private fun sensorUiActionListeners() {
        filterButtonActionListeners(listOf(chestBtn, limbBtn, bpBtn))
    }

    private fun filterButtonActionListeners(buttons: List<AppCompatButton>) {
        buttons.forEach {
            val type = when(it) {
                chestBtn -> SensorType.CHEST
                limbBtn -> SensorType.LIMB
                bpBtn -> SensorType.BP2_BLOOD_PRESSURE_MONITOR
                else -> return@forEach
            }

            it.setOnClickListener {
                adapter.recycleViewByType(type)
                buttons.forEach { btn ->
                    val color = if(btn != it)
                        context?.resources?.getColorStateList(R.color.components_gray, context.theme)
                    else
                        context?.resources?.getColorStateList(R.color.lightBlue, context.theme)

                    btn.backgroundTintList = color
                }
            }
        }
    }

    fun onSensorConnecting(sensorType: SensorType) {
        val sensorUi = when(sensorType) {
            SensorType.CHEST -> connectedChestUis
            SensorType.LIMB -> connectedChestUis
            SensorType.BP2_BLOOD_PRESSURE_MONITOR -> connectedBpSensorUi
            else -> return
        }

        
    }

    private data class ConnectedSensorUi(
        val sensorType: SensorType,
        val outerBox: LinearLayout,
        val sensorText: TextView,
        val connectivityText: TextView,
        val checkBox: AppCompatCheckBox,
    )

}