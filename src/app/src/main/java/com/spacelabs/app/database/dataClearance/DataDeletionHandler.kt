package com.spacelabs.app.database.dataClearance

import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresApi
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.database.daoHelper.DbDaoHelperManager
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.database.objectbox.dao.AlarmBoxDao
import com.spacelabs.app.database.objectbox.helpers.MeasurementDataDaoHelper
import kotlinx.coroutines.*
import java.lang.Runnable
import java.sql.SQLException
import java.time.Duration

class DataDeletionHandler(context: Context) : DbDaoHelperManager(context) {
    private var handler: Handler? = null
    private var runnable: Runnable? = null
    private var dbClearTick: String = ""
    private var currentTimeTick: Long? = null
    private var dbDeletionTimerTick: Long? = null
    private val settingDaoHelper = SettingDaoHelper(context)
    private var dbDeletionTimer: String = ""
    private val measurementDataDaoHelper = MeasurementDataDaoHelper()
    private  val alarmBoxDao=AlarmBoxDao()

    //to start deletion handling.
    @RequiresApi(Build.VERSION_CODES.S)
    fun startDeletionPeriodicTask() {
        try {
            handler = Handler(Looper.getMainLooper())
            runnable = Runnable {
                // Get the long value representing milliseconds since the Unix epoch
                currentTimeTick = TimestampUtils.getCurrentTimeMillis()
                //to check and get the  tick value
                getTickValues()
                //to delete the data from db
                dataDeletionHandler(dbClearTick)
            }
            handler?.post(runnable!!)

        } catch (ex: SQLException) {
            LogWriter.writeExceptLog("DeletionPeriodicTask", ex.stackTraceToString())
            Log.d("DeletionPeriodicTask", ex.stackTraceToString())
        }
    }


    @RequiresApi(Build.VERSION_CODES.S)
    private fun dataDeletionHandler(dbClearTick: String) {
        deleteDBData(dbClearTick)
        handler?.postDelayed(runnable!!, 20000)
    }

    // to get Tick Values.
    @RequiresApi(Build.VERSION_CODES.S)
    private fun getTickValues() {
        Log.d("data deletion",CommonDataArea.dataDeletionTimer)
        dbDeletionTimer = CommonDataArea.dataDeletionTimer
        val value =
            dbDeletionTimer.substring(0, dbDeletionTimer.length - 1)
                .toLong()
        dbDeletionTimerTick = when (val unit = dbDeletionTimer.last().toString()) {
            "m" -> Duration.ofMinutes(value).toMillis() // Convert minutes to milliseconds
            "h" -> Duration.ofHours(value).toMillis() // Convert hours to milliseconds
            "d" -> Duration.ofDays(value).toMillis()//Convert days to milliseconds
            else -> throw IllegalArgumentException("Invalid time interval unit: $unit")
        }
        dbClearTick = (currentTimeTick!! - dbDeletionTimerTick!!).toString()
    }

    // Call the deleteDBData() method on the Dao instance
    private fun deleteDBData(dbClearTick: String) {
        dbCoroutine.launch {
            val activePatientId = measurementDao.getActivePatientId()
            if(activePatientId !=null) {
                patientDao.deleteALlPatientHistory(activePatientId)
                measurementDataDaoHelper.deleteMeasurementsData(dbClearTick.toLong())
                alarmBoxDao.deleteAlarmEventData(dbClearTick.toLong())
                alarmBoxDao.deleteAlarmData(activePatientId)
                eventsDao.deleteAllEventLog()
                alarmDao.deleteAllPatientAlarmEvent(activePatientId)
                visitDao.deleteAllVisit(activePatientId)
                sensorDao.deleteAllSensors(activePatientId)
                settingDaoHelper.updateDataClearanceTime(<EMAIL>)
            }
        }
    }

    // Call the deleteDBDataOnDischarge() method on discharge of patient to delete the local storage till the time of discharge
    fun deleteDBDataOnDischarge() {
        try {
        dbCoroutine.launch {
            val inactivePatientIds = patientDao.getInActivePatientId()
            if (inactivePatientIds.isNotEmpty()) {
                for (inactivePatientId in inactivePatientIds) {
                    alarmDao.deleteAllPatientAlarmEvent(inactivePatientId)
                    visitDao.deleteAllVisit(inactivePatientId)
                    patientDao.deleteALlPatientHistoryOnDischarge(inactivePatientId)
                    sensorDao.deleteAllSensorsOnDischarge(inactivePatientIds)
                }
                alarmBoxDao.deleteAlarmEventOnDischarge()
                measurementDataDaoHelper.deleteMeasurementsOnDischarge()
                alarmBoxDao.deleteAlarmOnDischarge()
                eventsDao.deleteEventList()
            }
        }
        } catch (ex: SQLException) {
            LogWriter.writeExceptLog("deleteDBDataOnDischarge", ex.stackTraceToString())
            Log.d("deleteDBDataOnDischarge", ex.stackTraceToString())
        }
    }
}


