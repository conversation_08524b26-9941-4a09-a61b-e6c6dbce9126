package com.spacelabs.app.activities.fragments.childfragments

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.Spinner
import android.widget.Switch
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.spacelabs.app.R
import com.spacelabs.app.activities.fragments.fragmenthelpers.ParamSettingsHelper
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.database.daoHelper.SettingDaoHelper
import com.spacelabs.app.sensor.SibelSensorManager

class ActivitySettingsFragment(val settingsHelper: ParamSettingsHelper): Fragment(R.layout.settings_dialog_activity) {

    // Ui Variables
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    private lateinit var fallSwitch: Switch
    private lateinit var modeRadioGroup: RadioGroup
    private lateinit var bedBoundRadioButton: RadioButton
    private lateinit var activeRadioButton: RadioButton
    private lateinit var turnTimerLabel: TextView
    private lateinit var timerSpinner: Spinner
    private var  sharedPreferences: SharedPreferences? = null

    private lateinit var settingDaoHelper: SettingDaoHelper

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        settingDaoHelper = SettingDaoHelper(context!!)
        sharedPreferences = context?.getSharedPreferences("settings", Context.MODE_PRIVATE)

        fallSwitch = view.findViewById(R.id.fallSwitch)
        modeRadioGroup = view.findViewById(R.id.modeRadioGroup)
        bedBoundRadioButton = view.findViewById(R.id.bedBoundRadioButton)
        activeRadioButton = view.findViewById(R.id.activeRadioButton)
        turnTimerLabel = view.findViewById(R.id.turnTimerLabel)
        timerSpinner = view.findViewById(R.id.activity_timer_spinner)

        uiActionListeners(view)
    }

    override fun onStart() {
        super.onStart()
        postUiInitActions()
    }

    fun postUiInitActions() {
        fallSwitch.isChecked = CommonDataArea.fallStream.equals("on", true)
        if (fallSwitch.isChecked) {
            fallSwitch.thumbTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
            fallSwitch.trackTintList =
                context?.resources?.getColorStateList(R.color.lightBlue, context!!.theme)
        } else {
            fallSwitch.thumbTintList = context?.resources?.getColorStateList(R.color.white, context!!.theme)
            fallSwitch.trackTintList = context?.resources?.getColorStateList(R.color.white, context!!.theme)
        }

        updateUIForMode(CommonDataArea.patientMode)
        settingsHelper.setupTimerSpinner(CommonDataArea.patientModePeriodicTime, timerSpinner)
    }

    fun uiActionListeners(view: View) {
        modeRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            val selectedRadioButton = view.findViewById<RadioButton>(checkedId)
            val selectedValue = selectedRadioButton?.text.toString()
            settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.PatientMode, selectedValue)
            updateUIForMode(selectedValue)
        }

        fallSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                fallSwitch.thumbTintList =
                    context?.resources?.getColorStateList(R.color.lightBlue, context?.theme)
                fallSwitch.trackTintList = context?.resources?.getColorStateList(R.color.lightBlue,
                    context?.theme
                )
                CommonDataArea.fallStream = "ON"
            } else {
                fallSwitch.thumbTintList = context?.resources?.getColorStateList(R.color.white,
                    context?.theme
                )
                fallSwitch.trackTintList =
                    context?.resources?.getColorStateList(R.color.white, context?.theme)
                CommonDataArea.fallStream = "OFF"
            }
            if(CommonDataArea.chestSensor != null)
                SibelSensorManager.sensorManagerHelper.measurementDataStreamController(isChecked, CommonDataArea.chestSensor as Sensor, arrayOf(
                    StreamDataType.FALL_COUNT))

            settingDaoHelper.updateSettings(SettingDaoHelper.AppSettings.FallStream, CommonDataArea.fallStream)
        }

        timerSpinner.onItemSelectedListener = settingsHelper.onTimerSpinnerClickAction(
            CommonDataArea.patientModePeriodicTime, SettingDaoHelper.AppSettings.TurnTimer)
    }

    private fun updateUIForMode(mode: String) {
        val timerVisibility = when (mode) {
            "Bed Bound" -> {
                bedBoundRadioButton.isChecked = true
                activeRadioButton.isChecked = false
                View.VISIBLE
            }
            "Active" -> {
                bedBoundRadioButton.isChecked = false
                activeRadioButton.isChecked = true
                View.GONE
            }
            else -> return
        }
        turnTimerLabel.visibility = timerVisibility
        timerSpinner.visibility = timerVisibility
    }
}