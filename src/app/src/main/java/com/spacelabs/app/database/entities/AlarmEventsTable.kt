package com.spacelabs.app.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblAlarmEvent")
data class AlarmEventsTable(
    @PrimaryKey(autoGenerate = true)
    val alarmEventId: Int?,         //why paramId as PK ?
//    val alarmUuid: String,
    val patientId: Int,             //PK from patient Table
    val patientID1: String,
    val defaultAlarmId: Int?,
    val patientAlarmId: Int?,
    val alarmName: String?,
    val triggeredBy: Int,
    val alarmCauseValue: Double,    //Double ?
    val valueOverLimit: Double?,
    val alarmStartTime: String,
    val alarmEndTime: String?,      //what if there are two or more alarms for the same parameter at the same time. how we know which ended first
    val duration: Int?,             //in seconds ?            //precedence or criticality ?
    @ColumnInfo(defaultValue = 0.toString())
    val isAcknowledged: Int,    //what for this parameter
    val timeOfAcknowledge: Long?,   // ?
    val silencedTime: String?,
//    val alarmStatus: String?,
    @ColumnInfo(defaultValue = 0.toString())
    var uploadStatus: Int?,
    var tickValue: Long?
)