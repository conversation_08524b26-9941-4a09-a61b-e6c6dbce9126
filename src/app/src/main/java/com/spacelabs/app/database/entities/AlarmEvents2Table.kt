package com.spacelabs.app.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblAlarmEvents2")
data class AlarmEvents2Table(
    @PrimaryKey(autoGenerate = true)
    val alarmEventId: Int?,
    val alarmEventUUID: String,
    val alarmId: Int,
    val value: Double,
    val updatedTime: String,
    val alarmStatus: String,
    @ColumnInfo(defaultValue = 0.toString())
    var uploadStatus: Int,
    var uploadTime: String?,
    var tickValue: Long?

)