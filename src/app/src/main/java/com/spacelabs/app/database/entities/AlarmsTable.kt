package com.spacelabs.app.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblAlarms")
data class AlarmsTable (
    @PrimaryKey(autoGenerate = true)
    val alarmId: Int?,
    val alarmUUID: String,
    val patientId: Int,
    val defaultAlarmId: Int?,
    val patientAlarmId: Int?,
    val alarmName: String,
    val limitExceeded: Double,
    val startTime: String,
    var endTime: String?,
    var duration: Int?,
    var isAcknowledged: Int,
    var acknowledgedTime: String?,
    var uploadStatus: Int
)