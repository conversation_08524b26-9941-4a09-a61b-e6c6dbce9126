package com.spacelabs.app.activities.iomtActivities.helpers

import android.content.Context
import android.util.Log
import com.spacelabs.app.activities.commonActivities.helpers.CommonActivityHelper
import com.spacelabs.app.activities.iomtActivities.AccountConfigActivity
import com.spacelabs.app.common.CommonDataArea.Companion.enableSnsApi
import com.spacelabs.app.database.daoHelper.SettingDaoHelper

class AccountConfigActivityHelper(accountConfigActivity: AccountConfigActivity): CommonActivityHelper(accountConfigActivity) {

    private val accountConfActivity: AccountConfigActivity
    private val context: Context

    init {
        this.accountConfActivity = accountConfigActivity
        context = accountConfigActivity
    }

    fun navigateIfAccountIdExists() {
        navigateIfHasAuthenticatedExist(SettingDaoHelper.AppSettings.HasAuthenticated.getValue())
        Log.d("DEVICE_EXIST", "navigateIfAccountIdExists: ${SettingDaoHelper.AppSettings.HasAuthenticated.getValue()}")
    }


    fun saveAccountIdSettingsAndNavigateToNext(value: String) {
        if (enableSnsApi) {
            saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.AccountId)

        } else {
            saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.HasAuthenticated)

        }

    }

    fun saveDeviceValues(key: Int, value: String) {
        when (key) {
            0 -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.DeviceUuid)
            else -> saveDeviceUuidOrPass(value, SettingDaoHelper.AppSettings.DevicePass)
        }
    }

      suspend fun checkDeviceValues(): Boolean {

        val isDeviceUuid = getDeviceUUidOrPass(SettingDaoHelper.AppSettings.DeviceUuid.key)

         return isDeviceUuid
    }
    fun saveDeviceRegisterSettings(value: String) {
        /*if (enableSnsApi) {
            saveSettingsAndNavigate(value, SettingDaoHelper.AppSettings.AccountId)

        } else {*/
            saveDeviceRegisteredSettings(value, SettingDaoHelper.AppSettings.HasAuthenticated)

       // }

    }

}