package com.spacelabs.app.common

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.GCMParameterSpec

class KeystoreManager(private val context: Context) {
    private val keystoreAlias = "my_key_alias"

    fun saveKeyToKeystore(value: String) {
        // Create a new KeyStore instance
        val keyStore = KeyStore.getInstance("AndroidKeyStore")
        keyStore.load(null)

        // Generate a new key if one doesn't already exist
        if (!keyStore.containsAlias(keystoreAlias)) {
            val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, "AndroidKeyStore")
            val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                keystoreAlias,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                .setKeySize(256)
                .build()
            keyGenerator.init(keyGenParameterSpec)
            keyGenerator.generateKey()
        }

        // Encrypt the value using the key
        val cipher = Cipher.getInstance("AES/GCM/NoPadding")
        val key = keyStore.getKey(keystoreAlias, null)
        cipher.init(Cipher.ENCRYPT_MODE, key)
        val iv = cipher.iv
        val encryptedValue = cipher.doFinal(value.toByteArray())

        // Save the encrypted value and IV to shared preferences
        val sharedPreferences = context.getSharedPreferences("my_prefs", Context.MODE_PRIVATE)
        sharedPreferences.edit()
            .putString("encrypted_value", Base64.encodeToString(encryptedValue, Base64.DEFAULT))
            .putString("iv", Base64.encodeToString(iv, Base64.DEFAULT))
            .apply()
    }

    fun getKeyFromKeystore(): String? {
        // Load the KeyStore instance
        val keyStore = KeyStore.getInstance("AndroidKeyStore")
        keyStore.load(null)

        // Load the encrypted value and IV from shared preferences
        val sharedPreferences = context.getSharedPreferences("my_prefs", Context.MODE_PRIVATE)
        val encryptedValue = sharedPreferences.getString("encrypted_value", null)?.let {
            Base64.decode(it, Base64.DEFAULT)
        }
        val iv = sharedPreferences.getString("iv", null)?.let {
            Base64.decode(it, Base64.DEFAULT)
        }

        if (encryptedValue != null && iv != null) {
            // Decrypt the value using the key and IV
            val cipher = Cipher.getInstance("AES/GCM/NoPadding")
            val key = keyStore.getKey(keystoreAlias, null)
            val gcmParameterSpec = GCMParameterSpec(128, iv)
            cipher.init(Cipher.DECRYPT_MODE, key, gcmParameterSpec)
            val decryptedValue = cipher.doFinal(encryptedValue)

            return String(decryptedValue)
        }

        return null
    }
}
