package com.spacelabs.app.database.objectbox.boxes

import com.spacelabs.app.database.objectbox.encryption.EncryptionConverter
import io.objectbox.annotation.Convert
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

@Entity
data class MeasurementData(
    @Id
    var measurementId: Long?,
    val measurementUuid: String,
    val patientId: Long,
    val visitId: Int,
    val sensorId: Int?,
    val patientID1: String?,
    val valueType: String?,
    val paramName: String,
    val value: ByteArray?,
    @Convert(converter = EncryptionConverter::class, dbType = ByteArray::class)
    val measurementData: ByteArray?,
    val numberOfSamples: Int?,
    val timestamp: String,
    var uploadStatus: Int?,
    var uploadTimestamp: String?,
    var retryCount: Int?,
    val tickValue: Long?,
    val sln: Long
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MeasurementData

        if (measurementId != other.measurementId) return false
        if (measurementUuid != other.measurementUuid) return false
        if (patientId != other.patientId) return false
        if (visitId != other.visitId) return false
        if (sensorId != other.sensorId) return false
        if (patientID1 != other.patientID1) return false
        if (valueType != other.valueType) return false
        if (paramName != other.paramName) return false
        if (value != other.value) return false
        if (measurementData != null) {
            if (other.measurementData == null) return false
            if (!measurementData.contentEquals(other.measurementData)) return false
        } else if (other.measurementData != null) return false
        if (numberOfSamples != other.numberOfSamples) return false
        if (timestamp != other.timestamp) return false
        if (uploadStatus != other.uploadStatus) return false
        if (uploadTimestamp != other.uploadTimestamp) return false
        if (retryCount != other.retryCount) return false
        if (tickValue != other.tickValue) return false
        if (sln != other.sln) return false

        return true
    }

    override fun hashCode(): Int {
        var result = measurementId?.hashCode() ?: 0
        result = 31 * result + measurementUuid.hashCode()
        result = 31 * result + patientId.hashCode()
        result = 31 * result + visitId
        result = 31 * result + (sensorId ?: 0)
        result = 31 * result + (patientID1?.hashCode() ?: 0)
        result = 31 * result + (valueType?.hashCode() ?: 0)
        result = 31 * result + paramName.hashCode()
        result = 31 * result + (value?.hashCode() ?: 0)
        result = 31 * result + (measurementData?.contentHashCode() ?: 0)
        result = 31 * result + (numberOfSamples ?: 0)
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + (uploadStatus ?: 0)
        result = 31 * result + (uploadTimestamp?.hashCode() ?: 0)
        result = 31 * result + (retryCount ?: 0)
        result = 31 * result + (tickValue?.hashCode() ?: 0)
        result = 31 * result + sln.hashCode()
        return result
    }

}
