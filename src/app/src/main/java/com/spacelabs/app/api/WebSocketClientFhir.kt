package com.spacelabs.app.api

import android.util.Log
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.api.data.ApiDataTransactionManager
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.daoHelper.PatientDaoHelper
import com.spacelabs.app.interfaces.IOEventCallback
import kotlinx.coroutines.cancel
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.net.URI

class WebSocketClientFhir(serverUri: URI?, httpHeaders: Map<String, String>, apiData: ApiResponseData): WebSocketClient(serverUri, httpHeaders) {

    private val apiRespData: ApiResponseData
    private val apiDataManager = ApiDataTransactionManager()

    private val tag = "FHIRClass"

    init {
        this.apiRespData = apiData
    }

    override fun onOpen(handshakedata: ServerHandshake?) {
        Log.d(tag, "Connection Opened")
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.ServerConnectionStatusChanged, true)
        //CommonEventHandler.postIOEvent(IOEventCallback.IOEventType.BackgroundUpload, null) /*Checking Background Data Pending to upload to the server */

    }

    override fun onMessage(message: String?) {
        try {
            checkResourceTypeAndTakeAction(message!!)
        } catch (e: JSONException) {
            throw RuntimeException(e)
        }
        if (!message.contains("success", ignoreCase = true))
            Log.d(tag, "Received observation response: $message")
    }

    override fun onClose(code: Int, reason: String?, remote: Boolean) {
        if (CommonDataArea.hasInternetConnection) {
            SnsApiManager.connectFhir()
            Log.d("WEBSOCKET_CLIENT", "onClose: ${code}")
        }

        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.ServerConnectionStatusChanged, false)
        ApiDataTransactionManager.historyJob?.cancel()
    }

    override fun onError(ex: Exception?) {
        if (ex != null) {
            Log.e(tag, "ON ERORR: ${ex.cause} ${ ex.stackTraceToString() }")
            if (CommonDataArea.hasInternetConnection )SnsApiManager.connectFhirWithRefreshToken()
        }
    }

    override fun send(text: String?) {
        if (SnsApiManager.webSocketClient != null && SnsApiManager.webSocketClient?.connection != null)
            super.send(text)
    }

    private fun parsePatientAndUpdateDB(json: JSONObject) {
        val patientDaoHelper = PatientDaoHelper(CommonDataArea.curActivity!!)

        val identifier = json.getString("id")
        updatePatientIdentifier(identifier)
        apiRespData.patient.patientID1 = json.getString("id")
//        updatePatientIdentifier(identifier)

        val nameArray = json.getJSONArray("name")
        updatePatientName(nameArray)
        apiRespData.patient.gender = json.getString("gender")
        apiRespData.patient.birthDate = json.getString("birthDate")
        patientDaoHelper.updateCurrentPatientWithAPIPatientInfo(apiRespData)
        Log.d("SNS_PATIENT", "parsePatientAndUpdateDB: ${json.toString()}")
        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.PatientChange, null)

    }

    private fun updatePatientIdentifier(id: String) {
        apiRespData.patient.patientID1 = id
    }

    private fun updatePatientName(nameArray: JSONArray){
        for(i in 0 until nameArray.length()){
            val jObject = nameArray.getJSONObject(i)
            apiRespData.patient.firstName = jObject.getJSONArray("given").getString(0)
            apiRespData.patient.family = jObject.getString("family")
        }
    }

    private fun checkResourceTypeAndTakeAction(message: String) {
        val json = JSONObject(message)
        when (json.getString("resourceType")) {
            "Patient" -> parsePatientAndUpdateDB(json)
            "OperationOutcome" -> onReceiveObservationResponse(json)
        }
    }

    private fun onReceiveObservationResponse(json: JSONObject) {
        val uuId = json.getString("id")
        var responseText: String? = null
        val issueArray = json.getJSONArray("issue")
        for (i in 0 until issueArray.length()) {
            val jObject = issueArray.getJSONObject(i)
            val severity = jObject.getString("severity")
            if(!severity.equals("information", true))
                Log.d("onReceiveObservationResponse", jObject.getString("details"))
            if (jObject.has("details")) {
                responseText = jObject.getJSONObject("details").getString("text")
            }
        }
        if (responseText != null) {
            apiDataManager.checkResponseAndUpdateDb(uuId, responseText)
        }
    }

    /*private fun saveToCsv(uuid: String, severity: String, details: String) {
        val csvFile = File("${ Environment.getExternalStorageDirectory() }/SLHub/", "responseObservations.csv")

        // val csvFile = File("path/to/your/csvfile.csv")  // Replace this with your actual path
        val fileWriter = FileWriter(csvFile, true)  // true for append mode

        val csvLine = "$uuid,$severity,$details\n"
        fileWriter.append(csvLine)

        fileWriter.flush()
        fileWriter.close()
    }*/
}