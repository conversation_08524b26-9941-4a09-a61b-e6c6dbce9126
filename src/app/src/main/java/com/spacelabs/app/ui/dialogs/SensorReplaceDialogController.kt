package com.spacelabs.app.ui.dialogs

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Html
import android.text.Spannable
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import androidx.core.content.res.ResourcesCompat
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.R
import com.spacelabs.app.MainActivity
import com.spacelabs.app.sensor.helper.ScanEventObserverHelper

class SensorReplaceDialogController(mainActivity: MainActivity, sensor: Sensor, oldSensorName: String): DialogManager(mainActivity) {

    private lateinit var icon1: ImageView
    private lateinit var icon2: ImageView

    private lateinit var sensor1: TextView
    private lateinit var sensor2: TextView
    private lateinit var confirmationText: TextView

    private lateinit var cancelBtn: AppCompatButton
    private lateinit var okBtn: AppCompatButton

    private lateinit var iconDrawable: Drawable

    private val sensor: Sensor
    private val sensorType: SensorType
    private val newSensorName: String
    private val oldSensorName: String
    init {
        this.sensor = sensor
        this.sensorType = sensor.sensorType
        this.oldSensorName = oldSensorName
        this.newSensorName = sensor.name
    }

    override fun onCreateDialogActions(context: Context, dialog: Dialog, layout: Int) {
        iconDrawable = when(sensorType){
            SensorType.CHEST -> ResourcesCompat.getDrawable(context.resources, R.drawable.ic_sensor_chest, context.theme)!!
            else -> ResourcesCompat.getDrawable(context.resources, R.drawable.ic_sensor_limb, context.theme)!!
        }
    }

    override fun initUIs(dialog: Dialog) {
        icon1 = dialog.findViewById(R.id.icon1)
        icon2 = dialog.findViewById(R.id.icon2)

        sensor1 = dialog.findViewById(R.id.sensor1)
        sensor2 = dialog.findViewById(R.id.sensor2)
        confirmationText = dialog.findViewById(R.id.confirmationText)

        cancelBtn = dialog.findViewById(R.id.cancelBtn)
        okBtn = dialog.findViewById(R.id.okBtn)
    }

    override fun postUiInitActions(context: Context) {
        icon1.setImageDrawable(iconDrawable)
        icon2.setImageDrawable(iconDrawable)

        sensor1.text = Html.fromHtml(oldSensorName, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        sensor2.text = Html.fromHtml(newSensorName, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        confirmationText.text = Html.fromHtml(
            "Are you sure you want to replace the sensor <b>$oldSensorName</b> with sensor $newSensorName?",
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
    }

    override fun uiActionListeners(context: Context, dialog: Dialog) {
        cancelBtn.setOnClickListener {
            closeDialog(dialog)
        }

        okBtn.setOnClickListener {
            val scanEventHelper = ScanEventObserverHelper()
            scanEventHelper.reConfigureSensor(sensor)
            closeDialog(dialog)
        }
    }

}