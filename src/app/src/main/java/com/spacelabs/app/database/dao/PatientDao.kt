package com.spacelabs.app.database.dao

import android.annotation.SuppressLint
import androidx.room.*
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.database.entities.PatientTable
import com.spacelabs.app.database.entities.VisitTable
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.ui.activityUIHelpers.MainActivityToolbarUi
import java.text.SimpleDateFormat
import java.util.*

@Dao
interface PatientDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPatient(patient: PatientTable): Long

    @Query("SELECT * FROM tblPatient")
    suspend fun getAllPatient(): List<PatientTable>

    @Query("SELECT * FROM tblPatient WHERE patientId = :patientId")
    suspend fun getPatient(patientId: Int): PatientTable

    @Query("UPDATE tblPatient SET status = 'inactive' WHERE patientId != :patientId")
    suspend fun updateAllPatientsToInactiveExceptNewPatient(patientId: Long)

    @Query("SELECT * FROM tblPatient WHERE status = 'active'")
    suspend fun getActivePatient(): List<PatientTable>?

    @Query("SELECT patientId AS id FROM tblPatient WHERE status = 'active'")
    suspend fun getActivePatientId(): Int

    @Query("SELECT patientId AS id FROM tblPatient WHERE status = 'inactive'")
    suspend fun getInActivePatientId(): List<Int>

    @Query("SELECT PatientID1 || ' - ' || UPPER(SUBSTR(firstName, 1, 1)) || SUBSTR(firstName, 2) || ' ' || UPPER(SUBSTR(lastName, 1, 1)) || SUBSTR(lastName, 2) AS name, patientId FROM tblPatient WHERE status = 'active' AND isTempPatient = 0")
    suspend fun getActivePatientName(): List<MainActivityToolbarUi.ActivePatientDetails>

    @Query("UPDATE tblPatient SET status = 'inactive' WHERE status = 'active'")
    suspend fun setAllActivePatientsToInactive()

    @Query("SELECT * FROM tblVisit")
    suspend fun getAllVisit(): List<VisitTable>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVisit(visit: VisitTable): Long

    @SuppressLint("SimpleDateFormat")
    suspend fun insertNewPatientAsActive(patient: PatientTable){
        CommonDataArea.PATIENT.patientId = insertPatient(patient)
        setUpPatientInfo(patient)
        val patientId = CommonDataArea.PATIENT.patientId
        val sdf = SimpleDateFormat("MM-dd-yyyy")
        val date = sdf.format(Date()).toString()
        val visit = VisitTable(null, patientId.toInt(),null,date,null)
        CommonDataArea.PATIENT.visitId = insertVisit(visit)
        updateAllPatientsToInactiveExceptNewPatient(patientId)
    }

    @Query("DELETE FROM tblPatient WHERE patientId != :patientId")
    suspend fun deleteALlPatientHistory(patientId: Int)
    @Query("DELETE FROM tblPatient WHERE patientId IN (:patientId)")
    suspend fun deleteALlPatientHistoryOnDischarge(patientId: Int)

    @Update
    suspend fun updatePatient(patient: PatientTable)

    suspend fun updatePatientInfo(patient: PatientTable) {
        setUpPatientInfo(patient)
        updatePatient(patient)
    }

    fun setUpPatientInfo(patient: PatientTable) {
        if(patient.patientId != null)
            CommonDataArea.PATIENT.patientId = patient.patientId.toLong()

        CommonDataArea.PATIENT.visitId = 0
        CommonDataArea.PATIENT.patientId1 = patient.patientID1
        CommonDataArea.PATIENT.patientId2 = patient.patientID2
        CommonDataArea.PATIENT.firstName = patient.firstName
        CommonDataArea.PATIENT.lastName = patient.lastName
        CommonDataArea.PATIENT.gender = patient.gender
        CommonDataArea.PATIENT.dob = patient.dob
        CommonDataArea.PATIENT.age = patient.age
        CommonDataArea.PATIENT.height = patient.height?.toString()?.toDoubleOrNull()
        CommonDataArea.PATIENT.weight = patient.weight?.toString()?.toDoubleOrNull()
        CommonDataArea.PATIENT.bedNo = patient.bedName?.toString()
        CommonDataArea.PATIENT.wardNo = patient.facilityName?.toString()
        CommonDataArea.PATIENT.admittedOn = patient.admitDate?.toString()
        CommonDataArea.PATIENT.registeredOn = patient.createdOn?.toString()
        CommonDataArea.PATIENT.isPatientTemp = patient.isTempPatient == 1

        CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.PatientChange, null)
    }
}