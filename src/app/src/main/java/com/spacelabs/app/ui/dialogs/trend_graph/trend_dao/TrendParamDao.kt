package com.spacelabs.app.ui.dialogs.trend_graph.trend_dao

import android.widget.TextView
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.data.Entry
import com.google.android.material.card.MaterialCardView
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType

data class TrendParamDao (
    val trendType: StreamDataType,
    val chartOuter: MaterialCardView,
    val chart: LineChart,
    val chartErrorText: TextView,
    val valOuter: MaterialCardView,
    val valueText: TextView,
    val timeText: TextView,
    val data: MutableList<Entry> = mutableListOf(),
    val paramColor: Int,
    val yMax: Float,
    val yMin: Float
)