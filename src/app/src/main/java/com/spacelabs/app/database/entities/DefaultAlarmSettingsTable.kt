package com.spacelabs.app.database.entities

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "tblDefaultAlarmSettings")
data class DefaultAlarmSettingsTable(
    @PrimaryKey(autoGenerate = true)
    val alarmId: Int?,
    val paramId: Int,
    val defaultAlarmCategory: String?,
    val defaultAlarmHighValue: Double?,
    val defaultAlarmLowValue: Double?,
    val defaultAlarmExtremeHighValue: Double?,
    val defaultAlarmExtremeLowValue: Double?,
    val defaultAlarmStatus: Int,
    val defaultSound: Int
)