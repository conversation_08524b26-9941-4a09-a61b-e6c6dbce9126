package com.spacelabs.app.database.dao

import androidx.room.*
import com.spacelabs.app.database.entities.ParametersTable
import com.spacelabs.app.database.relationships.ParameterWithPatientAlarm

@Dao
interface
ParametersDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertParameter(parameter: ParametersTable): Long

    @Query("SELECT * FROM tblParameters")
    suspend fun getAllParameters(): List<ParametersTable>?

    @Query("SELECT * FROM tblParameters ORDER BY paramId DESC")
    suspend fun getAllParametersDescOrder(): List<ParametersTable>

    @Query("SELECT * FROM tblParameters WHERE paramName = :paramName")
    suspend fun getParameterByName(paramName: String): ParametersTable?

    @Update
    suspend fun updateParameter(parameter: ParametersTable)

    @Query("SELECT paramName FROM tblParameters WHERE paramId = :paramId")
    suspend fun getParameterNameById(paramId: Int): String

    @Transaction
    @Query("SELECT * FROM tblParameters WHERE paramId = :paramId")
    suspend fun getParameterWithAlarmSettingsById(paramId: Int): ParameterWithPatientAlarm

    @Query("UPDATE tblParameters SET paramName = UPPER(:paramName) WHERE paramName = :paramName")
    suspend fun updateParamNamesToUpperCase(paramName: String)
}