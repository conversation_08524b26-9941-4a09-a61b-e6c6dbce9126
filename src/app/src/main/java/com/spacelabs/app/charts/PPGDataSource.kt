package com.spacelabs.app.charts

import android.annotation.SuppressLint
import android.graphics.Color
import android.util.Log
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.Highlight
import com.sibelhealth.bluetooth.sensor.sibel.chest.ChestSensor
import com.sibelhealth.bluetooth.sensor.sibel.limb.LimbSensor
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.LogWriter

class PPGDataSource : XYDataSource() {
    private var minVal = -1.0f
     private var maxVal = 0.0f
     private var minValFrame = -1.0f
     private var maxValFrame = 0.0f
     private var diffVal = 0.0f
    private var midVal = 0.0f
    override val highlightPixels = 3
    private val rawValues = ArrayList<Entry>()
    override fun initialize(xyDataType: XYDataType) {
        this.xyDataType = xyDataType
        maxData = when (xyDataType) {
            XYDataType.ECG -> ecgSamplingRate
            XYDataType.Resp -> respSamplingRate /* 10 */ /*410 * 10 */
            XYDataType.PPG_IR -> ppgSamplingRate/* 10*/
            XYDataType.HR ->hrSamplingRate //one data for a minute and trend for 4 hours
            XYDataType.SPO2 -> spo2SamplingRate //one data for a minute and trend for 4 hours
            else -> 0
        }

        for (i in 0 until maxData) {
            values.add(Entry(i.toFloat(), 0.0f))
            rawValues.add(Entry(i.toFloat(), 0.0f))
        }
        //fillSine()
    }



    @SuppressLint("LongLogTag")
    override fun addData(value: Float) {
        try {
            if (writePtr == 0 && !isAllOnZero() && !isWaitTimeOut()) {
                return
            }
            resetTimer()

            val valuesCloned = rawValues.toMutableList()
            val valueEntry = valuesCloned[writePtr]
            if (writePtr == 0) {
                minVal = value
                maxVal = value
            }
            if (minVal > value) minVal = value
            if (maxVal < value) maxVal = value

            valueEntry.y = value
            valueEntry.x = writePtr.toFloat()
            ++writePtr
            updateFillPosTime()

            if (writePtr >= maxData) {
                Log.i("Sibel", "Resetting write Ptr")
                minValFrame = minVal
                maxValFrame = maxVal
                midVal = (maxVal - minVal) / 2 + minVal
                writePtr = 0
                zeroHitTime = System.currentTimeMillis()
            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("PPGDataSource.Add", ex.message)
            LogWriter.writeExceptLog("PPGDataSource.Add", ex.stackTraceToString())
        }

    }

    private fun reInitData(bufLen: Int) {
        maxData = bufLen
        values.clear()
        rawValues.clear()
        for (i in 0 until maxData) {
            values.add(Entry(i.toFloat(), 0.0f))
            rawValues.add(Entry(i.toFloat(), 0.0f))
        }
    }

    override fun setFrequency(freq: Int) {
        var bufLen = 0
        if (freq < 100) bufLen = 25 * 10
        else if (freq > 100) bufLen = 410 * 10
        if (maxData == bufLen) return
        reInitData(bufLen)
    }

    override fun updateData(chart: LineChart, color: Int) {
        try {
            synchronized(this) {
                val set: LineDataSet = chart.data.getDataSetByIndex(0) as LineDataSet
                if (rawValues.isNotEmpty()) {
                    for (i in 0 until rawValues.size) {
                        values[i].y = midVal - (rawValues[i].y - midVal)
                    }
                }
                if (values.isNotEmpty()) {
                    set.values = values
                    set.notifyDataSetChanged()
                    set.calcMinMax()

                    val diff = set.yMax - set.yMin
//                    diffVal = diff
                    //CATION!!!! write PTR can be changed while below loop executes
                    //A copy of writePtr may help avoid that
                    val writePtrTemp = writePtr
                    if (writePtrTemp < maxData - highlightPixels) {
                        chart.highlightValue(0.0f, -1)
                        val valuesCloned = values.toMutableList()
                        for (i in writePtrTemp until writePtrTemp + (highlightPixels-2)) {
                            val valueEntry = valuesCloned[i]
                            valueEntry.x = i.toFloat()
                            val highlight = Highlight(valueEntry.x, valueEntry.y, 0)
                            highlight.dataIndex = 0
                            chart.highlightValue(highlight, false)
                            set.highlightLineWidth = 10f
                            set.setDrawHorizontalHighlightIndicator(false)
                            set.highLightColor = highlighterColor
                        }
                    }

                    val leftAxis: YAxis = chart.axisLeft
                    leftAxis.axisMaximum = set.yMax + diff / 10
                    leftAxis.axisMinimum = set.yMin - diff / 10

                    val rightAxis: YAxis = chart.axisRight
                    rightAxis.axisMaximum = set.yMax + diff / 10
                    rightAxis.axisMinimum = set.yMin - diff / 10

                    chart.data.notifyDataChanged()
                    chart.notifyDataSetChanged()
                    chart.invalidate()
                }
            }
        } catch (ex: Exception) {
            LogWriter.writeExceptLog("PPGDataSource Update Chart Trace->" + xyDataType.name,"Exception->" + ex.stackTraceToString())
        }
    }
}