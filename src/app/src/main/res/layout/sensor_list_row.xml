<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2.5dp"
    android:gravity="center"
    android:background="@drawable/checked_text_view_background"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/sensorIcon"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="5dp"
        android:layout_weight="7"
        app:tint="@color/white" />

    <androidx.appcompat.widget.AppCompatCheckedTextView
        android:id="@+id/sensorName"
        android:textColor="@color/white"
        android:layout_width="match_parent"
        android:layout_height="?android:attr/listPreferredItemHeightSmall"
        android:layout_weight="1"
        style="@style/TextAppearance.AppCompat.Medium"
        android:checkMark="@drawable/custom_check_box"
        android:gravity="center_vertical"
        android:theme="@style/whiteCheckedTextView"
        android:paddingStart="?android:attr/listPreferredItemPaddingStart"
        android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"/>

</LinearLayout>
