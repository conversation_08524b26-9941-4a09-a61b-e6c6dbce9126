<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"

>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:layout_marginBottom="25dp"
            android:background="@color/ap_black"
            android:gravity="center"
            android:layout_gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/icLogoImageView"
                android:layout_width="220dp"
                android:layout_height="80dp"
                android:src="@drawable/applogo"
                android:layout_gravity="center"
                app:layout_constraintBottom_toTopOf="@id/qrCodeImageView"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/register_textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                style="@style/boldText"
                android:text="Register Device"
                android:textColor="@color/white"
                android:textSize="28sp"
                android:fontFamily="@font/open_sans_variable_font"
             />


            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardCornerRadius="10dp"
                android:maxWidth="500dp"
                android:maxHeight="500dp"
                android:background="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/register_textView">

                <ImageView
                    android:id="@+id/qrCodeImageView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="fill"
                    android:padding="0dp"
                    android:cropToPadding="true"
                    android:src="@drawable/app_logo" />
            </com.google.android.material.card.MaterialCardView>
            <!--   <TextView
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:text="Scan this Qr code with web app for device registration"
               android:textColor="@color/off_white"
               android:layout_marginTop="5dp"
               app:layout_constraintTop_toBottomOf="@id/qrCodeImageView" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/sysAccountId"
                android:textColor="@color/white"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"/>
   -->

            <TextView
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_marginTop="13dp"
                android:text="Scan QR Code to Register Device"
                android:textColor="#97EEEEEE"
                android:textSize="11sp"
                android:layout_gravity="center"
                android:gravity="center"
                style="@style/footerTopLabels"
                app:layout_constraintTop_toBottomOf="@id/qrCodeImageView" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/nextButton"
                android:layout_width="100dp"
                android:layout_height="40dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/lightBlue"
                android:text="Register"
                android:textAllCaps="false"
                android:textStyle="normal"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:lineSpacingExtra="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>