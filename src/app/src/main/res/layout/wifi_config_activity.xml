<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">


        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleX="1.5"
            android:scaleY="2"
            android:src="@drawable/ic_logo1" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/chooseAWifi"
            android:textColor="@color/white"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"/>

        <Spinner
            android:id="@+id/wifiSpinner"
            android:layout_width="300dp"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/spinner_bg"
            android:popupBackground="@drawable/spinner_popup_background"
            android:dropDownSelector="@drawable/spinner_popup_background"
            android:dropDownVerticalOffset="45dp"
            android:popupElevation="@dimen/fab_margin"
            android:spinnerMode="dropdown"
            android:gravity="start|center"
            android:nextFocusForward="@id/weight"/>

        <EditText
            android:id="@+id/password"
            android:layout_width="300dp"
            android:layout_height="40dp"
            android:layout_marginTop="15dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:inputType="textPassword"
            android:visibility="gone"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textColor="@color/white"
            android:hint="@string/password"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            tools:ignore="Autofill,LabelFor" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/nextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:paddingStart="50dp"
            android:paddingEnd="50dp"
            android:textColor="@color/white"
            android:text="@string/next"
            android:textAllCaps="false"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/lightBlue" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/animationView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@color/SibelTransparency2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/wifiLoading"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:src="@drawable/wifi_loading"/>

        <TextView
            android:id="@+id/animationText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:paddingStart="15dp"
            android:paddingEnd="0dp"
            android:textColor="@color/white"/>

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/skipBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:padding="10dp"
        android:text="@string/skip"
        android:textColor="@color/white"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
        android:background="@drawable/rounded_edit_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>