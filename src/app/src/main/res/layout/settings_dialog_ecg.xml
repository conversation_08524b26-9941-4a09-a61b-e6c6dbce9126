<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintTop_toTopOf="@id/navLayout"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/layout1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="horizontal"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:ignore="MissingConstraints,NotSibling">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:layout_weight="1"
            android:baselineAligned="false">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingStart="15dp"
                android:paddingEnd="0dp"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <TextView
                    android:id="@+id/textView"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_marginTop="10dp"
                    android:text="@string/ecgParameter"
                    android:gravity="start|center"
                    android:layout_gravity="start"
                    android:textColor="@color/ap_gray" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_gravity="center"
                    android:gravity="start"
                    android:text="@string/extremeHighHr"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/eehHr"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:enabled="false"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/components_gray"
                        android:layout_gravity="start"
                        android:inputType="number"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        android:nextFocusDown="@id/exLowHr"
                        tools:ignore="LabelFor"
                        android:autofillHints="" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/exHighInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/upIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/exHighDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/downIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textView4"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_gravity="center"
                    android:gravity="start"
                    android:text="@string/highHr"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/ehHr"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:enabled="false"
                        android:autofillHints=""
                        android:layout_gravity="start"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/components_gray"
                        android:inputType="number"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        android:nextFocusDown="@id/elHr"
                        tools:ignore="LabelFor" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/highInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/upIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/highDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/downIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingStart="10dp"
                android:paddingEnd="0dp"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <Switch
                    android:id="@+id/ecgAlarmSwitch"
                    android:layout_width="50dp"
                    android:layout_height="30dp"
                    android:layout_marginTop="10dp"
                    android:layout_gravity="center|start"
                    android:gravity="start|center"
                    android:checked="true"
                    android:scaleX="1.2"
                    android:scaleY="1.2"
                    tools:ignore="MissingConstraints,UseSwitchCompatOrMaterialXml" />

                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="@string/extremeLowHr"
                    android:layout_gravity="center"
                    android:gravity="start"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/exLowHr"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:enabled="false"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:layout_gravity="start"
                        android:backgroundTint="@color/components_gray"
                        android:inputType="number"
                        android:nextFocusDown="@id/ehHr"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        tools:ignore="LabelFor" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/exLowInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/upIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/exLowDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/downIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textView5"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:layout_gravity="center"
                    android:gravity="start"
                    android:text="@string/lowHr"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <EditText
                        android:id="@+id/elHr"
                        android:layout_width="100dp"
                        android:layout_height="40dp"
                        android:enabled="false"
                        android:layout_gravity="start"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/components_gray"
                        android:inputType="number"
                        android:paddingStart="20dp"
                        android:paddingEnd="0dp"
                        android:textColor="@color/white"
                        android:imeOptions="actionDone"
                        tools:ignore="LabelFor" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/lowInc"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/upIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/lowDec"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/white"
                        android:text="@string/downIcon"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1.5">

            <!--<androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="50dp"
                android:backgroundTint="@color/background">

                <TextView
                    android:id="@+id/volumeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ecgVolume"
                    android:textColor="@color/ap_gray"
                    android:layout_marginStart="10dp" />

                <com.google.android.material.slider.Slider
                    android:id="@+id/ecgVolume"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    app:labelBehavior="gone"
                    app:thumbColor="@color/white"
                    app:trackColorInactive="@color/components_gray"
                    app:trackColorActive="@color/lightBlue"/>

            </androidx.cardview.widget.CardView>-->

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>