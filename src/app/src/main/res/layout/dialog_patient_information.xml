<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="40dp"
        android:background="@drawable/linear_layout_bg">

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            style="@style/TextAppearance.AppCompat.Medium"
            tools:ignore="MissingConstraints">

            <TextView
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:layout_weight="1.5"
                android:text="@string/patientInformation"
                android:textStyle="bold"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/closeBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="end"
                android:background="@color/ap_transparent"
                android:text="@string/closeButtonIcon"
                android:paddingStart="0dp"
                android:paddingEnd="20dp"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Display1"
                android:layout_weight="1"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:baselineAligned="false"
            android:layout_gravity="bottom"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:gravity="bottom"
            android:orientation="vertical"
            tools:layout_editor_absoluteX="43dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.2"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="bottom|start"
                    android:text="@string/patientId1"
                    android:layout_marginEnd="10dp"
                    android:textColor="@color/ap_gray"
                    style="@style/TextAppearance.AppCompat.Medium"
                    tools:ignore="NestedWeights" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom|start"
                    android:layout_weight="1"
                    android:text="@string/patientId2"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/ap_gray"
                    style="@style/TextAppearance.AppCompat.Medium"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.1"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/patientId"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:layout_weight="1"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:gravity="start|center"
                    android:hint="@string/id1"
                    android:inputType="number"
                    android:nextFocusDown="@id/patientId2"
                    android:paddingStart="20dp"
                    android:paddingEnd="0dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/ap_gray"
                    android:clickable="false"
                    android:focusable="false"
                    tools:ignore="LabelFor,NestedWeights,TextViewEdits"
                    android:autofillHints="" />

                <EditText
                    android:id="@+id/patientId2"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:layout_weight="1"
                    android:autofillHints="name"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:gravity="start|center"
                    android:hint="@string/id2"
                    android:inputType="number"
                    android:nextFocusDown="@id/firstName"
                    android:paddingStart="20dp"
                    android:paddingEnd="0dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/ap_gray"
                    android:clickable="false"
                    android:focusable="false"
                    tools:ignore="LabelFor,NestedWeights,TextViewEdits" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.2"
                android:layout_marginTop="10dp"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom|start"
                    android:layout_weight="1"
                    android:layout_marginEnd="10dp"
                    android:text="@string/firstName"
                    android:autofillHints="name"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:textColor="@color/ap_gray"
                    tools:ignore="LabelFor,NestedWeights" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom|start"
                    android:layout_weight="1"
                    android:text="@string/lastName"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:nextFocusDown="@id/height"
                    android:textColor="@color/ap_gray"
                    tools:ignore="LabelFor,NestedWeights" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom|start"
                    android:layout_weight="1.1"
                    android:text="@string/gender"
                    android:layout_marginStart="10dp"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:nextFocusDown="@id/height"
                    android:textColor="@color/ap_gray"
                    tools:ignore="LabelFor,NestedWeights" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.1"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/firstName"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:autofillHints="name"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:gravity="start|center"
                    android:hint="@string/firstName"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:imeOptions="actionNext"
                    android:inputType="textCapSentences"
                    android:nextFocusForward="@id/lastName"
                    android:paddingStart="20dp"
                    android:paddingEnd="0dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/ap_gray"
                    android:clickable="false"
                    android:focusable="false"
                    tools:ignore="LabelFor,NestedWeights" />

                <EditText
                    android:id="@+id/lastName"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:autofillHints="name"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:gravity="start|center"
                    android:hint="@string/lastName"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:inputType="textCapSentences"
                    android:nextFocusDown="@id/height"
                    android:paddingStart="20dp"
                    android:paddingEnd="0dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/ap_gray"
                    android:clickable="false"
                    android:focusable="false"
                    tools:ignore="LabelFor,NestedWeights" />

                <Spinner
                    android:id="@+id/gender"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1.1"
                    android:background="@drawable/spinner_bg"
                    android:popupBackground="@drawable/spinner_popup_background"
                    android:dropDownSelector="@drawable/spinner_popup_background"
                    android:dropDownVerticalOffset="2dp"
                    android:popupElevation="@dimen/fab_margin"
                    android:spinnerMode="dropdown"
                    android:gravity="start|center"
                    android:nextFocusForward="@id/weight"
                    android:prompt="@string/gender"
                    android:clickable="false"
                    android:focusable="false" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.2"
                android:layout_marginTop="10dp"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_marginEnd="10dp"
                    style="@style/TextAppearance.AppCompat.Medium"
                    tools:ignore="LabelFor,NestedWeights">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/dob"
                        android:textColor="@color/ap_gray"
                        android:layout_alignParentStart="true"
                        style="@style/TextAppearance.AppCompat.Medium" />

                    <TextView
                        android:id="@+id/age"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/ap_gray"
                        android:layout_alignParentEnd="true"
                        style="@style/TextAppearance.AppCompat.Medium"
                        tools:ignore="RelativeOverlap" />

                </RelativeLayout>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom|start"
                    android:layout_weight="1"
                    android:text="@string/height"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:nextFocusDown="@id/height"
                    android:textColor="@color/ap_gray"
                    tools:ignore="LabelFor,NestedWeights" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="bottom|start"
                    android:layout_weight="1"
                    android:text="@string/weight"
                    android:layout_marginStart="10dp"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:nextFocusDown="@id/height"
                    android:textColor="@color/ap_gray"
                    tools:ignore="LabelFor,NestedWeights" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.1"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/dob"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:autofillHints="name"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:gravity="start|center"
                    android:hint="@string/dob"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:nextFocusDown="@id/height"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/ap_gray"
                    android:clickable="false"
                    android:focusable="false"
                    app:drawableRightCompat="@drawable/ic_calendar"
                    tools:ignore="LabelFor,NestedWeights" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:textColor="@color/white"
                    tools:ignore="NestedWeights" >

                    <EditText
                        android:id="@+id/height"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentStart="true"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:hint="@string/height"
                        android:inputType="numberDecimal"
                        android:nextFocusDown="@id/weight"
                        android:textColor="@color/white"
                        android:textColorHint="@color/ap_gray"
                        android:layout_alignEnd="@+id/inchesText"
                        android:clickable="false"
                        android:focusable="false"
                        tools:ignore="RelativeOverlap" />

                    <TextView
                        android:id="@+id/inchesText"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:textColor="@color/ap_gray"
                        android:gravity="center"
                        android:text="@string/inches"
                        android:layout_alignParentEnd="true" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:textColor="@color/white"
                    tools:ignore="NestedWeights" >

                    <EditText
                        android:id="@+id/weight"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentStart="true"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:hint="@string/weight"
                        android:inputType="numberDecimal"
                        android:nextFocusForward="@id/updateBtn"
                        android:textColor="@color/white"
                        android:textColorHint="@color/ap_gray"
                        android:layout_alignEnd="@+id/lbsText"
                        android:clickable="false"
                        android:focusable="false"
                        tools:ignore="RelativeOverlap" />


                    <TextView
                        android:id="@+id/lbsText"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:textColor="@color/ap_gray"
                        android:gravity="center"
                        android:text="@string/lbs"
                        android:layout_alignParentEnd="true" />

                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="10dp"
            android:gravity="bottom"
            android:orientation="horizontal"
            android:visibility="visible">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/discharge"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/components_gray"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="@string/discharge"
                android:textAllCaps="false"
                android:textColor="@color/ap_gray"
                tools:ignore="NestedWeights" />

            <androidx.legacy.widget.Space
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="0.3"/>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/updateBtn"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/lightBlue"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:text="@string/save"
                android:textAllCaps="false"
                android:textColor="@color/white"
                tools:ignore="NestedWeights"/>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>