<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    tools:ignore="VisualLintBounds">

    <TextView
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="PatientAlarmId"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Patient Id"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Param Id"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />



    <TextView
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="AlarmCategory"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Alarm Name"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="Value"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="LowValue"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="ExtremeLowValue"
        android:padding="10dp"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="HighValue"
        android:padding="10dp"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />


    <TextView
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="ExtremeHighValue"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="TimeStamp"
        android:padding="10dp"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="AlarmStatus"
        android:padding="10dp"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

    <TextView
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:text="AlarmSound"
        android:background="@drawable/textview_border"
        android:textStyle="bold"
        android:padding="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center" />

</LinearLayout>

