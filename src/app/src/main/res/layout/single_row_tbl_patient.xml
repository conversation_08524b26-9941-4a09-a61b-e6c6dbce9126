<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="5dp"
    android:paddingBottom="10dp">

    <TextView
        android:id="@+id/PatientId"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="PatientId"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/FristName"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="FristName"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/MiddleName"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="MiddleName"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/LastName"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="LastName"
        android:textColor="@color/white"
        android:textStyle="bold" />


    <TextView
        android:id="@+id/PatientID1"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="PatientID1"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/PatientID2"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="PatientID2"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/DOB"
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="DOB"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/Age"
        android:layout_width="50dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="Age"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/Gender"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="Gender"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/Height"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="Height"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/Weight"
        android:layout_width="70dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="Weight"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/BedName"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="BedName"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/AdminDate"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="AdminDate"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/FacilityName"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="FacilityName"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/CreatedOn"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="CreatedOn"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/Status"
        android:layout_width="60dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="Status"
        android:textColor="@color/white"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/isTempPatient"
        android:layout_width="110dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:padding="10dp"
        android:text="isTempPatient"
        android:textColor="@color/white"
        android:textStyle="bold" />


</LinearLayout>
