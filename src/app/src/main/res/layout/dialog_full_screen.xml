<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_gravity="center"
    android:gravity="center"
    android:background="#E7000000"

      >
    <com.google.android.material.card.MaterialCardView
        android:layout_width="500dp"
        android:layout_height="400dp"
        app:cardBackgroundColor="@color/ap_charcoal"
        app:cardCornerRadius="10dp"

     >
<LinearLayout
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_gravity="center"
    android:layout_margin="20dp"
    >
    <ImageView
        android:id="@+id/company_icon"
        android:layout_width="220dp"
        android:layout_height="80dp"
        android:layout_gravity="center"
        android:src="@drawable/applogo"
        android:visibility="invisible"
        />

    <ImageView
        android:id="@+id/dialogIcon"
        android:layout_width="220dp"
        android:layout_height="80dp"
        android:layout_gravity="center"
        android:layout_marginBottom="20dp"
        android:src="@drawable/ic_device_registered_unsuccess" />

    <TextView
        android:id="@+id/dialogMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:text=" Registration Unsuccessful"
        android:textAppearance="?attr/textAppearanceHeadline6" />
    <TextView
        android:id="@+id/dialogSubMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:textColor="#97EEEEEE"
        android:textSize="13sp"
        android:maxWidth="220dp"
        android:textAlignment="center"
        android:text="Please ensure the device is connected to a stable network/server"
        android:textAppearance="?attr/textAppearanceHeadline6" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/okButton"
        android:layout_width="100dp"
        android:layout_height="40dp"
        android:layout_gravity="center"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="24dp"
        android:backgroundTint="@color/lightBlue"
        android:background="@drawable/rounded_edit_text"
        android:textColor="@color/white"
        android:text="OK"
        />
</LinearLayout>
    </com.google.android.material.card.MaterialCardView>
</LinearLayout>
