<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"

    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="0dp"
    android:layout_margin="0dp"

   >

    <!-- Include your provided layout elements here or create a new layout -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/content_layout"
        >
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="80dp"
            android:src="@drawable/ic_sl_logo_white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/qrCodeImageView"
         />

        <TextView
            android:id="@+id/register_textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_gravity="center"
            android:text="Enroll Device "
            android:textColor="@color/white"
            style="@style/boldText"
            android:textSize="22sp"
            />
    </LinearLayout>

        <LinearLayout
            android:id="@+id/content_layout"

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/header_layout"
            app:layout_constraintBottom_toTopOf="@+id/footer_layout"
            >

        <TextView
            android:id="@+id/deviceUUID"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"

            android:gravity="center"
            android:textSize="20sp"
            android:textAlignment="center"
            android:textColor="@color/off_white"
            android:text="@string/device_uuid"/>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center_horizontal"
            >

        <TextView
            android:id="@+id/uuidEditText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:hint="@string/uuidHint"
            android:layout_weight="1"
            android:paddingStart="25dp"
            android:paddingEnd="25dp"
            android:padding="5dp"
            android:textSize="22sp"
            android:text="ab91xxxx-xxxx-xxxx-xxxxxxcb3233"
            android:gravity="center"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="10dp"
            android:textColor="@color/white"
            android:textColorHint="@color/ap_gray"
            android:visibility="visible"
            android:autofillHints="null"
            />
        </LinearLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:gravity="center"
            android:text="Use Device ID To Generate QR Code"
            android:textColor="#7FEEEEEE"
            android:textSize="13sp"
            />
        <!-- Include your provided layout elements here or create a new layout -->
        </LinearLayout>

        <LinearLayout
            android:id="@+id/footer_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/content_layout"
            app:layout_constraintBottom_toBottomOf="parent"
            >

 <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnScan"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:textColor="@color/white"
            android:textAllCaps="false"
            android:textSize="14sp"
            android:layout_gravity="center"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/lightBlue"
            android:text="Scan QR" />

        <TextView
            android:id="@+id/edtResult"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:gravity="center"
            android:layout_gravity="center"
            android:textAlignment="center"
            android:textColor="#7FEEEEEE"
            android:textSize="13sp"
            android:text="Tap On Scan QR to Enroll Device"
           />

        <!-- Debug Toggle Button -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnDebugToggle"
            android:layout_width="120dp"
            android:layout_height="35dp"
            android:layout_marginTop="10dp"
            android:textColor="@color/white"
            android:textAllCaps="false"
            android:textSize="12sp"
            android:layout_gravity="center"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/components_gray"
            android:text="Show Debug" />

        <!-- Debug Section (Initially Hidden) -->
        <ScrollView
            android:id="@+id/debugScrollView"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginTop="10dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="#33FFFFFF"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="10dp">

                <TextView
                    android:id="@+id/debugTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="QR Code Debug Information"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:layout_marginBottom="10dp" />

                <TextView
                    android:id="@+id/debugRawData"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Raw QR Data: (None scanned yet)"
                    android:textColor="@color/off_white"
                    android:textSize="11sp"
                    android:layout_marginBottom="5dp"
                    android:fontFamily="monospace" />

                <TextView
                    android:id="@+id/debugFormat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Detected Format: (None)"
                    android:textColor="@color/off_white"
                    android:textSize="11sp"
                    android:layout_marginBottom="5dp" />

                <TextView
                    android:id="@+id/debugFields"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Extracted Fields: (None)"
                    android:textColor="@color/off_white"
                    android:textSize="11sp"
                    android:layout_marginBottom="5dp" />

                <TextView
                    android:id="@+id/debugErrors"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Errors: (None)"
                    android:textColor="#FFAA00"
                    android:textSize="11sp"
                    android:layout_marginBottom="10dp" />

                <!-- Manual Input Section -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Manual QR Test:"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="5dp" />

                <EditText
                    android:id="@+id/debugManualInput"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:hint="Paste QR code content here..."
                    android:textColor="@color/white"
                    android:textColorHint="@color/ap_gray"
                    android:textSize="10sp"
                    android:padding="8dp"
                    android:gravity="top"
                    android:inputType="textMultiLine"
                    android:layout_marginBottom="5dp" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnTestManualInput"
                    android:layout_width="100dp"
                    android:layout_height="30dp"
                    android:textColor="@color/white"
                    android:textAllCaps="false"
                    android:textSize="10sp"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/lightBlue"
                    android:text="Test Input" />

            </LinearLayout>
        </ScrollView>

        </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>