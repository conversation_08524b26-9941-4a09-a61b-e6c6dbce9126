<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/ap_transparent"
    android:layout_gravity="center"
    android:gravity="center">
    <LinearLayout
        android:layout_width="450dp"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/background"
        android:orientation="vertical" >
        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            android:layout_weight="1.6"
            style="@style/TextAppearance.AppCompat.Medium"
            >
            <TextView
                android:id="@+id/alertTitle"
                style="@style/TextAppearance.AppCompat.Title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="20dp"
                android:layout_weight="1.5"
                android:textStyle="bold"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/closeBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="5dp"
                android:layout_gravity="top"
                android:gravity="end"
                android:background="@color/ap_transparent"
                android:text="@string/closeButtonIcon"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_weight="0"/>
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="20dp"
            android:gravity="center_vertical">

            <ImageView
                android:id="@+id/alertIcon"
                android:layout_width="120dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:layout_marginBottom="5dp"
                android:src="@drawable/ic_sensor_chest"
                android:visibility="visible" />
            <TextView
                android:id="@+id/sensorIdTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Device ID"
                android:visibility="gone"
                android:layout_gravity="center"
                android:textSize="17sp"
                android:textColor="@color/white"
                android:layout_centerInParent="true"/>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:layout_gravity="center">

                <TextView
                    android:id="@+id/alertMessage"
                    style="@style/TextAppearance.AppCompat.Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:layout_gravity="center"
                    android:layout_marginEnd="40dp"
                    android:paddingTop="5dp"
                    android:gravity="center"
                    android:textSize="19sp"
                    android:textColor="@color/white" />
                <TextView
                    android:id="@+id/additionalMessage"
                    style="@style/TextAppearance.AppCompat.Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="40dp"
                    android:paddingTop="5dp"
                    android:gravity="center"
                    android:textSize="14sp"
                    android:textColor="@color/ap_gray"
                    android:visibility="gone" />
            </LinearLayout>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal" >
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/alertOkButton"
                android:layout_width="110dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:paddingStart="30dp"
                android:paddingEnd="30dp"
                android:text="@string/ok"
                android:textColor="@color/white"
                android:visibility="visible"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/lightBlue"
                style="@style/TextAppearance.AppCompat.Medium"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>