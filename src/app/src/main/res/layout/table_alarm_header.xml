<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="2.5dp"
        android:paddingBottom="2.5dp"
        android:orientation="horizontal">

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="50dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:text="Id"
            android:textStyle="bold"
            android:padding="10dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center"  />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:layout_weight="1"
            android:text="alarmUUID"
            android:padding="10dp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:layout_weight="1"
            android:text="patientId"
            android:padding="10dp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="defaultAlarmId"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="patientAlarmId"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="alarmName"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="limitExceeded"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="230dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="startTime"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="230dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="endTime"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="duration"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="Acknowledged"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="230dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="acknowledgedTime"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:padding="10dp"
            android:text="uploadStatus"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

    </LinearLayout>

</HorizontalScrollView>