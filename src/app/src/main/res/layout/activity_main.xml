<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context="com.spacelabs.app.MainActivity">

    <LinearLayout
        android:id="@+id/fullLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="0dp"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <LinearLayout
            android:id="@+id/pauseAlarmView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="7"
            android:background="@color/red"
            android:visibility="gone"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="7"
                android:paddingTop="3dp"
                android:paddingBottom="3dp"
                android:src="@drawable/pause"
                tools:ignore="NestedWeights"
                app:tint="@color/white" />

            <TextView
                android:id="@+id/pauseAlarmText"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center|start"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/cancelPause"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="end"
                android:background="@color/ap_transparent"

                android:text="@string/closeButtonIcon"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_weight="7"/>
<!--            android:theme="@style/fontAwesomeText"-->

        </LinearLayout>

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/appToolbar"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="7"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            app:titleTextAppearance="@style/Toolbar.TitleText"
            app:titleTextColor="@color/white">

            <LinearLayout
                android:id="@+id/sensorInfo"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="end"
                android:layout_marginEnd="42dp">

                <LinearLayout
                    android:id="@+id/chestSensInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:padding="3dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:src="@drawable/ic_sensor_chest" />

                    <TextView
                        android:id="@+id/chestSensName"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="3dp"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/white" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/limbSensInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:padding="3dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:src="@drawable/ic_sensor_limb" />

                    <TextView
                        android:id="@+id/limbSensName"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="3dp"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/white" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/bpSensInfo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:padding="3dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:src="@drawable/ic_sensor_bp" />

                    <TextView
                        android:id="@+id/bpSensName"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:padding="3dp"
                        android:textAppearance="@style/TextAppearance.AppCompat.Small"
                        android:textColor="@color/white" />
                </LinearLayout>

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="90"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="83"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <LinearLayout
                    android:id="@+id/outerLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:baselineAligned="false"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/chartViewLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="9"
                        android:orientation="vertical"
                        tools:ignore="RtlSymmetry">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/ecgChartOuter"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp"
                            tools:ignore="NestedWeights">

                            <com.github.mikephil.charting.charts.LineChart
                                android:id="@+id/ecgChart"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/gray"
                                android:visibility="invisible" />

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:layout_marginStart="12dp"
                                android:paddingVertical="15dp"
                                android:layout_gravity="center_vertical">
                                <View
                                    android:id="@+id/ecgReferenceLineView"
                                    android:layout_width="0dp"
                                    android:layout_height="0dp"
                                    android:background="@color/lightGreen"
                                    android:layout_gravity="center_vertical"
                                    android:visibility="invisible"
                                    />
                            </LinearLayout>


                            <TextView
                                android:id="@+id/ecgReferenceLineTv"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:autoSizeTextType="uniform"
                                android:text=""
                                android:textSize="12sp"
                                android:textColor="@color/lightGreen"
                                android:layout_gravity="center_vertical"
                                android:layout_marginTop="15dp"
                                android:layout_marginStart="16dp"
                                android:visibility="invisible"
                                />

                            <TextView
                                android:id="@+id/ecgScale"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="start|bottom"
                                android:autoSizeTextType="uniform"
                                android:background="@drawable/rounded_edit_text"
                                android:paddingStart="10dp"
                                android:paddingEnd="10dp"
                                android:paddingBottom="5dp"
                                android:text="@string/ecgZoom"
                                android:textAppearance="@style/TextAppearance.AppCompat.Small"
                                android:textColor="@color/white"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/ecgConnectionAlerts"
                                style="@style/noConnectionText"
                                android:text="@string/no_chest_sensor_connected" />

                            <LinearLayout
                                android:id="@+id/hrAlarmBox"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/rounded_alarm_view"
                                android:layout_gravity="end|top"
                                android:orientation="horizontal"
                                android:visibility="gone">

                                <TextView
                                    android:id="@+id/ecgAlarmSilenceText"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="15dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="5dp"
                                    android:paddingBottom="3dp" />

                                <TextView
                                    android:id="@+id/ecgAlarmPrefix"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="5dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="5dp"
                                    android:paddingBottom="3dp" />

                                <TextView
                                    android:id="@+id/ecgAlarmText"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="5dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="15dp"
                                    android:paddingBottom="3dp" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/respChartOuter"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:padding="5dp"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <com.github.mikephil.charting.charts.LineChart
                                android:id="@+id/respChart"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/gray"
                                android:visibility="invisible" />

                            <TextView
                                android:id="@+id/respConnectionAlerts"
                                style="@style/noConnectionText"
                                android:text="@string/no_chest_sensor_connected" />

                            <LinearLayout
                                android:id="@+id/rrAlarmBox"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/rounded_alarm_view"
                                android:layout_gravity="end|top"
                                android:orientation="horizontal"
                                android:visibility="gone">

                                <TextView
                                    android:id="@+id/respAlarmSilenceText"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="15dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="5dp"
                                    android:paddingBottom="3dp" />

                                <TextView
                                    android:id="@+id/respAlarmPrefix"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="5dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="5dp"
                                    android:paddingBottom="3dp" />

                                <TextView
                                    android:id="@+id/respAlarmText"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="5dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="15dp"
                                    android:paddingBottom="3dp" />
                            </LinearLayout>
                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/spo2ChartOuter"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:padding="5dp"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <com.github.mikephil.charting.charts.LineChart
                                android:id="@+id/spo2Chrt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/gray"
                                android:visibility="invisible" />

                            <TextView
                                android:id="@+id/spo2ConnectionAlerts"
                                style="@style/noConnectionText"
                                android:text="@string/no_limb_sensor_connected" />

                            <LinearLayout
                                android:id="@+id/spo2AlarmBox"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/rounded_alarm_view"
                                android:layout_gravity="end|top"
                                android:orientation="horizontal"
                                android:visibility="gone">

                                <TextView
                                    android:id="@+id/spo2AlarmSilenceText"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="15dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="5dp"
                                    android:paddingBottom="3dp" />

                                <TextView
                                    android:id="@+id/spo2AlarmPrefix"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="5dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="5dp"
                                    android:paddingBottom="3dp" />

                                <TextView
                                    android:id="@+id/spo2AlarmText"
                                    style="@style/TextAppearance.AppCompat.Small"
                                    android:layout_width="wrap_content"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:paddingStart="5dp"
                                    android:paddingTop="3dp"
                                    android:paddingEnd="15dp"
                                    android:paddingBottom="3dp" />
                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/rightPanel"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="38"
                        android:orientation="vertical"
                        tools:ignore="RtlSymmetry">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/hrCard"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp"
                            tools:ignore="NestedWeights">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:padding="10dp"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="3"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/textView12"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:text="@string/hr"
                                        tools:ignore="TooDeepLayout" />

                                    <TextView
                                        android:id="@+id/hrHighVal"
                                        style="@style/footerTopLabels"
                                        android:textColor="@color/lightGreen"
                                        android:gravity="end"
                                        android:visibility="invisible"
                                        android:layout_weight="1"/>

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/hrLowVal"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="3.1"
                                    android:layout_marginTop="-5dp"
                                    android:gravity="end"
                                    android:visibility="invisible"
                                    android:textColor="@color/lightGreen" />

                                <TextView
                                    android:id="@+id/hrValue"
                                    style="@style/sideBarVitals"
                                    android:layout_weight="1.2"
                                    android:visibility="invisible"
                                    android:textColor="@color/lightGreen" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/rrCard"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:padding="5dp"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:padding="10dp"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="3"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/textView16"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:text="@string/resp"
                                        tools:ignore="TooDeepLayout" />

                                    <TextView
                                        android:id="@+id/respHighVal"
                                        style="@style/footerTopLabels"
                                        android:textColor="@color/white"
                                        android:gravity="end"
                                        android:visibility="invisible"
                                        android:layout_weight="1"/>

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/respLowVal"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="3.1"
                                    android:layout_marginTop="-5dp"
                                    android:gravity="end"
                                    android:visibility="invisible"
                                    android:textColor="@color/white" />

                                <TextView
                                    android:id="@+id/respValue"
                                    style="@style/sideBarVitals"
                                    android:layout_weight="1.2"
                                    android:visibility="invisible"
                                    android:textColor="@color/white" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/spo2Card"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:padding="5dp"
                            app:cardBackgroundColor="@color/gray"
                            app:cardCornerRadius="4dp"
                            app:strokeColor="@color/gray"
                            app:strokeWidth="2dp">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:padding="10dp"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="3"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/textView20"
                                        style="@style/footerTopLabels"
                                        android:layout_weight="1"
                                        android:text="@string/spo2"
                                        tools:ignore="TooDeepLayout" />

                                    <TextView
                                        android:id="@+id/spo2HighVal"
                                        style="@style/footerTopLabels"
                                        android:textColor="@color/spo2Blue"
                                        android:gravity="end"
                                        android:visibility="invisible"
                                        android:layout_weight="1"/>

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/spo2LowVal"
                                    style="@style/footerTopLabels"
                                    android:layout_weight="3.1"
                                    android:layout_marginTop="-5dp"
                                    android:gravity="end"
                                    android:visibility="invisible"
                                    android:textColor="@color/spo2Blue" />

                                <TextView
                                    android:id="@+id/spo2value"
                                    style="@style/sideBarVitals"
                                    android:layout_weight="1.2"
                                    android:layout_marginStart="-5dp"
                                    android:layout_marginEnd="-5dp"
                                    android:visibility="invisible"
                                    android:textColor="@color/spo2Blue" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="16"
                android:orientation="vertical">

                <include layout="@layout/activity_main_footer" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/main_activity_full_frame"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/halfTransparency"
        android:visibility="gone" />

</RelativeLayout>