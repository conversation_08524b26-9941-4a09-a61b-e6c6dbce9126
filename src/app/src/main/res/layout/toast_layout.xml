<?xml version="1.0" encoding="utf-8"?>


    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/custom_toast_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@drawable/spinner_popup_background"
        android:elevation="10dp">

        <!-- Icon -->
        <ImageView
            android:id="@+id/toast_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/checked" />

        <!-- Message -->
        <TextView
            android:id="@+id/toast_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white" />



</LinearLayout>