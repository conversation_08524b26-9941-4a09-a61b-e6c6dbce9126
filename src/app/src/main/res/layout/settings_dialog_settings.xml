<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_constraintTop_toBottomOf="@+id/navLayout">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/layout1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:baselineAligned="false"
            android:padding="15dp"
            tools:ignore="UnknownIdInLayout">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:baselineAligned="false"
                android:orientation="horizontal">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:cardBackgroundColor="@color/background">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight=".2"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/swVersion"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp" />

                        <TextView
                            android:id="@+id/androidId"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="22dp"
                            android:gravity="start"
                            android:text="@string/periodicDbClearanceTime"
                            android:textColor="@color/ap_gray" />

                        <Spinner
                            android:id="@+id/delete_timer_spinner"
                            android:layout_width="250dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginEnd="90dp"
                            android:background="@drawable/spinner_bg"
                            android:dropDownVerticalOffset="41dp"
                            android:gravity="center"
                            android:paddingStart="10dp"
                            android:paddingEnd="10dp"
                            android:popupBackground="@color/gray"
                            android:spinnerMode="dropdown"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                        <LinearLayout
                            android:id="@+id/ipAddress_layout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="0dp"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:layout_marginTop="22dp"
                                >
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:backgroundTint="@color/gray"
                                android:text="IOMT WebSocket "
                                android:layout_weight="2"
                                android:layout_marginEnd="35dp"
                                android:textColor="@color/ap_gray" />

                                <EditText
                                    android:id="@+id/ipAddressEditText"
                                    android:layout_width="260dp"
                                    android:layout_height="40dp"
                                    android:background="@drawable/rounded_edit_text"
                                    android:backgroundTint="@color/components_gray"
                                    android:hint="@string/ipHint"
                                    android:paddingStart="20dp"
                                    android:paddingEnd="20dp"
                                    android:layout_weight="1"
                                    android:layout_marginTop="10dp"
                                    android:textColor="@color/white"
                                    android:textColorHint="@color/ap_gray"
                                    android:visibility="visible"
                                    android:autofillHints="null"
                                    android:inputType="text" />

                            </LinearLayout>


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="10dp"
                                android:orientation="horizontal">

                               <!-- <EditText
                                    android:id="@+id/ipAddressPortEditText"
                                    android:layout_width="0dp"
                                    android:layout_height="40dp"
                                    android:background="@drawable/rounded_edit_text"
                                    android:backgroundTint="@color/components_gray"
                                    android:hint="@string/portNumber"
                                    android:layout_weight="4"
                                    android:paddingStart="20dp"
                                    android:paddingEnd="20dp"
                                    android:layout_marginEnd="10dp"
                                    android:textColor="@color/white"
                                    android:textColorHint="@color/ap_gray"
                                    android:visibility="gone" />-->

                                <androidx.appcompat.widget.AppCompatButton
                                    android:id="@+id/saveIpButton"
                                    android:layout_width="130dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center"
                                    android:background="@drawable/rounded_edit_text"
                                    android:backgroundTint="@color/lightBlue"
                                    android:paddingStart="20dp"
                                    android:paddingEnd="20dp"
                                    android:text="@string/reconnect"
                                    android:textAllCaps="false"
                                    android:textColor="@color/white"
                                    android:visibility="gone" />

                                <androidx.appcompat.widget.AppCompatButton
                                    android:id="@+id/editIpButton"
                                    android:layout_width="130dp"
                                    android:layout_height="40dp"
                                    android:layout_gravity="center"
                                    android:background="@drawable/rounded_edit_text"
                                    android:backgroundTint="@color/lightBlue"
                                    android:paddingStart="20dp"
                                    android:paddingEnd="20dp"
                                    android:text="@string/editIpAddress"
                                    android:textAllCaps="false"
                                    android:textColor="@color/white" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>


                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.3"
                android:background="@color/background"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingEnd="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:background="@color/background"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights">

                    <TextView
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:text="@string/wifi"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/wifi_ssid"
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/wifi_name"
                        android:textColor="@color/white"
                        android:textStyle="bold" />


                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/config_wifi"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_margin="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:paddingStart="40dp"
                        android:paddingEnd="40dp"
                        android:text="@string/configwifi"
                        android:textAllCaps="false"
                        android:textColor="@color/white" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/dbReplicaBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center"
                        android:layout_margin="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:paddingStart="40dp"
                        android:paddingEnd="40dp"
                        android:text="@string/replicateDb"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:visibility="gone" />

                    <Spinner
                        android:id="@+id/wifi_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_margin="10dp"
                        android:background="@drawable/spinner_bg"
                        android:dropDownVerticalOffset="41dp"
                        android:paddingStart="20dp"
                        android:paddingEnd="10dp"
                        android:popupBackground="@color/gray"
                        android:prompt="@string/your_prompt_text"
                        android:spinnerMode="dropdown"
                        android:spinnerStyle="@style/SpinnerStyle"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:visibility="gone" />

                    <EditText
                        android:id="@+id/wifi_user_name"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_margin="10dp"
                        android:autofillHints=""
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/gray"
                        android:hint="@string/username"
                        android:inputType="text"
                        android:paddingStart="20dp"
                        android:paddingEnd="10dp"
                        android:textColor="@color/white"
                        android:textColorHint="@color/ap_gray"
                        android:visibility="gone" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="start|center"
                    android:orientation="vertical">

                    <TextView
                        style="@style/TextAppearance.AppCompat.Medium"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:text="@string/alarmVolume"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/volume"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:valueFrom="80"
                        android:valueTo="100"
                        app:thumbColor="@color/white"
                        app:labelBehavior="gone"
                        app:trackColorInactive="@color/components_gray"
                        app:trackColorActive="@color/lightBlue"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"
                            android:text="@string/critical"/>

                        <TextView
                            android:id="@+id/criticalVolume"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"/>

                        <TextView
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"
                            android:text="@string/high"/>

                        <TextView
                            android:id="@+id/highVolume"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"/>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"
                            android:text="@string/medium"/>

                        <TextView
                            android:id="@+id/mediumVolume"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"/>

                        <TextView
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="end|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"
                            android:text="@string/low"/>

                        <TextView
                            android:id="@+id/lowVolume"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start|center"
                            android:paddingStart="0dp"
                            android:paddingEnd="5dp"
                            android:textColor="@color/white"/>

                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/hospitalName_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="22dp"
                        >

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="2dp"
                            android:gravity="start"
                            android:textColor="@color/white"
                            android:text="@string/hospital_name"/>
                        <TextView
                            android:id="@+id/hospitalName"
                            style="@style/TextAppearance.AppCompat.Medium"
                            android:layout_marginTop="5dp"
                            android:layout_width="200dp"
                            android:layout_height="30dp"
                            android:textAlignment="center"
                            android:textColor="@color/white"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/components_gray"
                            />

                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/wifi_save"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="end"
                        android:layout_margin="10dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/lightBlue"
                        android:paddingStart="40dp"
                        android:paddingEnd="40dp"
                        android:text="@string/save"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>