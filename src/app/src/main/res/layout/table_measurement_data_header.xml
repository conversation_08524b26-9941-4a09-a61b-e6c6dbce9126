<?xml version="1.0" encoding="utf-8"?>

<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        tools:ignore="VisualLintBounds">

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="MeasurementId"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="280dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="UUID"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="VisitId"
            android:textColor="@color/white"
            android:textStyle="bold" />
        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="SensorId"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="ParamId"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="PatientID1"
            android:textColor="@color/white"
            android:textStyle="bold" />
        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:text="ValueType"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="ParamName"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="Value"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="140dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="MeasurementData"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="130dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="NumberOfSample"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="TimeStamp"
            android:textColor="@color/white"
            android:textStyle="bold" />
        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="UploadStatus"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="UploadTimeStamp"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="RetryCount"
            android:textColor="@color/white"
            android:textStyle="bold" />
        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="TickValue"
            android:textColor="@color/white"
            android:textStyle="bold" />
    </LinearLayout>

</HorizontalScrollView>
