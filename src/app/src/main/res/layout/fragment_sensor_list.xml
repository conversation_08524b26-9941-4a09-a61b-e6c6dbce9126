<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_marginTop="40dp"
            android:background="@drawable/linear_layout_bg"
            android:backgroundTint="@color/background"
            android:baselineAligned="false">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:layout_weight="1"
                android:baselineAligned="false">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.2"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights,UselessParent">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:paddingStart="20dp"
                        android:paddingEnd="10dp"
                        android:paddingTop="10dp"
                        android:textColor="@color/white"
                        android:text="@string/available_sensors"
                        android:textStyle="bold"
                        style="@style/TextAppearance.AppCompat.Headline"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:paddingStart="20dp"
                        android:paddingEnd="10dp"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/chestBtn"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:paddingTop="7dp"
                            android:paddingBottom="7dp"
                            android:drawableStart="@drawable/ic_sensor_chest"
                            android:text="@string/chest"
                            android:textColor="@color/white"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/components_gray"/>

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/limbBtn"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:paddingTop="7dp"
                            android:paddingBottom="7dp"
                            android:drawableStart="@drawable/ic_sensor_limb"
                            android:text="@string/limb"
                            android:textColor="@color/white"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/components_gray"/>

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/bpBtn"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1"
                            android:paddingStart="15dp"
                            android:paddingEnd="15dp"
                            android:paddingTop="7dp"
                            android:paddingBottom="7dp"
                            android:drawableStart="@drawable/ic_sensor_bp"
                            android:text="@string/bp"
                            android:textColor="@color/white"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/components_gray"/>

                        <!--<androidx.appcompat.widget.SearchView
                            android:id="@+id/searchBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            app:queryHint="search"
                            app:iconifiedByDefault="false"
                            app:searchIcon="@drawable/ic_search"
                            android:backgroundTint="@color/components_gray"
                            android:layout_weight="5"
                            app:queryBackground="@drawable/rounded_edit_text"
                            style="@style/SearchViewStyle"
                            android:background="@drawable/rounded_edit_text"/>-->

                        <ImageButton
                            android:id="@+id/refreshButton"
                            android:layout_width="40dp"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:background="@drawable/rounded_edit_text"
                            android:src="@drawable/arrows_rotate_solid"
                            android:scaleX="0.7"
                            android:scaleY="0.7"
                            android:scaleType="fitCenter"
                            app:tint="@color/white" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="10dp"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/sensorsList"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:choiceMode="multipleChoice"
                            android:divider="@color/ap_transparent"
                            android:dividerHeight="5dp"
                            android:visibility="gone"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:id="@+id/scanningProgress"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="top"
                            android:gravity="center"
                            android:padding="20dp"
                            android:text="@string/sensor_scan_instruction"
                            android:textColor="@color/white"
                            style="@style/Animation.Design.BottomSheetDialog"
                            android:indeterminateTint="@color/white" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="0.9"
                        android:outlineProvider="none"
                        app:cardBackgroundColor="@android:color/transparent"
                        tools:ignore="NestedWeights">

                        <TextView
                            android:id="@+id/noSensorText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="@string/noSensorsConnected"
                            android:textColor="@color/ap_gray"
                            style="@style/TextAppearance.AppCompat.Title"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <Button
                                android:layout_width="60dp"
                                android:layout_height="match_parent"
                                android:id="@+id/closeBtn"
                                android:layout_weight="4"
                                android:paddingEnd="20dp"
                                android:paddingStart="0dp"
                                android:background="@color/ap_transparent"
                                android:layout_gravity="end"
                                android:gravity="end"
                                style="@style/TextAppearance.AppCompat.Display1"
                                android:textColor="@color/white"
                                android:text="@string/closeButtonIcon"
                                tools:ignore="NestedWeights" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="4"
                                android:text="@string/paired_sensors"
                                android:textColor="@color/white"
                                android:padding="10dp"
                                android:layout_gravity="center"
                                android:gravity="start|center"
                                style="@style/TextAppearance.AppCompat.Headline"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_margin="10dp"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <LinearLayout
                                    android:id="@+id/chestBoxOuter"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:layout_marginEnd="20dp"
                                    android:layout_marginBottom="10dp"
                                    android:gravity="center"
                                    android:background="@drawable/rounded_corner_view"
                                    android:backgroundTint="@color/components_gray"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:padding="10dp"
                                        android:layout_marginStart="20dp"
                                        android:src="@drawable/ic_sensor_chest"
                                        android:layout_weight="4"
                                        app:tint="@color/white"
                                        tools:ignore="TooDeepLayout" />

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="10dp"
                                        android:layout_gravity="center"
                                        android:layout_weight="1.6"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/chestText"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_gravity="center"
                                            android:text="@string/chestSensor"
                                            android:textColor="@color/white"
                                            style="@style/TextAppearance.AppCompat.Title"
                                            android:layout_weight="1" />

                                        <TextView
                                            android:id="@+id/chestConnectionStatus"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_gravity="center"
                                            android:text="@string/dfu_status_connecting_msg"
                                            android:textColor="@color/white"
                                            style="@style/TextAppearance.AppCompat.Title"
                                            android:layout_weight="1" />

                                    </LinearLayout>



                                    <androidx.appcompat.widget.AppCompatCheckBox
                                        android:id="@+id/chestBox"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        style="@style/TextAppearance.AppCompat.Title"
                                        android:button="@drawable/custom_check_box2"
                                        android:layout_gravity="end|center"
                                        android:layout_weight="4"
                                        android:textColor="@color/white"/>

                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/limbBoxOuter"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="20dp"
                                    android:layout_marginBottom="10dp"
                                    android:gravity="center"
                                    android:background="@drawable/rounded_corner_view"
                                    android:backgroundTint="@color/components_gray"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:padding="10dp"
                                        android:layout_marginStart="20dp"
                                        android:src="@drawable/ic_sensor_limb"
                                        android:layout_weight="4"
                                        app:tint="@color/white"/>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="10dp"
                                        android:layout_gravity="center"
                                        android:layout_weight="1.6"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/limbText"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_gravity="center"
                                            android:text="@string/limbSensor"
                                            android:textColor="@color/white"
                                            style="@style/TextAppearance.AppCompat.Title"
                                            android:layout_weight="1" />

                                        <TextView
                                            android:id="@+id/limbConnectionStatus"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_gravity="center"
                                            android:text="@string/dfu_status_connecting_msg"
                                            android:textColor="@color/white"
                                            style="@style/TextAppearance.AppCompat.Title"
                                            android:layout_weight="1" />

                                    </LinearLayout>



                                    <androidx.appcompat.widget.AppCompatCheckBox
                                        android:id="@+id/limbBox"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:button="@drawable/custom_check_box2"
                                        android:layout_weight="4"
                                        android:layout_gravity="end|center"/>

                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/bpBoxOuter"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:layout_marginTop="5dp"
                                    android:layout_marginEnd="20dp"
                                    android:layout_marginBottom="10dp"
                                    android:gravity="center"
                                    android:background="@drawable/rounded_corner_view"
                                    android:backgroundTint="@color/components_gray"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"
                                        android:padding="10dp"
                                        android:layout_marginStart="20dp"
                                        android:src="@drawable/ic_sensor_bp"
                                        android:layout_weight="4"
                                        app:tint="@color/white"/>

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="10dp"
                                        android:layout_gravity="center"
                                        android:layout_weight="1.6"
                                        android:orientation="vertical">

                                        <TextView
                                            android:id="@+id/bpText"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_gravity="center"
                                            android:text="@string/bpSensor"
                                            android:textColor="@color/white"
                                            style="@style/TextAppearance.AppCompat.Title"
                                            android:layout_weight="1" />

                                        <TextView
                                            android:id="@+id/bpConnectionStatus"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_marginStart="10dp"
                                            android:layout_gravity="center"
                                            android:text="@string/dfu_status_connecting_msg"
                                            android:textColor="@color/white"
                                            style="@style/TextAppearance.AppCompat.Title"
                                            android:layout_weight="1" />

                                    </LinearLayout>



                                    <androidx.appcompat.widget.AppCompatCheckBox
                                        android:id="@+id/bpBox"
                                        style="@style/TextAppearance.AppCompat.Title"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_gravity="end|center"
                                        android:layout_weight="4"
                                        android:button="@drawable/custom_check_box2"
                                        android:textColor="@color/white" />

                                </LinearLayout>

                                <androidx.legacy.widget.Space
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"/>

                            </LinearLayout>

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:baselineAligned="false"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_weight="5">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.2"
                    android:orientation="horizontal"
                    tools:ignore="NestedWeights">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/cancel"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="start|bottom"
                        android:layout_marginStart="20dp"
                        android:paddingStart="30dp"
                        android:paddingEnd="30dp"
                        android:text="@string/cancel"
                        android:textColor="@color/white"
                        android:textAllCaps="false"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/components_gray"/>

                    <androidx.cardview.widget.CardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"/>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/connectBtn"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_marginEnd="10dp"
                        android:paddingStart="30dp"
                        android:paddingEnd="30dp"
                        android:layout_gravity="end"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:text="@string/connect"
                        android:backgroundTint="@color/lightBlue"
                        android:background="@drawable/rounded_edit_text" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center|end"
                    android:layout_weight="1">
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="0dp"
                        android:background="@drawable/rounded_edit_text"
                        android:layout_marginStart="40dp"
                        android:layout_marginEnd="10dp"
                        android:layout_weight="0.3"
                        >

                        <TextView
                            android:id="@+id/sensorAuthtextView"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="start"
                            android:layout_marginEnd="10dp"
                            android:gravity="center"
                            android:text="@string/sensor_authentication"
                            android:textColor="@color/white" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/sensorAuthenticationCheck"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:checked="true"
                            android:layout_gravity="start"
                            android:gravity="start|center"
                            android:scaleX="1.2"
                            android:scaleY="1.2"
                            tools:ignore="MissingConstraints,UseSwitchCompatOrMaterialXml" />
                    </LinearLayout>
                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/remove"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="end|bottom"
                        android:layout_marginEnd="20dp"
                        android:paddingStart="30dp"
                        android:paddingEnd="30dp"
                        android:textColor="@color/white"
                        android:visibility="invisible"
                        android:text="@string/remove"
                        android:backgroundTint="@color/red"
                        android:textAllCaps="false"
                        android:background="@drawable/rounded_edit_text" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>