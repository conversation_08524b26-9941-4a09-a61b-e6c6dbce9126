<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="400dp"
    android:layout_height="330dp"
    android:background="@color/ap_transparent"
    android:layout_gravity="center"
    android:gravity="center">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/background"
        android:orientation="vertical" >

        <LinearLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            android:layout_weight="1.6"
            style="@style/TextAppearance.AppCompat.Medium"
            tools:ignore="MissingConstraints">

            <TextView
                android:id="@+id/alertTitle"
                style="@style/TextAppearance.AppCompat.Title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_weight="1.5"
                android:text="@string/connectionStatus"
                android:textStyle="bold"
                android:textColor="@color/white"
                tools:ignore="NestedWeights" />

            <Button
                android:id="@+id/closeBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="5dp"
                android:layout_gravity="top"
                android:visibility="invisible"
                android:gravity="end"
                android:background="@color/ap_transparent"
                android:text="@string/closeButtonIcon"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_weight="0"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/chestBox"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:visibility="gone"
                tools:ignore="NestedWeights">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights">

                    <ImageView
                        android:id="@+id/chestIcon"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingTop="10dp"
                        android:paddingBottom="5dp"
                        android:gravity="center"
                        android:src="@drawable/ic_sensor_chest" />

                    <TextView
                        android:id="@+id/chestStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="30dp"
                        android:layout_gravity="center|top"
                        android:text="@string/checkIcon"
                        android:textColor="@color/lightGreen"
                        android:textSize="25sp"
                        android:background="@drawable/rounded_edit_text"
                        android:outlineProvider="none"
                        android:backgroundTint="@color/white"
                        app:tint="@color/lightGreen" />

                </FrameLayout>

                <TextView
                    android:id="@+id/chestName"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="5dp"
                    android:layout_weight="5"
                    android:textColor="@color/white"
                    android:paddingTop="-15dp"
                    android:paddingBottom="-15dp"
                    android:gravity="center"
                    style="@style/TextAppearance.AppCompat.Medium" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/limbBox"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:visibility="gone"
                android:orientation="vertical">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights">

                    <ImageView
                        android:id="@+id/limbIcon"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingTop="10dp"
                        android:paddingBottom="5dp"
                        android:gravity="center"
                        android:src="@drawable/ic_sensor_limb" />

                    <TextView
                        android:id="@+id/limbStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:text="@string/warningIcon"
                        android:textColor="@color/warningYellow"
                        android:textSize="25sp"
                        android:background="@drawable/rounded_edit_text"
                        android:outlineProvider="none"
                        android:layout_gravity="center|top"
                        android:backgroundTint="@color/white"
                        app:tint="@color/lightGreen" />

                </FrameLayout>

                <TextView
                    android:id="@+id/limbName"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="5"
                    android:layout_marginTop="5dp"
                    android:textColor="@color/white"
                    android:paddingTop="-15dp"
                    android:paddingBottom="-15dp"
                    android:gravity="center"
                    style="@style/TextAppearance.AppCompat.Medium" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/bpBox"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:visibility="gone"
                android:orientation="vertical">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights">

                    <ImageView
                        android:id="@+id/bpIcon"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:paddingTop="10dp"
                        android:paddingBottom="5dp"
                        android:gravity="center"
                        android:src="@drawable/ic_sensor_bp" />

                    <TextView
                        android:id="@+id/bpStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center|top"
                        android:layout_marginStart="10dp"
                        android:text="@string/wrongIcon"
                        android:textColor="@color/warningYellow"
                        android:textSize="25sp"
                        android:background="@drawable/rounded_edit_text"
                        android:outlineProvider="none"
                        android:backgroundTint="@color/white"
                        app:tint="@color/lightGreen" />

                </FrameLayout>

                <TextView
                    android:id="@+id/bpName"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="5"
                    android:layout_marginTop="5dp"
                    android:textColor="@color/white"
                    android:paddingTop="-15dp"
                    android:paddingBottom="-15dp"
                    android:gravity="center"
                    style="@style/TextAppearance.AppCompat.Medium" />

            </LinearLayout>

        </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingBottom="10dp"
        android:layout_weight="1.2"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/connectionText"
            style="@style/TextAppearance.AppCompat.Title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="5dp"
            android:layout_weight="1"
            android:paddingStart="25dp"
            android:paddingEnd="25dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:autoSizeTextType="uniform"
            android:autoSizeMaxTextSize="22dp"
            android:autoSizeMinTextSize="16dp"
            tools:ignore="NestedWeights" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginStart="25dp"
            android:layout_marginEnd="25dp"
            android:orientation="horizontal" >

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnCancel"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:paddingStart="30dp"
                android:paddingEnd="30dp"
                android:text="@string/wait"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:visibility="invisible"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/gray"
                style="@style/TextAppearance.AppCompat.Medium"/>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnOk"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:paddingStart="30dp"
                android:paddingEnd="30dp"
                android:text="@string/ok"
                android:textColor="@color/white"
                android:visibility="invisible"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/lightBlue"
                style="@style/TextAppearance.AppCompat.Medium"/>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnConfirm"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="10dp"
                android:paddingStart="25dp"
                android:paddingEnd="25dp"
                android:text="@string/yes"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:visibility="invisible"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/lightBlue"
                style="@style/TextAppearance.AppCompat.Medium"/>

        </LinearLayout>

        <!--<androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnOk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="10dp"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:text="@string/ok"
            android:textColor="@color/white"
            android:visibility="invisible"
            android:layout_weight="0.2"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/lightBlue"
            style="@style/TextAppearance.AppCompat.Title"/>-->

    </LinearLayout>

    </LinearLayout>

</LinearLayout>