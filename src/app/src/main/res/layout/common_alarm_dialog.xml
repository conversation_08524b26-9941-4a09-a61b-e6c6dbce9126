<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="400dp"
    android:layout_height="330dp"
    android:background="@color/ap_transparent"
    android:layout_gravity="center"
    android:gravity="center">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/background"
        android:orientation="vertical" >

        <TextView
            android:id="@+id/alarmTitle"
            style="@style/TextAppearance.AppCompat.Title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="start|center"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:text="@string/connectionStatus"
            android:paddingStart="25dp"
            android:paddingEnd="0dp"
            android:layout_weight="1.6"
            tools:ignore="MissingConstraints"/>


        <ImageView
            android:id="@+id/alarmIcon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:padding="5dp"
            android:src="@drawable/ic_arrows_turn"
            app:tint="@color/red" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="10dp"
            android:layout_weight="1.2"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/alarmText"
                style="@style/TextAppearance.AppCompat.Title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingStart="25dp"
                android:paddingEnd="25dp"
                android:gravity="center"
                android:textColor="@color/white"
                android:autoSizeTextType="uniform"
                android:autoSizeMaxTextSize="22dp"
                android:autoSizeMinTextSize="16dp"
                tools:ignore="NestedWeights" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/acknowledgeBtn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="5dp"
                android:paddingStart="30dp"
                android:paddingEnd="30dp"
                android:textAllCaps="false"
                android:text="@string/resume"
                android:textColor="@color/white"
                android:layout_weight="1"
                android:background="@drawable/rounded_edit_text"
                android:backgroundTint="@color/lightBlue"
                style="@style/TextAppearance.AppCompat.Title"/>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>