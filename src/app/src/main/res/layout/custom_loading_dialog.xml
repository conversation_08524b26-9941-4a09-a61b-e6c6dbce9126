<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    android:paddingLeft="20dp"
    android:paddingRight="20dp"
    android:background="@drawable/rounded_edit_text"
    android:layout_gravity="center_horizontal"
    android:gravity="center"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:indeterminate="true"
        app:indicatorColor="@color/lightBlue"
        app:indicatorSize="105dp"/>
</LinearLayout>