<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="40dp"
    android:background="@drawable/linear_layout_bg"
    android:backgroundTint="@color/background"
    android:baselineAligned="false"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:baselineAligned="false"
        android:orientation="vertical">

        <!-- Header -->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                style="@style/TextAppearance.AppCompat.Headline"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:paddingStart="15dp"
                android:paddingTop="10dp"
                android:paddingEnd="10dp"
                android:text="@string/database"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <Button
                android:id="@+id/closeBtn"
                style="@style/TextAppearance.AppCompat.Display1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@color/ap_transparent"
                android:gravity="end"
                android:text="@string/closeButtonIcon"
                android:textColor="@color/white" />

        </LinearLayout>

        <!-- HorizontalScrollView for buttons -->

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_patient_alarm"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblPatientAlarm"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_alarm"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/alarms"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_alarm_event"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblAlarmEvent"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_event_log"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblEventsLog"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_measurement_data"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblMeasurementData"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_unsend_measurement_data"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="Unsend Measurement Data"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_patient"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblPatient"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_sensor"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblSensors"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_sensor_def"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblSensorsDef"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_vist"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/tblVisit"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_settings"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/settings"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_default_alarm_settings"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/defaultalarmsettings"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_event_list"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/eventlist"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_parameter"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:padding="10dp"
                    android:text="@string/parameter"
                    android:textAllCaps="false"
                    android:textColor="@color/ap_gray" />

            </LinearLayout>

        </HorizontalScrollView>


        <HorizontalScrollView
            android:id="@+id/headScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="10dp"
                android:layout_weight="1"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <include
                    android:id="@+id/table_patient_alarm_header"
                    layout="@layout/table_patient_alarm_header"
                    android:visibility="visible" />

                <include
                    android:id="@+id/table_alarm_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    layout="@layout/table_alarm_header"
                    android:visibility="gone"/>

                <include
                    android:id="@+id/table_alarm_event_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    layout="@layout/table_alarm_event_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_event_log_header"
                    layout="@layout/table_event_log_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_measurement_data_header"
                    layout="@layout/table_measurement_data_header"
                    android:visibility="gone" />
                <include
                    android:id="@+id/table_unsend_measurement_data_header"
                    layout="@layout/table_unsend_measurement_data_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_patient_header"
                    layout="@layout/table_patient_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_sensor_header"
                    layout="@layout/table_sensor_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_sensor_def_header"
                    layout="@layout/table_sensor_def_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_visit_header"
                    layout="@layout/table_visit_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_setting_header"
                    layout="@layout/table_setting_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_default_alarm_settings_header"
                    layout="@layout/table_default_alarm_settings_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_event_list_header"
                    layout="@layout/table_event_list_header"
                    android:visibility="gone" />

                <include
                    android:id="@+id/table_parameter_header"
                    layout="@layout/table_parameter_header"
                    android:visibility="gone" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/datas"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </LinearLayout>

        </HorizontalScrollView>

    </LinearLayout>

</LinearLayout>


