<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:layout_constraintTop_toTopOf="@id/navLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/layout1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:baselineAligned="false"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints,NotSibling"
        tools:layout_editor_absoluteX="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:baselineAligned="false"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="2"
                android:orientation="vertical"
                android:paddingStart="15dp"
                android:paddingEnd="0dp"
                tools:ignore="NestedWeights">

                <TextView
                    android:id="@+id/textView"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_gravity="start"
                    android:layout_marginTop="10dp"
                    android:gravity="start|center"
                    android:text="@string/fallParameter"
                    android:textColor="@color/ap_gray" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:gravity="start"
                    android:text="@string/activityModes"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <RadioGroup
                        android:id="@+id/modeRadioGroup"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        tools:ignore="UselessParent">

                        <RadioButton
                            android:id="@+id/bedBoundRadioButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/bed_bound"
                            android:textColor="@color/ap_gray"
                            android:buttonTint="@color/lightBlue"
                            android:layout_marginEnd="5dp"
                            android:checked="true" />

                        <RadioButton
                            android:id="@+id/activeRadioButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:buttonTint="@color/lightBlue"
                            android:text="@string/active"
                            android:textColor="@color/ap_gray"
                            tools:ignore="TouchTargetSizeCheck,VisualLintBounds" />

                    </RadioGroup>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingEnd="0dp"
                tools:ignore="NestedWeights">

                <Switch
                    android:id="@+id/fallSwitch"
                    android:layout_width="50dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center|start"
                    android:layout_marginTop="10dp"
                    android:checked="true"
                    android:gravity="start|center"
                    android:scaleX="1.2"
                    android:scaleY="1.2"
                    tools:ignore="MissingConstraints,UseSwitchCompatOrMaterialXml" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:paddingStart="0dp"
            android:baselineAligned="false"
            tools:ignore="RtlSymmetry">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="2"
                android:orientation="vertical"
                android:paddingStart="15dp"
                android:paddingEnd="0dp"
                tools:ignore="NestedWeights,UselessParent">

                <androidx.legacy.widget.Space
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_gravity="start"
                    android:layout_marginTop="10dp"
                    android:gravity="start|center"
                    android:textColor="@color/ap_gray" />

                <TextView
                    android:id="@+id/turnTimerLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="20dp"
                    android:gravity="start"
                    android:text="@string/ActivityTimer"
                    android:textColor="@color/ap_gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp">

                    <Spinner
                        android:id="@+id/activity_timer_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="90dp"
                        android:background="@drawable/spinner_bg"
                        android:dropDownVerticalOffset="41dp"
                        android:gravity="center"
                        android:paddingStart="10dp"
                        android:paddingEnd="10dp"
                        android:popupBackground="@color/gray"
                        android:spinnerMode="dropdown"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>