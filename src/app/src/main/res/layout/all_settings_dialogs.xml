<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/Theme.AppCompat"
    android:background="@color/ap_transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="40dp"
        android:background="@drawable/linear_layout_bg">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/navLayout"
            android:paddingStart="5dp"
            android:paddingEnd="0dp"
            android:orientation="horizontal"
            tools:ignore="MissingConstraints">

            <include
                layout="@layout/settings_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/closeBtn"
                android:layout_weight="15"
                android:gravity="end"
                android:paddingEnd="20dp"
                android:background="@color/ap_transparent"
                style="@style/TextAppearance.AppCompat.Display1"
                android:textColor="@color/white"
                android:text="@string/closeButtonIcon"/>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/settings_fragment_holder"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>