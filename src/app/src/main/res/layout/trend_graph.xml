<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="40dp"
        android:background="@drawable/linear_layout_bg"
        app:layout_constraintTop_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:gravity="start"
            android:layout_weight="9">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:textColor="@color/text_grey"
                android:text="Data Interval"/>
            <Spinner
                android:id="@+id/trend_duration"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_weight="1.75"
                android:layout_marginTop="5dp"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:layout_gravity="center"
                android:popupBackground="@color/components_gray"
                android:dropDownVerticalOffset="41dp"
                android:gravity="center"
                android:background="@drawable/spinner_bg"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                tools:ignore="NestedWeights" />

            <TextView
                android:id="@+id/date"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center|bottom"
                android:padding="10dp"
                android:textColor="@color/white"
                android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                android:autoSizeTextType="uniform"
                android:autoSizeMinTextSize="15sp"
                android:autoSizeMaxTextSize="20sp"
                android:layout_weight="1"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.45"
                android:gravity="end"
                android:paddingTop="10dp">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_previous"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_weight="1"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/white"
                    android:text="@string/angleLeftDouble"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/lightBlue" />
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_backward"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_weight="1"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/white"
                    android:text="@string/angleLeft"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/lightBlue" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_forward"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/white"
                    android:text="@string/angleRight"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/lightBlue" />
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_next"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_weight="1"
                    android:layout_marginStart="10dp"
                    android:textColor="@color/white"
                    android:text="@string/angleRightDouble"
                    style="@style/TextAppearance.AppCompat.Medium"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/lightBlue" />

            </LinearLayout>

            <Button
                android:id="@+id/trend_close_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.8"
                android:layout_marginStart="10dp"
                android:layout_gravity="center|end"
                android:gravity="end"
                android:background="@color/ap_transparent"

                android:text="@string/closeButtonIcon"
                android:textColor="@color/white"
                style="@style/TextAppearance.AppCompat.Display1"
                tools:ignore="InefficientWeight" />
<!--            android:theme="@style/fontAwesomeText"-->

        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="10dp"
            android:layout_weight="1">

            <LinearLayout
                android:id="@+id/outerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="false">

                <LinearLayout
                    android:id="@+id/chartViewLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/hr_chart_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        tools:ignore="NestedWeights">

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/hrChart"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/gray"
                            android:visibility="invisible" />

                        <TextView
                            android:id="@+id/hr_chart_text"
                            style="@style/noConnectionText"
                            android:text="@string/noDataToDisplay" />

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/rr_chart_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        android:padding="5dp"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp">

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/rrChart"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/gray"
                            android:visibility="invisible" />

                        <TextView
                            android:id="@+id/rr_chart_text"
                            style="@style/noConnectionText"
                            android:text="@string/noDataToDisplay" />

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/spo2_chart_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        android:padding="5dp"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp">

                        <com.github.mikephil.charting.charts.LineChart
                            android:layout_width="match_parent"
                            android:id="@+id/spo2Chart"
                            android:layout_height="match_parent"
                            android:background="@color/gray"
                            android:visibility="invisible" />

                        <TextView
                            android:id="@+id/spo2_chart_text"
                            style="@style/noConnectionText"
                            android:text="@string/noDataToDisplay" />

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/temp_chart_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        android:padding="5dp"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp">

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/tempChart"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/gray"
                            android:visibility="invisible" />

                        <TextView
                            android:id="@+id/temp_chart_text"
                            style="@style/noConnectionText"
                            android:text="@string/noDataToDisplay" />

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/bpSys_chart_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        android:padding="5dp"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp">

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/bpSysChart"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/gray"
                            android:visibility="invisible" />

                        <TextView
                            android:id="@+id/bpSys_chart_text"
                            style="@style/noConnectionText"
                            android:text="@string/noDataToDisplay" />

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/bpDia_chart_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        android:padding="5dp"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp">

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/bpDiaChart"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@color/gray"
                            android:visibility="invisible" />

                        <TextView
                            android:id="@+id/bpDia_chart_text"
                            style="@style/noConnectionText"
                            android:text="@string/noDataToDisplay" />

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/rightPanel"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="4"
                    android:orientation="vertical">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/hr_val_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        tools:ignore="NestedWeights">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="10dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="9"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:text="@string/hr"
                                    tools:ignore="TooDeepLayout" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:textColor="@color/lightGreen"
                                    android:gravity="end"
                                    android:text="@string/bpm"
                                    android:layout_weight="1"/>

                            </LinearLayout>

                            <TextView
                                android:id="@+id/trend_hr"
                                style="@style/trendSideBarVitals"
                                android:layout_weight="2.8"
                                android:visibility="invisible"
                                android:textColor="@color/lightGreen" />

                            <TextView
                                android:id="@+id/hr_time"
                                style="@style/footerTopLabels"
                                android:gravity="center"
                                android:layout_weight="9"
                                android:text="@string/noValue"
                                tools:ignore="TooDeepLayout" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/rr_val_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        tools:ignore="NestedWeights">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="10dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="9"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:text="@string/resp"
                                    tools:ignore="TooDeepLayout" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:textColor="@color/white"
                                    android:gravity="end"
                                    android:text="@string/bpm"
                                    android:layout_weight="1"/>

                            </LinearLayout>

                            <TextView
                                android:id="@+id/trend_rr"
                                style="@style/trendSideBarVitals"
                                android:layout_weight="2.8"
                                android:visibility="invisible"
                                android:textColor="@color/white" />

                            <TextView
                                android:id="@+id/rr_time"
                                style="@style/footerTopLabels"
                                android:gravity="center"
                                android:layout_weight="9"
                                android:text="@string/noValue"
                                tools:ignore="TooDeepLayout" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/spo2_val_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        tools:ignore="NestedWeights">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="10dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="8"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:text="@string/spo2"
                                    tools:ignore="TooDeepLayout" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:textColor="@color/spo2Blue"
                                    android:gravity="end"
                                    android:text="@string/percentage"
                                    android:layout_weight="1"/>

                            </LinearLayout>

                            <TextView
                                android:id="@+id/trend_spo2"
                                style="@style/trendSideBarVitals"
                                android:layout_weight="2.8"
                                android:visibility="invisible"
                                android:textColor="@color/spo2Blue" />

                            <TextView
                                android:id="@+id/spo2_time"
                                style="@style/footerTopLabels"
                                android:gravity="center"
                                android:layout_weight="8"
                                android:text="@string/noValue"
                                tools:ignore="TooDeepLayout" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/temp_val_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        tools:ignore="NestedWeights">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="10dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="9"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:text="@string/temp"
                                    tools:ignore="TooDeepLayout" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:textColor="@color/white"
                                    android:gravity="end"
                                    android:text="@string/fahrenheit"
                                    android:layout_weight="1"/>

                            </LinearLayout>

                            <TextView
                                android:id="@+id/trend_temp"
                                style="@style/trendSideBarVitals"
                                android:layout_weight="2.8"
                                android:visibility="invisible"
                                android:textColor="@color/white" />

                            <TextView
                                android:id="@+id/temp_time"
                                style="@style/footerTopLabels"
                                android:gravity="center"
                                android:layout_weight="9"
                                android:text="@string/noValue"
                                tools:ignore="TooDeepLayout" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/bpSys_val_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        tools:ignore="NestedWeights">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="10dp"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="8"
                                android:orientation="horizontal">

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:text="@string/bpSys"
                                    tools:ignore="TooDeepLayout" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:textColor="@color/lightGreen"
                                    android:gravity="end"
                                    android:visibility="invisible"
                                    android:layout_weight="1"/>

                            </LinearLayout>

                            <TextView
                                android:id="@+id/trend_bpSys"
                                style="@style/trendSideBarVitals"
                                android:layout_weight="2.8"
                                android:visibility="invisible"
                                android:textColor="@color/pink" />

                            <TextView
                                android:id="@+id/bpSys_time"
                                style="@style/footerTopLabels"
                                android:gravity="center"
                                android:layout_weight="8"
                                android:text="@string/noValue"
                                tools:ignore="TooDeepLayout" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/bpDia_val_outer"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="5dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/gray"
                        app:cardCornerRadius="4dp"
                        app:strokeColor="@color/gray"
                        app:strokeWidth="2dp"
                        tools:ignore="NestedWeights">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="10dp"
                            android:orientation="vertical"
                            tools:ignore="TooManyViews">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="9"
                                android:orientation="horizontal"
                                tools:ignore="TooManyViews">

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:layout_weight="1"
                                    android:text="@string/bpDia"
                                    tools:ignore="TooDeepLayout" />

                                <TextView
                                    style="@style/footerTopLabels"
                                    android:textColor="@color/lightGreen"
                                    android:gravity="end"
                                    android:visibility="invisible"
                                    android:layout_weight="1"/>

                            </LinearLayout>

                            <TextView
                                android:id="@+id/trend_bpDia"
                                style="@style/trendSideBarVitals"
                                android:layout_weight="2.8"
                                android:visibility="invisible"
                                android:textColor="@color/pink" />

                            <TextView
                                android:id="@+id/bpDia_time"
                                style="@style/footerTopLabels"
                                android:gravity="center"
                                android:layout_weight="9"
                                android:text="@string/noValue"
                                tools:ignore="TooDeepLayout" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

    </LinearLayout>

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/trendAnimation"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:lottie_rawRes="@raw/wifi_animation"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>