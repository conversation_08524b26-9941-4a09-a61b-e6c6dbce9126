<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="100dp"
    android:layout_height="100dp"
    android:background="@color/ap_transparent"
    android:layout_gravity="center"
    android:gravity="center">
    <LinearLayout
        android:layout_width="420dp"
        android:layout_height="250dp"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/background"
        android:orientation="vertical" >
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingStart="5dp"
        android:paddingEnd="0dp"
        android:layout_weight="1.6"
        style="@style/TextAppearance.AppCompat.Medium"
        >
        <TextView
            android:id="@+id/alertTitle"
            style="@style/TextAppearance.AppCompat.Title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_weight="1.5"
            android:textStyle="bold"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/closeBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="5dp"
            android:layout_gravity="top"
            android:gravity="end"
            android:background="@color/ap_transparent"
            android:text="@string/closeButtonIcon"
            android:textColor="@color/white"
            style="@style/TextAppearance.AppCompat.Headline"
            android:layout_weight="0"/>
    </LinearLayout>


    <TextView
        android:id="@+id/alertMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:layout_marginStart="20dp"
        android:layout_gravity="start"
        android:layout_marginBottom="30dp"
        android:textSize="16sp"
        android:layout_marginTop="20dp"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/alertOkButton"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:text="Ok"
        android:layout_gravity="center"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/rounded_edit_text"
        android:backgroundTint="@color/lightBlue"
        android:textColor="@android:color/white"/>
</LinearLayout>
</LinearLayout>