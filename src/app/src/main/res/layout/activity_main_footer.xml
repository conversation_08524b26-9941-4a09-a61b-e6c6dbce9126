<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/footer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="bottom"
    android:layout_marginBottom="5dp"
    android:background="@color/background"
    android:baselineAligned="false"
    android:orientation="horizontal">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/appLogo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1.02"
        android:backgroundTint="@color/fullTransparency"
        android:outlineProvider="none"
        app:cardCornerRadius="4dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/app_name"
            android:gravity="center"
            android:src="@drawable/app_logo" />

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/bpSysCard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:layout_weight=".95"
        android:visibility="gone"
        app:strokeWidth="2dp"
        app:strokeColor="@color/gray"
        app:cardBackgroundColor="@color/gray"
        app:cardCornerRadius="4dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/bpDiaCard"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:strokeWidth="3dp"
            app:strokeColor="@color/gray"
            app:cardBackgroundColor="@color/ap_transparent"
            app:cardCornerRadius="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="-2dp"
                    android:layout_weight="1.5"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/bpLabel"
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/NIBP"
                        tools:ignore="NestedWeights" />
                    <TextView
                        android:id="@+id/bp_auto_time"
                        style="@style/footerTopLabels"
                        android:gravity="end|center"
                        android:layout_weight="1"
                        tools:ignore="SmallSp"
                        app:drawableEndCompat="@drawable/timer"
                        android:drawablePadding="5dp"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/bpVitalLayer"
                    style="@style/footerMiddleLayer"
                    android:orientation="horizontal"
                    tools:ignore="TooManyViews">

                    <TextView
                        android:id="@+id/bpSys"
                        style="@style/footerVitals"
                        android:layout_weight="1"
                        android:gravity="end|center_vertical"
                        android:text="@string/noValue"
                        android:textColor="@color/pink"
                        android:visibility="visible"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/line"
                        style="@style/footerVitals"
                        android:layout_weight="1.5"
                        android:gravity="center"
                        android:text="@string/foreSlash"
                        android:textColor="@color/pink"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/bpDia"
                        style="@style/footerVitals"
                        android:layout_weight="1"
                        android:gravity="start|center_vertical"
                        android:textColor="@color/pink"
                        tools:ignore="NestedWeights" />

                </LinearLayout>

                <TextView
                    android:id="@+id/bpTime"
                    style="@style/footerTopLabels"
                    android:layout_marginTop="-2dp"
                    android:layout_weight="1.5"
                    android:text="@string/noValue"
                    android:gravity="top|center"
                    tools:ignore="SmallSp,TooManyViews" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/bpErrorCard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:layout_weight=".95"
        android:padding="10dp"
        app:strokeWidth="2dp"
        app:strokeColor="@color/gray"
        app:cardBackgroundColor="@color/gray"
        app:cardCornerRadius="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="10dp"
            android:paddingTop="5dp"
            android:paddingEnd="10dp"
            android:paddingBottom="5dp">
        <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:layout_weight="1.3">
             <TextView
                android:id="@+id/nibpLabel"
                style="@style/footerTopLabels"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="@string/NIBP"
                tools:ignore="NestedWeights,SmallSp"
                />
            <TextView
        android:id="@+id/bp_auto_time_tv"
        style="@style/footerTopLabels"
        android:gravity="end|center"
        android:layout_weight="1"
        tools:ignore="SmallSp"
        app:drawableEndCompat="@drawable/timer"
        android:drawablePadding="5dp"/>
</LinearLayout>

            <androidx.legacy.widget.Space
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="-8dp"
                android:layout_weight="1.8" />

            <LinearLayout
                style="@style/footerMiddleLayer"
                android:orientation="horizontal"
                tools:ignore="TooManyViews">

                <TextView
                    android:id="@+id/bpError"
                    style="@style/noConnectionText"
                    android:paddingStart="5dp"
                    android:paddingEnd="5dp"
                    android:text="@string/no_bp_sensor_connected"
                    android:backgroundTint="@color/components_gray"
                    android:gravity="center|center_vertical"/>

            </LinearLayout>


            <TextView
                android:id="@+id/bpMeasuringStatus"
                style="@style/footerTopLabels"
                android:layout_weight="1.4"
                android:text="Measuring..."
                android:visibility="invisible"
                android:gravity="center"
                tools:ignore="SmallSp,TooManyViews" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:layout_weight="0.7" >

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/bedPositionCard"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:layout_weight="0.80"
            android:visibility="gone"
            app:strokeWidth="2dp"
            app:strokeColor="@color/gray"
            app:cardBackgroundColor="@color/gray"
            app:cardCornerRadius="4dp"
            tools:ignore="NestedWeights">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="-2dp"
                    android:layout_weight="1.5"
                    android:orientation="horizontal"
                    tools:ignore="TooManyViews">

                    <TextView
                        android:id="@+id/textView4"
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/rollPosition"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/turnTimeSetting"
                        style="@style/footerTopLabels"
                        android:gravity="end|center"
                        android:layout_weight="3"
                        tools:ignore="SmallSp"
                        app:drawableEndCompat="@drawable/timer"
                        android:drawablePadding="5dp"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/positionVitalLayer"
                    style="@style/footerMiddleLayer"
                    android:orientation="horizontal"
                    tools:ignore="TooManyViews">

                    <TextView
                        android:id="@+id/bodyAngle"
                        style="@style/footerVitals"
                        android:layout_weight="1.1"
                        android:gravity="center|center_vertical"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/left"
                        style="@style/footerVitals"
                        android:layout_weight="1.375"
                        android:gravity="center"
                        android:text="@string/L"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights,TooManyViews" />

                    <TextView
                        android:id="@+id/back"
                        style="@style/footerVitals"
                        android:layout_weight="1.375"
                        android:gravity="center"
                        android:text="@string/B"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/right"
                        style="@style/footerVitals"
                        android:layout_weight="1.375"
                        android:gravity="center"
                        android:text="@string/R"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/turnTime"
                        style="@style/footerVitals"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:paddingStart="5dp"
                        android:paddingEnd="2dp"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights,VisualLintBounds" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="-2dp"
                    android:layout_weight="1.5"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/text_second_bottom"
                        style="@style/footerTopLabels"
                        android:layout_weight="1.1"
                        android:gravity="top|center"
                        android:text="@string/rollAngle"
                        tools:ignore="NestedWeights" />

                    <TextView
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:gravity="top|center"
                        android:text="@string/position" />

                    <TextView
                        android:id="@+id/labelTurnTime"
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:text="@string/turnTime"
                        android:gravity="top|center"/>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/bedActivityCard"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:layout_weight="2"
            android:visibility="gone"
            app:strokeColor="@color/gray"
            app:strokeWidth="2dp"
            app:cardBackgroundColor="@color/gray"
            app:cardCornerRadius="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="-2dp"
                    android:layout_weight="1.5"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/textView5"
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/activity"
                        tools:ignore="NestedWeights" />

                    <Chronometer
                        android:id="@+id/positionTime"
                        style="@style/footerTopLabels"
                        android:layout_weight="8"
                        android:gravity="center"
                        android:textColor="@color/gray"
                        tools:ignore="NestedWeights,VisualLintBounds" />

                </LinearLayout>

                <TextView
                    android:id="@+id/bed_fall_alert"
                    style="@style/footerVitals"
                    android:layout_weight="1.3"
                    android:gravity="center|center_vertical"
                    android:textColor="@color/white"
                    android:layout_marginTop="-5dp"
                    android:layout_marginBottom="-7dp"
                    tools:ignore="TooManyViews"/>

                <TextView
                    style="@style/footerTopLabels"
                    android:layout_marginTop="-2dp"
                    android:layout_weight="1.5"
                    android:gravity="top|center"
                    android:text="@string/fallAlert"
                    tools:ignore="NestedWeights" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/activePositionCard"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:layout_weight="1"
            app:strokeWidth="2dp"
            app:strokeColor="@color/gray"
            app:cardBackgroundColor="@color/gray"
            app:cardCornerRadius="4dp"
            tools:ignore="NestedWeights">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp">

                <TextView
                    style="@style/footerTopLabels"
                    android:layout_marginBottom="-2dp"
                    android:layout_weight="1.5"
                    android:gravity="center|start"
                    android:text="@string/uprightPosition"
                    tools:ignore="NestedWeights" />

                <LinearLayout
                    style="@style/footerMiddleLayer"
                    android:orientation="horizontal"
                    tools:ignore="TooManyViews">

                    <TextView
                        android:id="@+id/activeAngle"
                        style="@style/footerVitals"
                        android:layout_weight="1.7"
                        android:gravity="center|center_vertical"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/bodyPosition"
                        style="@style/footerVitals"
                        android:textAllCaps="false"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/noValue"
                        android:textColor="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="-2dp"
                    android:layout_weight="1.5"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/footerTopLabels"
                        android:layout_weight="1.7"
                        android:gravity="top|center"
                        android:text="@string/angle"
                        tools:ignore="NestedWeights" />

                    <TextView
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:gravity="top|center"
                        android:text="@string/position" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/activeActivityCard"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:layout_weight="1.1"
            app:strokeColor="@color/gray"
            app:strokeWidth="2dp"
            app:cardBackgroundColor="@color/gray"
            app:cardCornerRadius="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingStart="10dp"
                android:paddingTop="5dp"
                android:paddingEnd="10dp"
                android:paddingBottom="5dp">

                <TextView
                    style="@style/footerTopLabels"
                    android:layout_marginBottom="-2dp"
                    android:layout_weight="1.5"
                    android:gravity="center|start"
                    android:text="@string/activity"
                    tools:ignore="NestedWeights" />

                <LinearLayout
                    style="@style/footerMiddleLayer"
                    android:orientation="horizontal"
                    tools:ignore="TooManyViews">

                    <TextView
                        android:id="@+id/stepCount"
                        style="@style/footerVitals"
                        android:layout_weight="1"
                        android:gravity="center|center_vertical"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/active_fall_count"
                        style="@style/footerVitals"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:paddingStart="5dp"
                        android:paddingEnd="2dp"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="-2dp"
                    android:layout_weight="1.5"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:gravity="top|center"
                        android:text="@string/step_count"
                        tools:ignore="NestedWeights" />

                    <TextView
                        style="@style/footerTopLabels"
                        android:layout_weight="1"
                        android:gravity="top|center"
                        android:text="@string/fallAlert"
                        tools:ignore="SmallSp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/tempCard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:layout_weight="1"
        app:strokeWidth="2dp"
        app:strokeColor="@color/gray"
        app:cardBackgroundColor="@color/gray"
        app:cardCornerRadius="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="10dp"
            android:paddingTop="5dp"
            android:paddingEnd="10dp"
            android:paddingBottom="5dp">

            <TextView
                style="@style/footerTopLabels"
                android:layout_weight="1.5"
                android:layout_marginBottom="-2dp"
                android:text="@string/temp"
                tools:ignore="NestedWeights,SmallSp" />

            <TextView
                android:id="@+id/temp"
                style="@style/footerVitals"
                android:layout_weight="1.3"
                android:gravity="center|center_vertical"
                android:textColor="@color/white"
                android:layout_marginTop="-5dp"
                android:layout_marginBottom="-7dp"
                android:visibility="invisible"
                tools:ignore="TooManyViews"/>

            <TextView
                android:id="@+id/tempSensor"
                style="@style/footerTopLabels"
                android:layout_marginTop="-2dp"
                android:layout_weight="1.5"
                android:gravity="top|center"
                android:text="@string/noValue"
                tools:ignore="NestedWeights" />

        </LinearLayout>


    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/pauseAlarm"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:layout_weight=".99"
        app:cardBackgroundColor="@color/gray"
        app:cardCornerRadius="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="5dp"
            android:paddingTop="5dp"
            android:paddingEnd="5dp"
            android:paddingBottom="5dp">

            <androidx.legacy.widget.Space
                style="@style/footerTopLabels"
                android:layout_weight="1.5"
                android:layout_marginBottom="-2dp"
                tools:ignore="NestedWeights,SmallSp"/>

            <ImageView
                android:id="@+id/img_alarm"
                style="@style/footerTopLabels"
                android:layout_marginTop="-20dp"
                android:layout_weight="1.3"
                android:padding="5dp"
                android:layout_gravity="center"
                android:src="@drawable/pause"
                app:tint="@color/white" />

            <TextView
                style="@style/footerTopLabels"
                android:layout_marginTop="-2dp"
                android:layout_weight="1.5"
                android:gravity="top|center"
                android:text="@string/pauseAlarms" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/scanCard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="5dp"
        android:layout_weight="1"
        android:backgroundTint="@color/gray"
        android:padding="5dp"
        app:cardCornerRadius="4dp"
        tools:ignore="NestedWeights">

        <TextView
            android:id="@+id/textConnect"
            style="@style/TextAppearance.AppCompat.Medium"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:autoSizeMaxTextSize="30dp"
            android:autoSizeMinTextSize="18dp"
            android:autoSizeTextType="uniform"
            android:background="@drawable/rounded_edit_text"
            android:backgroundTint="@color/gray"
            android:gravity="center"
            android:padding="5dp"
            android:text="@string/tapToConnect"
            android:textColor="@color/white"
            android:textStyle="bold" />

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>

