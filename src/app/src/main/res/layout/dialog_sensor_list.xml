<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/main_layout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:background="@drawable/linear_layout_bg"
            android:backgroundTint="@color/background"
            android:baselineAligned="false"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:baselineAligned="false"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:layout_weight=".9"
                    android:orientation="vertical"
                    tools:ignore="UselessParent">

                    <TextView
                        style="@style/TextAppearance.AppCompat.Headline"
                        android:layout_width="match_parent"
                        android:layout_height="70dp"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:paddingEnd="10dp"
                        android:text="@string/manageSensors"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="65dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:weightSum="10"
                        tools:context=".MainActivity">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2.8"
                            android:gravity="start"
                            android:text="@string/sensorType"
                            android:textColor="@color/white"
                            android:textSize="15sp" />

                        <LinearLayout
                            android:id="@+id/toggleChest"
                            android:layout_width="0dp"
                            android:layout_height="35dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="2.4"
                            android:background="@drawable/toggle_selector"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:textColor="@drawable/toggle_text_selector">

                            <ImageView
                                android:id="@+id/toggleChestPic"
                                android:layout_width="wrap_content"
                                android:layout_height="20dp"
                                android:clickable="true"
                                android:focusable="true"
                                android:src="@drawable/ic_sensor_chest"
                                app:tint="@color/white" />

                            <androidx.appcompat.widget.AppCompatToggleButton
                                android:id="@+id/toggleChestButton"
                                android:layout_width="wrap_content"
                                android:layout_height="30dp"
                                android:layout_marginStart="5dp"
                                android:background="@drawable/toggle_selector"
                                android:checked="true"
                                android:onClick="onToggleClicked"
                                android:text="@string/chestText"
                                android:textColor="@drawable/toggle_text_selector"
                                android:textOff="@string/chestText"
                                android:textOn="@string/chestText" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/toggleLimb"
                            android:layout_width="0dp"
                            android:layout_height="35dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="2.4"
                            android:background="@drawable/toggle_selector"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:textColor="@drawable/toggle_text_selector">

                            <ImageView
                                android:id="@+id/toggleLimbPic"
                                android:layout_width="wrap_content"
                                android:layout_height="20dp"
                                android:clickable="true"
                                android:focusable="true"
                                android:src="@drawable/ic_sensor_limb"
                                app:tint="@color/white" />

                            <androidx.appcompat.widget.AppCompatToggleButton
                                android:id="@+id/toggleLimbButton"
                                android:layout_width="wrap_content"
                                android:layout_height="30dp"
                                android:layout_marginStart="5dp"
                                android:background="@drawable/toggle_selector"
                                android:checked="false"
                                android:onClick="onToggleClicked"
                                android:text="@string/limbText"
                                android:textColor="@drawable/toggle_text_selector"
                                android:textOff="@string/limbText"
                                android:textOn="@string/limbText" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/toggleBP"
                            android:layout_width="0dp"
                            android:layout_height="35dp"
                            android:layout_weight="2.4"
                            android:background="@drawable/toggle_selector"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:textColor="@drawable/toggle_text_selector">

                            <ImageView
                                android:id="@+id/toggleBPPic"
                                android:layout_width="wrap_content"
                                android:layout_height="20dp"
                                android:clickable="true"
                                android:focusable="true"
                                android:src="@drawable/ic_sensor_bp"
                                app:tint="@color/white" />

                            <androidx.appcompat.widget.AppCompatToggleButton
                                android:id="@+id/toggleBPButton"
                                android:layout_width="wrap_content"
                                android:layout_height="30dp"
                                android:layout_marginStart="5dp"
                                android:background="@drawable/toggle_selector"
                                android:checked="false"
                                android:onClick="onToggleClicked"
                                android:text="@string/bpText"
                                android:textColor="@drawable/toggle_text_selector"
                                android:textOff="@string/bpText"
                                android:textOn="@string/bpText" />
                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="10">

                        <androidx.appcompat.widget.SearchView
                            android:id="@+id/searchBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="8"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/components_gray"
                            android:imeOptions="actionSearch"
                            android:inputType="number"
                            app:autoShowKeyboard="false"
                            app:iconifiedByDefault="false"
                            app:queryBackground="@drawable/rounded_edit_text"
                            app:queryHint="search sensor to connect"
                            app:closeIcon="@drawable/ic_close_24"
                            android:clickable="true"
                            app:searchIcon="@drawable/ic_search" />

                        <!--<androidx.appcompat.widget.AppCompatButton
                            android:layout_width="40dp"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:background="@drawable/rounded_edit_text"
                            android:text="@string/refreshIcon"
                            android:textSize="25sp"
                            android:textColor="@color/white"
                            android:theme="@style/fontAwesomeText" />-->

                        <ImageButton
                            android:id="@+id/refreshButton"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_margin="5dp"
                            android:layout_weight="1.5"
                            android:background="@drawable/rounded_edit_text"
                            android:scaleType="fitCenter"
                            android:scaleX="0.7"
                            android:scaleY="0.7"
                            android:src="@drawable/arrows_rotate_solid"
                            app:tint="@color/white" />

                    </LinearLayout>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginVertical="10dp"
                        android:layout_weight="2"
                        android:backgroundTint="@color/ap_transparent"
                        app:cardElevation="0dp"
                        tools:ignore="NestedWeights">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="60dp"
                            android:layout_marginTop="2dp"
                            android:gravity="center_horizontal"
                            android:text="@string/available_sensors"
                            android:textColor="@color/white"
                            android:textSize="20sp"
                            android:textStyle="bold" />
                        <!--<ProgressBar
                            android:id="@+id/scanningProgress"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_gravity="center"
                            style="@style/Animation.Design.BottomSheetDialog"
                            android:indeterminateTint="@color/white" />-->

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginTop="30dp"
                            android:background="@color/lightBlue" />



                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/sensorsList"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="45dp"
                            android:layout_marginBottom="50dp"
                            android:choiceMode="multipleChoice"
                            android:divider="@color/ap_transparent"
                            android:dividerHeight="10dp"
                            tools:ignore="NestedWeights" />
                        <TextView
                            android:id="@+id/noSensorAvailable"
                            style="@style/TextAppearance.AppCompat.Title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:layout_gravity="center"
                            android:layout_marginBottom="30dp"
                            android:text="@string/no_sensor_available"
                            android:background="@color/background"
                            android:textColor="@color/ap_gray"
                            android:visibility="visible" />
                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    tools:ignore="NestedWeights">

                    <Button
                        android:id="@+id/closeBtn"
                        style="@style/TextAppearance.AppCompat.Display1"
                        android:layout_width="60dp"
                        android:layout_height="70dp"
                        android:layout_gravity="end"
                        android:background="@color/ap_transparent"
                        android:gravity="center"
                        android:paddingEnd="20dp"
                        android:text="@string/closeButtonIcon"
                        android:textColor="@color/white"
                        tools:ignore="NestedWeights" />

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:outlineProvider="none"
                        app:cardBackgroundColor="@android:color/transparent"
                        tools:ignore="NestedWeights">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="123dp">

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="30dp"
                                    android:paddingStart="130dp"
                                    android:paddingEnd="10dp"

                                    android:text="@string/connected_sensors"
                                    android:textColor="@color/white"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    tools:ignore="TooDeepLayout" />

                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="1dp"
                                    android:layout_marginTop="0dp"
                                    android:layout_marginEnd="20dp"
                                    android:layout_marginBottom="5dp"
                                    android:background="@color/lightBlue" />



                                <ScrollView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:baselineAligned="false"
                                        android:orientation="vertical"
                                        tools:ignore="TooDeepLayout">
                                        <TextView
                                            android:id="@+id/noSensorText"
                                            style="@style/TextAppearance.AppCompat.Title"
                                            android:layout_width="match_parent"
                                            android:layout_height="match_parent"
                                            android:layout_gravity="center_horizontal"
                                            android:gravity="center_horizontal"
                                            android:layout_marginTop="90dp"
                                            android:text="@string/noSensorsConnected"
                                            android:textColor="@color/ap_gray"
                                            android:visibility="visible" />
                                        <LinearLayout
                                            android:id="@+id/chestBoxOuter"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="0dp"
                                            android:layout_marginTop="5dp"
                                            android:layout_marginEnd="20dp"
                                            android:layout_marginBottom="5dp"
                                            android:layout_weight="0.3"
                                            android:background="@drawable/rounded_corner_view"
                                            android:backgroundTint="@color/components_gray"
                                            android:gravity="center"
                                            android:orientation="horizontal"
                                            android:paddingStart="20dp"
                                            android:paddingTop="15dp"
                                            android:paddingBottom="15dp"
                                            android:paddingEnd="0dp"
                                            android:visibility="gone">

                                            <ImageView
                                                android:layout_width="wrap_content"
                                                android:layout_height="match_parent"
                                                android:layout_weight="0.3"
                                                android:src="@drawable/ic_sensor_chest"
                                                app:tint="@color/white" />

                                            <TextView
                                                android:id="@+id/chestText"
                                                style="@style/TextAppearance.AppCompat.Medium"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_gravity="center"
                                                android:layout_marginStart="10dp"
                                                android:layout_weight="1.6"
                                                android:text="@string/chestSensor"
                                                android:textColor="@color/white" />

                                            <androidx.appcompat.widget.AppCompatCheckBox
                                                android:id="@+id/chestBox"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:button="@drawable/custom_check_box2"/>
                                        </LinearLayout>

                                        <LinearLayout
                                            android:id="@+id/limbBoxOuter"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="0dp"
                                            android:layout_marginEnd="20dp"
                                            android:layout_marginBottom="5dp"
                                            android:layout_weight="0.3"
                                            android:background="@drawable/rounded_corner_view"
                                            android:backgroundTint="@color/components_gray"
                                            android:gravity="center"
                                            android:orientation="horizontal"
                                            android:paddingStart="20dp"
                                            android:paddingTop="15dp"
                                            android:paddingBottom="15dp"
                                            android:paddingEnd="0dp"
                                            android:visibility="gone">

                                            <ImageView
                                                android:layout_width="wrap_content"
                                                android:layout_height="match_parent"
                                                android:layout_weight="0.3"
                                                android:src="@drawable/ic_sensor_limb"
                                                app:tint="@color/white" />

                                            <TextView
                                                android:id="@+id/limbText"
                                                style="@style/TextAppearance.AppCompat.Medium"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_gravity="center"
                                                android:layout_marginStart="10dp"
                                                android:layout_weight="1.6"
                                                android:text="@string/limbSensor"
                                                android:textColor="@color/white" />


                                            <androidx.appcompat.widget.AppCompatCheckBox
                                                android:id="@+id/limbBox"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:button="@drawable/custom_check_box2" />

                                        </LinearLayout>

                                        <LinearLayout
                                            android:id="@+id/bpBoxOuter"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="0dp"
                                            android:layout_marginEnd="20dp"
                                            android:layout_marginBottom="5dp"
                                            android:layout_weight="0.3"
                                            android:background="@drawable/rounded_corner_view"
                                            android:backgroundTint="@color/components_gray"
                                            android:gravity="center"
                                            android:orientation="horizontal"
                                            android:paddingStart="20dp"
                                            android:paddingTop="15dp"
                                            android:paddingBottom="15dp"
                                            android:paddingEnd="0dp"
                                            android:visibility="gone">

                                            <ImageView
                                                android:layout_width="wrap_content"
                                                android:layout_height="match_parent"
                                                android:layout_weight="0.3"
                                                android:src="@drawable/ic_sensor_bp"
                                                app:tint="@color/white" />

                                            <TextView
                                                android:id="@+id/bpText"
                                                style="@style/TextAppearance.AppCompat.Medium"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:layout_gravity="center"
                                                android:layout_marginStart="10dp"
                                                android:layout_weight="1.6"
                                                android:text="@string/bpSensor"
                                                android:textColor="@color/white" />


                                            <androidx.appcompat.widget.AppCompatCheckBox
                                                android:id="@+id/bpBox"
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:button="@drawable/custom_check_box2"  />

                                        </LinearLayout>
                                    </LinearLayout>

                                </ScrollView>
                                <!--
                                                                <LinearLayout
                                                                    android:layout_width="wrap_content"
                                                                    android:layout_height="0dp"
                                                                    android:layout_weight="3"
                                                                    android:orientation="horizontal">-->

                                <!--  </LinearLayout>-->

                            </LinearLayout>

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:id="@+id/linearLayout2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="5"
            android:baselineAligned="false"
            android:gravity="center"
            android:orientation="horizontal"
            android:background="@drawable/linear_layout_bg"
            android:backgroundTint="@color/background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="10dp"
            >

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.2"
                android:orientation="horizontal"
                tools:ignore="NestedWeights">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="start|bottom"
                    android:layout_marginStart="20dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:paddingStart="30dp"
                    android:paddingEnd="30dp"
                    android:text="@string/cancel"
                    android:textAllCaps="false"
                    android:textColor="@color/white" />

                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/connectBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="end"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:paddingStart="30dp"
                    android:paddingEnd="30dp"
                    android:text="@string/connect"
                    android:textAllCaps="false"
                    android:textColor="@color/white" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.3"
                android:gravity="center|end">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="10dp"
                    android:layout_weight="0.2"
                    android:background="@drawable/rounded_edit_text"
                    android:orientation="horizontal"
                    android:padding="0dp">

                    <TextView
                        android:id="@+id/sensorAuthtextView"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="start"
                        android:layout_marginEnd="10dp"
                        android:gravity="center"
                        android:text="@string/sensor_authentication"
                        android:textColor="@color/white"
                        android:visibility="invisible" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/sensorAuthenticationCheck"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="start"
                        android:checked="false"
                        android:gravity="start|center"
                        android:scaleX="1.2"
                        android:scaleY="1.2"
                        android:visibility="invisible"
                        tools:ignore="MissingConstraints,UseSwitchCompatOrMaterialXml" />
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/remove"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="end|bottom"
                    android:layout_marginEnd="20dp"
                    android:background="@drawable/rounded_edit_text"
                    android:backgroundTint="@color/components_gray"
                    android:paddingStart="30dp"
                    android:paddingEnd="30dp"
                    android:text="@string/remove"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:visibility="visible" />

            </LinearLayout>

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>