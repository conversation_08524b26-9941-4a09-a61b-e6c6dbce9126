<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="2.5dp"
        android:paddingBottom="2.5dp"
        android:orientation="horizontal">

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.2"
            android:textColor="@color/white"
            android:text="Id"
            android:textStyle="bold"
            android:padding="10dp"
            android:gravity="center"/>

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/white"
            android:text="EventUuid"
            android:textStyle="bold"
            android:padding="10dp"
            android:gravity="center"/>

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.2"
            android:textColor="@color/white"
            android:text="AlarmId"
            android:textStyle="bold"
            android:padding="10dp"
            android:gravity="center"/>

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.2"
            android:textColor="@color/white"
            android:text="Value"
            android:textStyle="bold"
            android:padding="10dp"
            android:gravity="center"/>

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/white"
            android:text="UpdatedTime"
            android:textStyle="bold"
            android:padding="10dp"
            android:gravity="center"/>

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.17"
            android:textColor="@color/white"
            android:text="AlarmStatus"
            android:textStyle="bold"
            android:padding="10dp"
            android:gravity="center"/>

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1.17"
            android:textColor="@color/white"
            android:text="UploadStatus"
            android:textStyle="bold"
            android:padding="10dp"
            android:gravity="center"/>

        <TextView
            android:background="@drawable/textview_border"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/white"
            android:text="UploadTime"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

    </LinearLayout>

</HorizontalScrollView>
