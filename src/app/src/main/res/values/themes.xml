<resources>

    <style name="Theme.SibelPatch" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryVariant">@color/black</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:privateImeOptions">disableToolbar=true</item>
        <!-- Customize your theme here. -->
        <item name="android:itemBackground">@android:color/black</item>
        <item name="android:itemTextAppearance">@style/customTextMenu</item>
    </style>

    <style name="customTextMenu" parent="@android:style/TextAppearance.Widget.IconMenu.Item">
        <item name="android:textColor">@android:color/white</item>
    </style>

    <style name="splashScreenTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="fontFamily">@font/open_sans_variable_font</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:statusBarColor">@color/black</item>
    </style>

    <style name="toolbarTitle" parent="@style/TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:textColor">@color/white</item>
        <item name="autoSizeTextType">uniform</item>
        <item name="autoSizeMaxTextSize">100sp</item>
        <item name="autoSizeMinTextSize">80sp</item>
        <item name="android:theme">@style/ThemeOverlay.AppCompat.Dark</item>
    </style>

    <style name="Toolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">23sp</item>
    </style>

    <style name="footerMiddleLayer">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">1.3</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_marginTop">-5dp</item>
        <item name="android:layout_marginBottom">-7dp</item>
    </style>

    <style name="boldText" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="fontFamily">@font/open_sans_bold_font</item>
        <item name="android:text">@string/noValue</item>
        <item name="android:textStyle">bold</item>
        <item name="autoSizeTextType">uniform</item>
        <item name="autoSizeMaxTextSize">300sp</item>
        <item name="autoSizeMinTextSize">17sp</item>
        <item name="android:gravity">clip_vertical</item>
    </style>

    <style name="noConnectionText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:paddingBottom">5dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:autoSizeTextType">uniform</item>
        <item name="android:autoSizeMaxTextSize">70sp</item>
        <item name="android:autoSizeMinTextSize">10sp</item>
        <item name="android:background">@drawable/connection_info_text_views</item>
    </style>

    <style name="footerTopLabels">
        <item name="textAppearanceBodyLarge">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:text">@string/noValue</item>
        <item name="android:textStyle">bold</item>
        <item name="autoSizeTextType">uniform</item>
        <item name="autoSizeMaxTextSize">300sp</item>
        <item name="autoSizeMinTextSize">14sp</item>
        <item name="android:gravity">clip_vertical</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="footerVitals" parent="boldText">
        <item name="textAppearanceBodyLarge">@style/TextAppearance.AppCompat.Headline</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="sideBarVitals" parent="boldText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Display4</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">bottom|center</item>
        <item name="autoSizeMaxTextSize">300sp</item>
        <item name="autoSizeMinTextSize">75sp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>

    <style name="trendSideBarVitals" parent="boldText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Display3</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">bottom|center</item>
        <item name="autoSizeMaxTextSize">300sp</item>
        <item name="autoSizeMinTextSize">75sp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
    </style>



    <style name="splashScreenTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="splashScreenTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="splashScreenTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="SpinnerStyle" parent="Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/spinner_bg</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
    </style>

    <style name="customDatePickerDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@drawable/rounded_popup_view</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/components_gray</item>
        <item name="android:colorControlNormal">@color/white</item>
        <item name="colorControlActivated">@color/lightBlue</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:colorAccent">@color/components_gray</item>
    </style>

    <style name="CustomSwitchStyle" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="android:thumbTint">@color/lightBlue</item>
        <item name="android:trackTint">@color/lightBlue</item>
    </style>

    <style name="DialogUpAndDownAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
    </style>

    <style name="DialogLeftToRightAnimation">
        <item name="android:windowEnterAnimation">@android:anim/slide_in_left</item>
        <item name="android:windowExitAnimation">@android:anim/slide_out_right</item>
    </style>

    <style name="otpEditTextTheme">
        <item name="android:layout_width">60dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:layout_marginEnd">10dp</item>
        <item name="android:gravity">center</item>
        <item name="android:inputType">number</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:background">@drawable/rounded_edit_text</item>
        <item name="android:backgroundTint">@color/gray</item>
    </style>

</resources>