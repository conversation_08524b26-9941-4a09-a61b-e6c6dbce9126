<resources>
    <!--Font awesome icon values-->
    <string name="closeButtonIcon">\uf00d</string>
    <string name="upIcon">\uf077</string>
    <string name="downIcon">\uf078</string>
    <string name="rightArrow">\uf061</string>
    <string name="angleRight">\uf105</string>
    <string name="angleLeft">\uf104</string>
    <string name="angleRightDouble">\uf101</string>
    <string name="angleLeftDouble">\uf100</string>
    <string name="refreshIcon">\uf021</string>
    <string name="checkIcon">\uf058</string>
    <string name="wrongIcon">\uf057</string>
    <string name="warningIcon">\uf06a</string>
    <string name="cloud_upload">\uf0ee</string>
    <string name="cloud_error">\uf071</string>

    <!-- Normal Strings -->
    <string name="connect">Connect</string>
    <string name="spacelabsHub">UNKNOWN</string>
    <string name="wifi">Wifi</string>
    <string name="username">Username</string>
    <string name="password">Password</string>
    <string name="submit">Submit</string>
    <string name="cancel">Cancel</string>
    <string name="remove">Remove</string>
    <string name="wait">Wait</string>
    <string name="replace">Replace</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="manageSensors">Manage Sensors</string>
    <string name="paired_sensors">Paired Sensors</string>
    <string name="sensor_scan_instruction">Choose a sensor type from the above toolbar to begin the search</string>
    <string name="noSensorsConnected">No Sensor Connected</string>
    <string name="chestSensor">Chest Sensor - </string>
    <string name="limbSensor">Limb Sensor - </string>
    <string name="bpSensor">BP Monitor - </string>
    <string name="start_scan">Start Scan</string>
    <string name="stop_scan">Stop Scan</string>
    <string name="angle">Angle</string>
    <string name="bpm">BPM</string>
    <string name="percentage">%</string>
    <string name="fahrenheit">°F</string>
    <string name="degrees">°</string>
    <string name="position">Position</string>
    <string name="turnTime">Turn Time</string>
    <string name="fallAlert">Fall Alert</string>
    <string name="pauseAlarms">Pause Alarms</string>
    <string name="temp_">Temp</string>
    <string name="calibrating">Calibrating</string>
    <string name="chest">CHEST</string>
    <string name="limb">LIMB</string>
    <string name="bp">BP2A</string>
    <string name="filter_by_type">Filter By Type</string>
    <string name="connectionLost">Connection Lost</string>
    <string name="leadOff">Lead Off</string>
    <string name="poorSkinContact">Poor Skin Contact</string>
    <string name="no_chest_sensor_connected">No Chest Sensor Connected</string>
    <string name="no_limb_sensor_connected">No Limb Sensor Connected</string>
    <string name="no_bp_sensor_connected">No Bp Sensor Connected</string>
    <string name="monitorOff">Monitor Off</string>
    <string name="noDataToDisplay">No Data Found</string>
    <string name="loading">Loading..</string>
    <string name="noValue"><span>-?-</span></string>
    <string name="cough_count">COUGH COUNT</string>
    <string name="accel_y">ACCEL_Y</string>
    <string name="accel_z">ACCEL_Z</string>
    <string name="ecg">ECG</string>
    <string name="ecgParameter">ECG Parameter</string>
    <string name="ecgVolume">ECG Volume</string>
    <string name="respirations">Respirations</string>
    <string name="ecgZoom">0.25 x</string>
    <string name="fall_count">Fall Count</string>
    <string name="ppg_ir">PPG IR</string>
    <string name="ppg_red">PPG RED</string>
    <string name="body_position">Body Position</string>
    <string name="per">30\u00B0</string>
    <string name="cough">COUGH</string>
    <string name="hr">HR</string>
    <string name="highHr">High HR</string>
    <string name="extremeHighHr">Extreme High HR</string>
    <string name="lowHr">Low HR</string>
    <string name="extremeLowHr">Extreme Low HR</string>
    <string name="nibp">---/--</string>
    <string name="NIBP">NIBP</string>
    <string name="bpSys">BP Sys</string>
    <string name="bpDia">BP Dia</string>
    <string name="resp">RESP</string>
    <string name="h">1 h</string>
    <string name="respParameter">RESP Parameter</string>
    <string name="highResp">High RESP</string>
    <string name="lowResp">Low RESP</string>
    <string name="respVolume">RESP Volume</string>
    <string name="spo2">SpO2</string>
    <string name="spo2Parameter">SpO2 Parameter</string>
    <string name="spo2Volume">SpO2 Volume</string>
    <string name="highSpo2">High SpO2</string>
    <string name="lowSpo2">Low SpO2</string>
    <string name="temperature">Temperature</string>
    <string name="temp">TEMP</string>
    <string name="tempParameter">TEMP Parameter</string>
    <string name="tempVolume">TEMP Volume</string>
    <string name="highTemp">High TEMP</string>
    <string name="lowTemp">Low TEMP</string>
    <string name="settings">Settings</string>
    <string name="legalInformation">Legal Information</string>
    <string name="softwareVersion">Software version\t\t:\t\t</string>
    <string name="releaseDate">Released on\t:\t</string>
    <string name="androidId">Android ID\t\t\t\t\t\t:\t\t</string>
    <string name="bloodPressure">Blood Pressure</string>
    <string name="bpParameter">BP Parameter</string>
    <string name="highBpSys">High BP Sys</string>
    <string name="highBpDia">High BP Dia</string>
    <string name="lowBpSys">Low BP Sys</string>
    <string name="lowBpDia">Low BP Dia</string>
    <string name="bpVolume">BP Volume</string>
    <string name="foreSlash">/</string>
    <string name="step_count">Step Count</string>
    <string name="alarmVolume">Alarm Volume</string>
    <string name="critical">Critical :</string>
    <string name="high">High :</string>
    <string name="medium">Medium :</string>
    <string name="low">Low :</string>
    <string name="scan_duration_">Scan duration (s):</string>
    <string name="default_scan_time">10</string>
    <string name="serialize">Serialize</string>
    <string name="data_package">DataPackage-%1$s</string>
    <string name="time_stamp">Timestamp: </string>
    <string name="data">Data: </string>
    <string name="downloads">Downloads</string>
    <string name="patientInformation">Patient Information</string>
    <string name="patientId1">Patient ID 1</string>
    <string name="patientId2">Patient ID 2</string>
    <string name="gender">Gender</string>
    <string name="id1">ID-1</string>
    <string name="id2">ID-2</string>
    <string name="dob">Date of Birth</string>
    <string name="age">age</string>
    <string name="firstName">First Name</string>
    <string name="middleInitial">Middle Initial</string>
    <string name="lastName">Last Name</string>
    <string name="height">Height</string>
    <string name="inches">Inches</string>
    <string name="weight">Weight</string>
    <string name="lbs">Lbs</string>
    <string name="ok">OK</string>
    <string name="save">Save</string>
    <string name="discharge">Discharge</string>
    <string name="newPatient">New Patient</string>
    <string name="file_path">File Path</string>
    <string name="file_size">File Size</string>
    <string name="session_collection_id">Session Collection Id</string>
    <string name="session_collection_start_time">Session Collection Start Time</string>
    <string name="autoconnect">autoConnect</string>
    <string name="autoBp">Auto BP</string>
    <string name="send_command">Send command</string>
    <string name="enter_new_encryption_key">Enter encryption key to create/update encrypted download files.</string>
    <string name="encryption_key">Encryption key</string>
    <string name="enable_encryption">Enable file encryption</string>
    <string name="disable_encryption">Disable file encryption</string>
    <string name="encryption_configuration">Encryption Configuration</string>
    <string name="session_duration_seconds">Session Duration(Seconds)</string>
    <string name="session_interval">Session Interval(Seconds)</string>
    <string name="number_of_sessions">Number of Sessions</string>
    <string name="delay">Delay(Seconds)</string>
    <string name="unlimited_session">Unlimited Session</string>
    <string name="allAlarmPaused"> All Alarms Paused - </string>
    <string name="resume">Resume</string>
    <string name="resumingAlarmsIn">Resuming Alarms In</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="connectionStatus">Connection Status</string>
    <string name="sensorChangeRequest">Connection Delay!\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t Would you like to remove the sensor?</string>
    <string name="sensorRemovingAlertText">sensor is removed</string>
    <string name="sensor_download">Sensor Download</string>
    <string name="sensor_command">Sensor Command</string>
    <string name="sensor_session">Sensor Session</string>
    <string name="sensor_streaming">Sensor Streaming</string>
    <string name="sensor_dfu">Sensor DFU</string>
    <string name="pick_dfu_zip_file">Pick Dfu Zip File</string>
    <string name="update_dfu">Update Dfu</string>
    <string name="abort_dfu">Abort Dfu</string>
    <string name="update_firmware">Update Firmware</string>
    <string name="cancel_firmware_update">Cancel Firmware Update</string>
    <string name="filter_sensor">Filter by name and type</string>
    <string name="newSensorDiscovered">New Sensor Discovered</string>
    <string name="sensorReplaceText">Are you sure you want to replace sensor 2324 with sensor 2560?</string>
    <string name="property_view_title">Properties</string>
    <string name="connect_tab_title">Connect</string>
    <string name="command_tab_title">Command</string>
    <string name="stream_tab_title">Stream</string>
    <string name="dfu_tab_title">Dfu</string>
    <string name="session_tab_title">Session</string>
    <string name="experiment_tab_title">Experiment</string>
    <string name="clear_console">Clear</string>
    <string name="sensor_experiment">Experimental</string>
    <string name="title_activity_main2">iBSM</string>
    <string name="title_activity_main_sns">Spacelabs Hub</string>

    <!-- Strings used for fragments for navigation -->
    <string name="first_fragment_label">First Fragment</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="next">Next</string>
    <string name="skip">Skip >></string>
    <string name="previous">Previous</string>


    <!-- Strings for the UI -->
    <string name="app_name"><!--iBSM--><b>SPACELABS</b>\nHEALTHCARE</string>
    <string name="tapToConnect"><b>Manage\nSensors</b></string>
    <string name="menu_cloud">Server</string>
    <string name="menuWifi">wifi</string>
    <string name="menuBluetooth">bluetooth</string>
    <string name="menuVolume">volume</string>
    <string name="menuBattery">Battery</string>

    <string name="sensor_search_hint">Search Sensor to Connect</string>

    <string-array name="gender">
        <item>unknown</item>
        <item>Male</item>
        <item>Female</item>
        <item>Others</item>
    </string-array>

    <string-array name="timeIntervals">
        <item>2m</item>
        <item>15m</item>
        <item>30m</item>
        <item>1h</item>
        <item>2h</item>
        <item>3h</item>
        <item>4h</item>
        <item>5h</item>
    </string-array>

    <string-array name="trendIntervals">
        <item>1h</item>
        <item>2h</item>
        <item>3h</item>
        <item>4h</item>
        <item>5h</item>
        <item>6h</item>
        <item>7h</item>
    </string-array>

    <string-array name="testTimeIntervals">
        <item>1h</item>
        <item>1d</item>
        <item>5d</item>
        <item>10d</item>
        <item>14d</item>
    </string-array>

    <string-array name="dbClearanceIntervals">
        <item>1d</item>
        <item>2d</item>
        <item>3d</item>
        <item>4d</item>
        <item>5d</item>
        <item>6d</item>
        <item>7d</item>
        <item>9d</item>
        <item>10d</item>
        <item>11d</item>
        <item>12d</item>
        <item>13d</item>
        <item>14d</item>
    </string-array>

    <string-array name="updateGender">
        <item>Male</item>
        <item>Female</item>
        <item>Others</item>
    </string-array>

    <string name="hello_first_fragment">Hello first fragment</string>
    <string name="hello_second_fragment">Hello second fragment. Arg: %1$s</string>
    <string name="title_activity_sensor_config">SensorConfig</string>
    <string name="prompt">Select an item:</string>
    <string name="wifi_spinner_prompt">Select Network</string>
    <string name="activity">Activity</string>
    <string name="periodicDbClearanceTime">Periodic Data Clearance In </string>
    <string name="your_prompt_text">Select Network</string>
    <string name="alarms">Alarms</string>
    <string name="tblPatientAlarm">Patient Alarm</string>
    <string name="tblAlarmEvent">Alarm Event</string>
    <string name="tblEventsLog">Events Log</string>
    <string name="tblMeasurementData">Measurement Data</string>
    <string name="tblPatient">Patient</string>
    <string name="tblSensors">Sensors</string>
    <string name="database">Database</string>
    <string name="tblSensorsDef">SensorsDef</string>
    <string name="tblVisit">Visit</string>
    <string name="Timer">Timer</string>
    <string name="measureBp">Measure BP in every</string>
    <string name="startBp">Start manual BP</string>
    <string name="rollPosition">Roll Position</string>
    <string name="L">L</string>
    <string name="B">B</string>
    <string name="R">R</string>
    <string name="rollAngle">Roll Angle</string>
    <string name="fallParameter">Fall Parameter</string>
    <string name="activityModes">Activity Modes</string>
    <string name="uprightPosition">Upright Position</string>
    <string name="defaultalarmsettings">DefaultAlarmSettings</string>
    <string name="eventlist">EventList</string>
    <string name="parameter">Parameter</string>
    <string name="ActivityTimer">Turn Timer</string>
    <string name="bed_bound">Bed Bound</string>
    <string name="active">Active</string>
    <string name="configwifi">Configure Wifi</string>
    <string name="generateCsv">Generate CSV</string>
    <string name="replicateDb">Replicate DB</string>
    <string name="wifi_name">No wifi connected</string>
    <string name="chooseAWifi">Choose a Wi-Fi Network</string>
    <string name="sysAccountId">System Account ID</string>
    <string name="sensorType">Select Sensor Type :</string>
    <string name="reconnect">Reconnect</string>
    <string name="editIpAddress">Edit IP Address</string>
    <string name="portNumber">Port No</string>
    <string name="ipHint">Edit IP: XXX.XXX.XXX.XXX</string>
    <string name="uuidHint"> uuid: XXXXXX</string>
    <string name="sensor_authentication">Sensor Authentication</string>
    <string name="limbText">Limb</string>
    <string name="bpText">BP</string>
    <string name="chestText">Chest</string>
    <string name="available_sensors">Available Sensors</string>
    <string name="connected_sensors">Connected Sensors</string>
    <string name="hospital_name">Hospital Name</string>

    <string name="device_uuid">Device ID</string>

    <string name="no_sensor_available">No Sensor Available</string>
    <string name="sensor_selection">Sensor Selection</string>
    <string name="sensor_selection_message">Please ensure that selected sensor ID matches the one chosen for the patient.</string>
    <string name="bp_additional_message">Also take a manual measurement to ensure correct placement of the cuff.</string>
    <string name="bp_measuring_status">Measuring...</string>
    <string name="trend_graph">Trend Graph</string>
    <string name="low_battery">Low Battery</string>
    <string name="replace_sensor">Replace the sensor as soon as possible</string>
    <string name="critical_battery">Critical Battery</string>
    <string name="connect_to_power">Connect to power as soon as possible</string>
</resources>