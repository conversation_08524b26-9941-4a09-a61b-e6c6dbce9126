{"waveformObservations": [{"resourceType": "Observation", "id": "test-ecg-waveform-001", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "procedure", "display": "Procedure"}]}], "subject": {"reference": "Patient/test-patient-001", "display": "Test Patient"}, "effectiveDateTime": "2025-07-08T10:30:00+00:00", "performer": [{"reference": "Practitioner/test-practitioner", "display": "Test Practitioner"}], "device": {"reference": "Device/test-device-001", "display": "Test ECG Device"}, "component": [{"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "131143", "display": "MDC_ECG_LEAD_A"}]}, "valueSampledData": {"origin": {"value": 2048}, "period": 10, "factor": 1.612, "lowerLimit": -3300, "upperLimit": 3300, "dimensions": 1, "data": "2041 2043 2037 2047 2060 2062 2051 2023 2014 2027 2034 2033 2040 2047 2047 2053 2055 2058 2061 2064 2067 2070 2073 2076 2079 2082 2085 2088 2091 2094"}}]}, {"resourceType": "Observation", "id": "test-spo2-waveform-001", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "procedure", "display": "Procedure"}]}], "subject": {"reference": "Patient/test-patient-001", "display": "Test Patient"}, "effectiveDateTime": "2025-07-08T10:30:05+00:00", "performer": [{"reference": "Practitioner/test-practitioner", "display": "Test Practitioner"}], "device": {"reference": "Device/test-device-001", "display": "Test SpO2 Device"}, "component": [{"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "150452", "display": "MDC_PULS_OXIM_PLETH"}]}, "valueSampledData": {"origin": {"value": 1024}, "period": 20, "factor": 1.0, "lowerLimit": -2048, "upperLimit": 2048, "dimensions": 1, "data": "1020 1025 1030 1035 1040 1045 1050 1055 1060 1065 1070 1075 1080 1085 1090 1095 1100 1105 1110 1115 1120 1125 1130 1135 1140 1145 1150 1155 1160 1165"}}]}, {"resourceType": "Observation", "id": "test-resp-waveform-001", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "procedure", "display": "Procedure"}]}], "subject": {"reference": "Patient/test-patient-001", "display": "Test Patient"}, "effectiveDateTime": "2025-07-08T10:30:10+00:00", "performer": [{"reference": "Practitioner/test-practitioner", "display": "Test Practitioner"}], "device": {"reference": "Device/test-device-001", "display": "Test Respiration Device"}, "component": [{"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "151562", "display": "MDC_RESP_RATE"}]}, "valueSampledData": {"origin": {"value": 512}, "period": 50, "factor": 1.0, "lowerLimit": -1024, "upperLimit": 1024, "dimensions": 1, "data": "510 512 514 516 518 520 522 524 526 528 530 532 534 536 538 540 542 544 546 548 550 552 554 556 558 560 562 564 566 568"}}]}], "vitalObservations": [{"resourceType": "Observation", "id": "test-vitals-001", "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}]}], "subject": {"reference": "Patient/test-patient-001"}, "effectiveDateTime": "2025-07-08T10:30:00+00:00", "performer": [{"reference": "Practitioner/test-practitioner"}], "component": [{"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "147842", "display": "MDC_ECG_HEART_RATE"}]}, "valueQuantity": {"value": 72, "unit": "beats/minute", "system": "http://unitsofmeasure.org", "code": "/min"}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "151554", "display": "MDC_RESP_RATE"}]}, "valueQuantity": {"value": 16, "unit": "breaths/minute", "system": "http://unitsofmeasure.org", "code": "/min"}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "150316", "display": "MDC_SAT_O2"}]}, "valueQuantity": {"value": 98, "unit": "%", "system": "http://unitsofmeasure.org", "code": "%"}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "150364", "display": "MDC_TEMP_BODY"}]}, "valueQuantity": {"value": 36.5, "unit": "<PERSON>l", "system": "http://unitsofmeasure.org", "code": "<PERSON>l"}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "150017", "display": "MDC_PRESS_BLD_SYS"}]}, "valueQuantity": {"value": 120, "unit": "mmHg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "150018", "display": "MDC_PRESS_BLD_DIA"}]}, "valueQuantity": {"value": 80, "unit": "mmHg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "528484", "display": "MDC_DEV_SUB_SPEC_PROFILE_STEP_COUNTER"}], "text": "Step Count"}, "valueQuantity": {"value": 8500}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "264918", "display": "MDC_AI_EVT_FALL_DETECTED"}], "text": "Fall Count"}, "valueQuantity": {"value": 0}}, {"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "126977", "display": "MDC_BODY_POSITION"}], "text": "Body Position"}, "valueQuantity": {"value": "2"}}]}], "alarmObservations": [{"resourceType": "Observation", "id": "test-alarm-hr-low-001", "status": "final", "meta": {"profile": ["http://hl7.org/fhir/StructureDefinition/vitalsigns"], "tag": [{"display": "extreme", "code": "false"}, {"display": "acknowledged", "code": "false"}]}, "device": {"reference": "Device/test-device-001", "display": "Test Device"}, "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}]}], "code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "147842", "display": "MDC_ECG_HEART_RATE"}], "text": "Heart rate"}, "subject": {"reference": "Patient/test-patient-001"}, "effectivePeriod": {"start": "2025-07-08T10:35:00+00:00"}, "performer": [{"reference": "Practitioner/test-practitioner"}], "interpretation": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation", "code": "L", "display": "low"}], "text": "Below low normal"}], "component": [{"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "147842", "display": "MDC_ECG_HEART_RATE"}]}, "valueQuantity": {"value": 45, "unit": "beats/minute", "system": "http://unitsofmeasure.org", "code": "/min"}, "interpretation": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation", "code": "L", "display": "low"}], "text": "Below low normal"}], "referenceRange": [{"high": {"value": 150, "unit": "beats/minute", "code": "/min"}, "low": {"value": 50, "unit": "beats/minute", "code": "/min"}}]}]}, {"resourceType": "Observation", "id": "test-alarm-spo2-low-001", "status": "final", "meta": {"profile": ["http://hl7.org/fhir/StructureDefinition/vitalsigns"], "tag": [{"display": "extreme", "code": "false"}, {"display": "acknowledged", "code": "false"}]}, "device": {"reference": "Device/test-device-001", "display": "Test Device"}, "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}]}], "code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "150316", "display": "MDC_SAT_O2"}], "text": "Oxygen saturation"}, "subject": {"reference": "Patient/test-patient-001"}, "effectivePeriod": {"start": "2025-07-08T10:40:00+00:00"}, "performer": [{"reference": "Practitioner/test-practitioner"}], "interpretation": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation", "code": "L", "display": "low"}], "text": "Below low normal"}], "component": [{"code": {"coding": [{"system": "urn:oid:2.16.840.1.113883.6.24", "code": "150316", "display": "MDC_SAT_O2"}]}, "valueQuantity": {"value": 88, "unit": "%", "system": "http://unitsofmeasure.org", "code": "%"}, "interpretation": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v3-ObservationInterpretation", "code": "L", "display": "low"}], "text": "Below low normal"}], "referenceRange": [{"high": {"value": 100, "unit": "%", "code": "%"}, "low": {"value": 90, "unit": "%", "code": "%"}}]}]}], "technicalAlarms": [{"resourceType": "Communication", "id": "test-tech-alarm-battery-001", "status": "inprogress", "category": [{"coding": [{"system": "http://hl7.org/fhir/ValueSet/communication-category", "code": "alert", "display": "<PERSON><PERSON>"}], "text": "<PERSON><PERSON>"}], "subject": {"reference": "Patient/test-patient-001", "display": "Test Patient"}, "sent": "2025-07-08T10:45:00+00:00", "sender": {"reference": "Device/test-device-001"}, "payload": [{"contentString": "Error: LowBattery"}, {"contentString": "Alert Priority: MEDIUM"}, {"contentString": "StartTime: 2025-07-08T10:45:00+00:00"}, {"contentString": "Type: chest-sensor"}, {"contentString": "ID: TEST001"}]}, {"resourceType": "Communication", "id": "test-tech-alarm-connectivity-001", "status": "inprogress", "category": [{"coding": [{"system": "http://hl7.org/fhir/ValueSet/communication-category", "code": "alert", "display": "<PERSON><PERSON>"}], "text": "<PERSON><PERSON>"}], "subject": {"reference": "Patient/test-patient-001", "display": "Test Patient"}, "sent": "2025-07-08T10:50:00+00:00", "sender": {"reference": "Device/test-device-001"}, "payload": [{"contentString": "Error: OutOfRange"}, {"contentString": "Alert Priority: HIGH"}, {"contentString": "StartTime: 2025-07-08T10:50:00+00:00"}, {"contentString": "Type: limb-sensor"}, {"contentString": "ID: TEST002"}]}, {"resourceType": "Communication", "id": "test-tech-alarm-resolved-001", "status": "completed", "category": [{"coding": [{"system": "http://hl7.org/fhir/ValueSet/communication-category", "code": "alert", "display": "<PERSON><PERSON>"}], "text": "<PERSON><PERSON>"}], "subject": {"reference": "Patient/test-patient-001", "display": "Test Patient"}, "sent": "2025-07-08T10:55:00+00:00", "sender": {"reference": "Device/test-device-001"}, "payload": [{"contentString": "Resolved: OutOfRange"}, {"contentString": "Alert Priority: HIGH"}, {"contentString": "StartTime: 2025-07-08T10:50:00+00:00"}, {"contentString": "Type: limb-sensor"}, {"contentString": "ID: TEST002"}]}]}