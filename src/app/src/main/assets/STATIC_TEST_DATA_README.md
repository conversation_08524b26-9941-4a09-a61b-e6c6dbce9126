# Static Test Data System

This document explains how to use the static test data system in the Spacelabs Sibel Patch application for testing data transmission accuracy.

## Overview

The static test data system allows you to replace live sensor data with predefined FHIR observation data for testing purposes. This is useful for:

- Testing data transmission accuracy by comparing sent data against known expected values
- Testing without requiring live sensor connections
- Consistent, repeatable test scenarios
- Validating FHIR observation format compliance

## Components

### 1. Static Data JSON File
**Location:** `src/app/src/main/assets/static_medical_test_data.json`

Contains predefined FHIR observation data including:
- **Waveform Observations:** ECG, SpO2, Respiration with sample data arrays
- **Vital Sign Observations:** HR, RR, SpO2, Temperature, Blood Pressure, Step Count, Fall Count, Body Position
- **Alarm Observations:** Limit alarms with high/low thresholds
- **Technical Alarm Communications:** Battery, connectivity issues

### 2. Control Flag
**Location:** `CommonDataArea.useStaticTestData`

Simple boolean flag to enable/disable static test data:
```kotlin
// Enable static test data
CommonDataArea.useStaticTestData = true

// Disable static test data (use live data)
CommonDataArea.useStaticTestData = false
```

### 3. Static Test Data Manager
**Class:** `StaticTestDataManager`

Utility class that provides methods to:
- Read JSON data from assets
- Return static observations when flag is enabled
- Extract specific data types (waveforms, vitals, alarms)
- Generate test UUIDs and timestamps

## Usage Examples

### Basic Usage

```kotlin
// Enable static test data
CommonDataArea.useStaticTestData = true

// Get static data manager instance
val staticDataManager = StaticTestDataManager.getInstance()

// Check if static data should be used
if (staticDataManager.shouldUseStaticData()) {
    // Use static data for testing
    val ecgObservation = staticDataManager.getStaticECGWaveformObservation()
    val vitalObservation = staticDataManager.getStaticVitalObservation()
    val alarmObservation = staticDataManager.getStaticAlarmObservation()
}
```

### Waveform Data

```kotlin
// Get static waveform observations (full FHIR JSON)
val ecgObservation = staticDataManager.getStaticECGWaveformObservation()
val spo2Observation = staticDataManager.getStaticSpO2WaveformObservation()
val respObservation = staticDataManager.getStaticRespirationWaveformObservation()

// Get static waveform samples as DoubleArray
val ecgSamples = staticDataManager.getStaticWaveformSamples("ECG")
val spo2Samples = staticDataManager.getStaticWaveformSamples("SPO2")
val respSamples = staticDataManager.getStaticWaveformSamples("RESP")
```

### Vital Signs Data

```kotlin
// Get complete vital observation
val vitalObservation = staticDataManager.getStaticVitalObservation()

// Get specific vital values by MDC code
val heartRate = staticDataManager.getStaticVitalValue("147842") // MDC_ECG_HEART_RATE
val respRate = staticDataManager.getStaticVitalValue("151554")  // MDC_RESP_RATE
val spo2 = staticDataManager.getStaticVitalValue("150316")      // MDC_SAT_O2
val temperature = staticDataManager.getStaticVitalValue("150364") // MDC_TEMP_BODY
```

### Alarm Data

```kotlin
// Get static alarm observations
val alarmObservation = staticDataManager.getStaticAlarmObservation("HR_LOW")
val technicalAlarm = staticDataManager.getStaticTechnicalAlarm("BATTERY")
```

## Integration with Existing Code

The static test data system is designed to integrate seamlessly with existing data transmission methods without modifying their signatures. Here's how to integrate:

### Example: Waveform Transmission

```kotlin
fun sendWaveformData(samples: DoubleArray, paramType: String) {
    val staticDataManager = StaticTestDataManager.getInstance()
    
    val finalSamples = if (staticDataManager.shouldUseStaticData()) {
        // Use static samples for testing
        staticDataManager.getStaticWaveformSamples(paramType) ?: samples
    } else {
        // Use live samples
        samples
    }
    
    // Send data using existing transmission method
    transmissionMethod.send(finalSamples)
}
```

### Example: Vital Signs Transmission

```kotlin
fun sendVitalData(measurement: MeasurementData, paramCode: String) {
    val staticDataManager = StaticTestDataManager.getInstance()
    
    val finalValue = if (staticDataManager.shouldUseStaticData()) {
        // Use static value for testing
        staticDataManager.getStaticVitalValue(paramCode) ?: measurement.value
    } else {
        // Use live value
        measurement.value
    }
    
    // Create measurement with final value
    val finalMeasurement = measurement.copy(value = finalValue)
    
    // Send data using existing transmission method
    transmissionMethod.send(finalMeasurement)
}
```

## Testing Workflow

1. **Enable Static Data:**
   ```kotlin
   CommonDataArea.useStaticTestData = true
   ```

2. **Run Data Transmission:**
   - All waveform, vital, and alarm data will use static values from JSON
   - Data transmission methods work normally but with predictable test data

3. **Verify Transmitted Data:**
   - Compare transmitted FHIR observations against expected values in JSON file
   - Validate data format, structure, and values

4. **Disable Static Data:**
   ```kotlin
   CommonDataArea.useStaticTestData = false
   ```

5. **Resume Normal Operation:**
   - All data transmission returns to using live sensor data

## Demo Class

Use `StaticTestDataDemo` class to see examples and run demonstrations:

```kotlin
// Run complete demonstration
StaticTestDataDemo.runCompleteDemo()

// Toggle static data on/off
StaticTestDataDemo.toggleStaticTestData(true)

// Demonstrate specific data types
StaticTestDataDemo.demonstrateStaticWaveformData()
StaticTestDataDemo.demonstrateStaticVitalData()
StaticTestDataDemo.demonstrateStaticAlarmData()
```

## Important Notes

- **Scope:** Only provides alternative data source - does NOT modify existing UI, settings, or database code
- **Thread Safety:** StaticTestDataManager uses singleton pattern and is thread-safe
- **Initialization:** Automatically initialized in ApplicationClass.onCreate()
- **Fallback:** If static data is unavailable, methods fall back to live data
- **Testing Only:** This system is intended for testing purposes only
- **Data Classes:** When integrating with existing MeasurementData objects, modify the existing instance rather than creating new ones due to constructor requirements

## Static Data Values

The JSON file contains realistic medical values:
- **Heart Rate:** 72 bpm
- **Respiratory Rate:** 16 /min
- **SpO2:** 98%
- **Temperature:** 36.5°C
- **Blood Pressure:** 120/80 mmHg
- **Step Count:** 8500
- **Fall Count:** 0
- **Body Position:** Supine (value: 2)

All waveform data includes realistic sample arrays with proper FHIR structure including origin values, periods, limits, and dimensions.
