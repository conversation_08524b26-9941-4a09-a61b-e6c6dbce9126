plugins{
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
    id 'androidx.navigation.safeargs'
    id 'org.jetbrains.kotlin.android'
    id 'org.sonarqube' version '3.0'
}

//apply plugin: "androidx.navigation.safeargs"
def getMyVersionCode = { ->
    def code = project.hasProperty('versionCode') ? versionCode.toInteger() : 78
    println "VersionCode is set to $code"
    return code
}

def secretsPropertiesFile = rootProject.file("secrets.properties")
def secretProperties = new Properties()

if (secretsPropertiesFile.exists()) {
    secretProperties.load(new FileInputStream(secretsPropertiesFile))
} else {
    secretProperties.setProperty("SIGNING_KEYSTORE_PASSWORD", "${System.getenv('SIGNING_KEYSTORE_PASSWORD')}")
    secretProperties.setProperty("SIGNING_KEY_ALIAS", "${System.getenv('SIGNING_KEY_ALIAS')}")
    secretProperties.setProperty("SIGNING_KEY_PASSWORD", "${System.getenv('SIGNING_KEY_PASSWORD')}")
}

android {
    namespace 'com.spacelabs.app'
    compileSdk 34
    buildToolsVersion "34.0.0"

    defaultConfig {
        applicationId 'com.spacelabs.app'
        minSdkVersion 26
        targetSdkVersion 34
        versionCode getMyVersionCode()
        versionName '1.4.07806252024'
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        //buildConfigField "String", "SCI_CHART_LICENSE_KEY", "\"$SCI_CHART_LICENSE_KEY\""
    }

    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
        test.java.srcDirs += "src/test/kotlin"
        androidTest.java.srcDirs += "src/androidTest/kotlin"
    }

    buildFeatures {
        viewBinding true
    }

    signingConfigs {
        release {
            // keytool -genkey -v -keystore android-signing-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias sibel-dev
            storeFile file("/home/<USER>/Desktop/sns-api-ibsm/spacelabs-sibelPatch/src/release-keystore.jks")
            storePassword "ibsm-iomt"        //storePassword -> 6aV_K7NgGP4dxTJJ
            keyAlias "release_key"
            keyPassword "ibsm-iomt"          //keyPassword -> 6aV_K7NgGP4dxTJJ
        }
    }

    buildTypes {
        debug {
            testCoverageEnabled true
            android.dexOptions {
                javaMaxHeapSize "4g"
                jumboMode true
            }
        }
        release {
            minifyEnabled false
            shrinkResources false
            testCoverageEnabled false
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures.dataBinding = true

    lintOptions {
        disable "ContentDescription"
    }

    testOptions {
        execution 'ANDROIDX_TEST_ORCHESTRATOR'
        animationsDisabled true
        unitTests.returnDefaultValues = true
        unitTests.includeAndroidResources = true
        unitTests.all {
            useJUnitPlatform()
            jvmArgs "-noverify", "-ea"
        }
    }
}

apply plugin: "org.sonarqube"
sonarqube{
    properties{
        property "sonar.projectName", "com.spacelabs.app"
        property "sonar.projectKey", "com.spacelabs.app"
        property "sonar.host.url", "http://**********:9000"
        property "sonar.language", "kotlin"
        property "sonar.sources", "src/main/java/"
        property "sonar.login", "admin"
        property "sonar.password", "iOrbit@2023"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral() // This is essential for paperdb and most other libraries
        maven { url 'https://jitpack.io' }
    }
}




apply plugin: 'kotlin-kapt' // Apply Kotlin Annotation processing plugin

dependencies {
    implementation "io.objectbox:objectbox-kotlin:3.6.0"
    releaseImplementation "io.objectbox:objectbox-android:3.6.0"
    debugImplementation "io.objectbox:objectbox-android-objectbrowser:3.6.0"
    kapt "io.objectbox:objectbox-processor:3.6.0"

    implementation 'no.nordicsemi.android.support.v18:scanner:1.4.5'
    implementation 'no.nordicsemi.android:ble:2.6.1'
    implementation "no.nordicsemi.android:dfu:2.0.3"
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'com.google.android.material:material:1.6.1'
    implementation project(":core")
    implementation project(":bluetooth")
    implementation project(":nfc")
    implementation fileTree(dir: "libs", include: ["*.jar"])
    //noinspection GradleDependency
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4"
    implementation "org.jetbrains.kotlin:kotlin-reflect:1.7.10"
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation "androidx.constraintlayout:constraintlayout:2.1.4"
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    //noinspection GradleDependency
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    //noinspection GradleDependency
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'com.androidplot:androidplot-core:1.5.9'
    debugImplementation "androidx.fragment:fragment-testing:1.7.1"
    compileOnly "javax.annotation:jsr250-api:1.0"

    implementation "com.google.android.flexbox:flexbox:3.0.0"
    implementation "com.google.accompanist:accompanist-permissions:0.21.1-beta"

    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

    implementation 'androidx.room:room-runtime:2.5.2'
    kapt 'androidx.room:room-compiler:2.5.2'

    implementation 'androidx.room:room-ktx:2.5.2'

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'

    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.5.1'

    implementation 'android.arch.lifecycle:extensions:1.1.1'
    implementation 'com.google.android.material:material:1.8.0'

   // implementation 'info.androidhive:fontawesome:0.0.5'
    //noinspection GradleCompatible
    implementation 'com.android.support:appcompat-v7:28.0.0'
    //FhirApiDependencies
    implementation 'com.squareup.okhttp3:okhttp:4.9.0'
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'
    implementation 'com.google.code.gson:gson:2.9.0'

    //Library for local storage write and retrieve
    implementation 'io.github.pilgr:paperdb:2.7.2'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'com.airbnb.android:lottie:4.1.0'
    implementation 'com.github.tobiasschuerg:android-prefix-suffix-edit-text:1.3.1'

    implementation("com.opencsv:opencsv:5.6")

    implementation 'net.zetetic:android-database-sqlcipher:4.4.2'

    //QrCODE

    implementation ("com.google.zxing:core:3.4.1")

    //QrCODE_scan

    implementation "com.google.mlkit:barcode-scanning:17.2.0"
    implementation 'androidx.camera:camera-core:1.3.0'
    implementation 'androidx.camera:camera-camera2:1.3.0'
    implementation 'androidx.camera:camera-lifecycle:1.3.0'
    implementation 'androidx.camera:camera-view:1.4.0-alpha02'



    implementation 'com.journeyapps:zxing-android-embedded:4.2.0'
    implementation 'com.google.zxing:core:3.4.1'
//Retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.5.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.1'
    implementation 'com.squareup.retrofit2:converter-scalars:2.3.0'


}