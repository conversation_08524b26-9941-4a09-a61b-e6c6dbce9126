{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:4538222507068760478", "lastPropertyId": "23:5847589936316168932", "name": "MeasurementData", "properties": [{"id": "2:4281228703312593409", "name": "measurementUuid", "type": 9}, {"id": "3:7907257200215855266", "name": "visitId", "type": 5}, {"id": "4:3630990016714057951", "name": "sensorId", "type": 5}, {"id": "6:5725754166751914962", "name": "patientID1", "type": 9}, {"id": "7:8169552857307617788", "name": "valueType", "type": 9}, {"id": "8:3198098474612190125", "name": "paramName", "type": 9}, {"id": "9:7623819751044619480", "name": "value", "type": 8}, {"id": "10:6067380300922254298", "name": "measurementData", "type": 23}, {"id": "11:7979825712581158680", "name": "numberOfSamples", "type": 5}, {"id": "12:6491252156220895130", "name": "timestamp", "type": 9}, {"id": "13:4065395794247889038", "name": "uploadStatus", "type": 5}, {"id": "14:1574334201027524188", "name": "uploadTimestamp", "type": 9}, {"id": "15:4021134231060957418", "name": "retryCount", "type": 5}, {"id": "16:6250835146278446015", "name": "tickValue", "type": 6}, {"id": "17:158246617904204346", "name": "measurementId", "type": 6, "flags": 1}, {"id": "19:4368495202556454982", "name": "patientId", "type": 6}, {"id": "23:5847589936316168932", "name": "sln", "type": 6}], "relations": []}, {"id": "2:5102443007341759693", "lastPropertyId": "14:1358581196219440301", "name": "AlarmData", "properties": [{"id": "1:7847998308037022836", "name": "alarmId", "type": 6, "flags": 1}, {"id": "2:5692473009375704239", "name": "alarmUUID", "type": 9}, {"id": "3:4252185551540385059", "name": "patientId", "type": 6}, {"id": "4:569296207989872199", "name": "defaultAlarmId", "type": 5}, {"id": "5:4957064749801620354", "name": "patientAlarmId", "type": 6}, {"id": "6:4934179772145767031", "name": "alarmName", "type": 9}, {"id": "7:4132804332740136772", "name": "limitExceeded", "type": 8}, {"id": "8:9000125346690566026", "name": "startTime", "type": 9}, {"id": "9:1324825368184497331", "name": "endTime", "type": 9}, {"id": "10:772184668546089436", "name": "duration", "type": 6}, {"id": "11:2758383673511319685", "name": "isAcknowledged", "type": 5}, {"id": "12:533039768714895417", "name": "acknowledgedTime", "type": 9}, {"id": "13:1867437048090776312", "name": "uploadStatus", "type": 5}, {"id": "14:1358581196219440301", "name": "uploadTimeMillis", "type": 6}], "relations": []}, {"id": "3:9185508669225332383", "lastPropertyId": "9:8001176756601028412", "name": "AlarmEventData", "properties": [{"id": "1:5617985214569074452", "name": "alarmEventId", "type": 6, "flags": 1}, {"id": "2:4268093792691067796", "name": "alarmEventUUID", "type": 9}, {"id": "3:2639401341889934193", "name": "alarmId", "type": 6}, {"id": "4:5788210492413784638", "name": "value", "type": 8}, {"id": "5:5823361424797924429", "name": "updatedTime", "type": 9}, {"id": "6:2585674472525732502", "name": "alarmStatus", "type": 9}, {"id": "7:1691759827009262553", "name": "uploadStatus", "type": 5}, {"id": "8:8514118413820907370", "name": "uploadTime", "type": 9}, {"id": "9:8001176756601028412", "name": "tickValue", "type": 6}], "relations": []}], "lastEntityId": "3:9185508669225332383", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [6738603924456330312, 8061577515696997197, 8222381621241112188, 6254579871757095461, 1166207481330893123, 1699344047185912975], "retiredRelationUids": [], "version": 1}