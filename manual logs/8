---------------------------- PROCESS STARTED (4045) for package com.spacelabs.app ----------------------------
2025-07-08 15:08:12.316  4045-4045  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-08 15:08:13.254  4045-4045  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/lib/x86_64:/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 15:08:13.298  4045-4045  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb791d8) locale list changing from [] to [en-US]
2025-07-08 15:08:13.300  4045-4045  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb75038) locale list changing from [] to [en-US]
2025-07-08 15:08:13.312  4045-4045  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 15:08:13.312  4045-4045  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 15:08:13.312  4045-4045  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 15:08:13.312  4045-4045  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 15:08:13.314  4045-4045  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 15:08:13.315  4045-4045  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 15:08:13.315  4045-4045  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 15:08:13.321  4045-4045  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 15:08:13.322  4045-4045  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 15:08:13.322  4045-4045  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 15:08:13.352  4045-4049  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 15:08:13.625  4045-4045  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 15:08:14.451  4045-4045  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 15:08:14.686  4045-4045  StaticTestDataManager   com.spacelabs.app                    D  Static test data loaded successfully
2025-07-08 15:08:14.897  4045-4065  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-08 15:08:14.900  4045-4045  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb7ba78) locale list changing from [] to [en-US]
2025-07-08 15:08:14.947  4045-4065  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-08 15:08:14.948  4045-4065  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-08 15:08:14.967  4045-4065  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-08 15:08:14.974  4045-4045  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb7c0b8) locale list changing from [] to [en-US]
2025-07-08 15:08:14.981  4045-4045  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-08 15:08:15.027  4045-4045  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-08 15:08:15.059  4045-4045  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:08:15.059  4045-4045  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:08:15.336  4045-4045  nativeloader            com.spacelabs.app                    D  Load /data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!classes20.dex): ok
2025-07-08 15:08:15.622  4045-4072  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-08 15:08:15.700  4045-4070  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-08 15:08:15.714  4045-4074  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-08 15:08:15.715  4045-4084  NetworkCallback         com.spacelabs.app                    D  Network is available: 100
2025-07-08 15:08:15.715  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 15:08:15.719  4045-4082  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-08 15:08:15.727  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:08:15.728  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated=''
2025-07-08 15:08:15.728  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:08:15.728  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:08:15.731  4045-4071  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-08 15:08:15.738  4045-4084  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 1Mbps, Tx Link speed: 1Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: 2Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-08 15:08:15.740  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:08:15.740  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated=''
2025-07-08 15:08:15.740  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:08:15.740  4045-4084  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:08:15.742  4045-4045  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-08 15:08:15.747  4045-4084  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-08 15:08:15.747  4045-4084  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-08 15:08:15.749  4045-4045  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:08:15.757  4045-4070  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:08:15.772  4045-4070  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-08 15:08:15.836  4045-4070  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-08 15:08:15.867  4045-4045  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:08:15.906  4045-4045  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:08:15.948  4045-4074  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-08 15:08:15.975  4045-4071  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-08 15:08:16.001  4045-4071  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-08 15:08:16.016  4045-4071  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-08 15:08:16.033  4045-4071  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-08 15:08:16.051  4045-4070  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-08 15:08:16.070  4045-4071  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-08 15:08:16.088  4045-4074  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-08 15:08:16.152  4045-4050  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-08 15:08:16.197  4045-4074  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-08 15:08:16.263  4045-4070  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-08 15:08:16.612  4045-4070  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-08 15:08:16.839  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Setting up activity UI
2025-07-08 15:08:16.850  4045-4045  SNS_QR_DEBUG            com.spacelabs.app                    D  postInitUiActions called - enableSnsApi: true
2025-07-08 15:08:16.850  4045-4045  SNS_QR_DEBUG            com.spacelabs.app                    D  Calling generateAndDisplayQRCode() for SNS flow
2025-07-08 15:08:16.898  4045-4075  invalidKey              com.spacelabs.app                    D  DeviceUuid key is Invalid
2025-07-08 15:08:16.900  4045-4045  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: Android ID: 6fdc3252d58580a1
                                                                                                    OS Version: 16
                                                                                                    Device: Google sdk_gphone64_x86_64
                                                                                                    UUID: 803bf36c-36ad-4be6-b569-416879d541ad
                                                                                                    Passphrase: &M3XlLe#uiJm5o6*
2025-07-08 15:08:16.901  4045-4045  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: UQxCgxl9uCFQ1k70V+buzcJom0VNmSM+tLo/VYanX2yLF/Rr9I4ljWVz6Qk0kx4O4NW1AtTfCUGB
                                                                                                    7f9mKy+EBduLYLsIZgBqUlJU+MoYglURVReUn1K+QMLld1iMxrN1TRS/go6GutL99+kXzg44zGJe
                                                                                                    bpFfDloAtar9JRnvy41juwZJsXBhE9ncqsjHRqLYOsimJ7KI7wLepDfgpG1G+4LD1AGGgHQUzb39
                                                                                                    CCVUq0M5PVbZBmFAiosAFRTH4OY4R2d06C44mm0nyoXUFlnVLM/Z/ocxPW8XpSBMZDmYPYinXP/V
                                                                                                    Nn80eCMuT+mFjE0RrMNG96Xk6KZNAsPaipEgRw==
2025-07-08 15:08:16.931  4045-4075  invalidKey              com.spacelabs.app                    D  DevicePass key is Invalid
2025-07-08 15:08:17.604  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: QR code has been displayed
2025-07-08 15:08:17.604  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Activity UI setup completed
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = ''
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:08:17.612  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:08:17.614  4045-4045  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:08:17.614  4045-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:08:17.614  4045-4075  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:08:17.617  4045-4045  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:08:17.750  4045-4093  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-08 15:08:18.078  4045-4045  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:08:18.173  4045-4045  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:08:18.380  4045-4045  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-08 15:08:18.591  4045-4045  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:08:18.592  4045-4045  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:eadfbd1b: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:08:19.613  4045-4045  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:08:20.286  4045-4112  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
2025-07-08 15:08:20.605  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Delay completed, checking navigation
2025-07-08 15:08:20.605  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Activity is resumed, checking navigation
2025-07-08 15:08:20.605  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:08:20.605  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:08:20.605  4045-4045  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:08:20.606  4045-4045  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:08:20.607  4045-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:08:20.607  4045-4075  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:08:25.495  4045-4045  SNS                     com.spacelabs.app                    D  onNextButtonClickedAction: true hasAuthenticated
2025-07-08 15:08:25.497  4045-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 15:08:25.498  4045-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = 'true'
2025-07-08 15:08:25.499  4045-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 15:08:25.500  4045-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Going to QrScannerActivitySns (AccountId is empty)
2025-07-08 15:08:25.500  4045-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = QrScannerActivitySns
2025-07-08 15:08:25.500  4045-4075  SAVE_ACCOUNT_ID         com.spacelabs.app                    D  saveSettingsAndNavigate: true ,And HasAuthenticated
2025-07-08 15:08:25.500  4045-4075  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:08:25.502  4045-4045  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-08 15:08:25.685  4045-4045  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb7dcd8) locale list changing from [] to [en-US]
2025-07-08 15:08:25.692  4045-4051  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 4544KB AllocSpace bytes, 33(1180KB) LOS objects, 49% free, 5154KB/10MB, paused 6.264ms,26.238ms total 176.059ms
2025-07-08 15:08:26.240  4045-4045  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:08:26.380  4045-4045  VRI[Accoun...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:08:26.425  4045-4045  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:08:26.425  4045-4045  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:c64b1f4b: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:08:27.754  4045-4045  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:08:29.640  4045-4121  CameraManagerGlobal     com.spacelabs.app                    I  Connecting to camera service
2025-07-08 15:08:29.702  4045-4121  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:08:29.719  4045-4121  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:08:29.722  4045-4121  CameraX                 com.spacelabs.app                    W  Retry init. Start time 157055 current time 157160
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:08:29.975  4045-4045  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:08:29.975  4045-4045  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:f78eb248: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:08:30.234  4045-4121  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:08:30.235  4045-4121  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:08:30.235  4045-4121  CameraX                 com.spacelabs.app                    W  Retry init. Start time 157055 current time 157674
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:08:30.241  4045-4045  VRI[QrScan...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:08:30.737  4045-4121  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:08:30.737  4045-4121  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:08:30.738  4045-4121  CameraX                 com.spacelabs.app                    W  Retry init. Start time 157055 current time 158177
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:08:31.241  4045-4121  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:08:31.241  4045-4121  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:08:31.241  4045-4121  CameraX                 com.spacelabs.app                    W  Retry init. Start time 157055 current time 158680
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:08:31.742  4045-4121  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:08:31.742  4045-4121  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:08:31.742  4045-4121  CameraX                 com.spacelabs.app                    W  Retry init. Start time 157055 current time 159182
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:08:32.246  4045-4121  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:08:32.246  4045-4121  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:08:32.246  4045-4121  CameraX                 com.spacelabs.app                    E  The device might underreport the amount of the cameras. Finish the initialize task since we are already reaching the maximum number of retries.
2025-07-08 15:08:32.265  4045-4045  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Use case binding failed
                                                                                                    java.lang.IllegalArgumentException: Provided camera selector unable to resolve a camera for the given use case
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:609)
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:407)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.startCamera$lambda$2(CameraActivity.kt:57)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.$r8$lambda$QsmAJe7FdI1KZZ6w_B0BAiHaOXA(Unknown Source:0)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity$$ExternalSyntheticLambda1.run(Unknown Source:4)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:995)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:103)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
2025-07-08 15:09:25.430  4045-4045  WindowOnBackDispatcher  com.spacelabs.app                    W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda13@c5d9c55
2025-07-08 15:09:25.465  4045-4045  WindowOnBackDispatcher  com.spacelabs.app                    W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda13@4b3ef2d
2025-07-08 15:09:25.488  4045-4045  WindowOnBackDispatcher  com.spacelabs.app                    W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda13@c1ce339
2025-07-08 15:09:25.502  4045-4045  WindowOnBackDispatcher  com.spacelabs.app                    W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda13@3ee5c7c
2025-07-08 15:09:25.505  4045-4045  ViewRootImpl            com.spacelabs.app                    D  Skipping stats log for color mode
---------------------------- PROCESS ENDED (4045) for package com.spacelabs.app ----------------------------
