--------- beginning of crash
2025-07-08 15:16:28.899  4345-4401  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7ab479c02c38)
2025-07-08 15:16:28.899  4345-4402  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7ab479c02c38)
2025-07-08 15:16:28.900  4345-4402  libc                    com.spacelabs.app                    A  Fatal signal 6 (SIGABRT), code -1 (SI_QUEUE) in tid 4402 (hwuiTask1), pid 4345 (m.spacelabs.app)
2025-07-08 15:16:29.092   698-828   InputDispatcher         system_server                        E  channel 'cc843e com.spacelabs.app/com.spacelabs.app.activities.snsActivities.helpers.CameraActivity' ~ Channel is unrecoverably broken and will be disposed!
---------------------------- PROCESS ENDED (4345) for package com.spacelabs.app ----------------------------
---------------------------- PROCESS STARTED (4477) for package com.spacelabs.app ----------------------------
2025-07-08 15:16:32.996  4477-4477  re-initialized>         com.spacelabs.app                    W  type=1400 audit(0.0:362): avc:  granted  { execute } for  path="/data/data/com.spacelabs.app/code_cache/startup_agents/2890feb1-agent.so" dev="dm-55" ino=98630 scontext=u:r:untrusted_app:s0:c216,c256,c512,c768 tcontext=u:object_r:app_data_file:s0:c216,c256,c512,c768 tclass=file app=com.spacelabs.app
2025-07-08 15:16:33.016  4477-4477  nativeloader            com.spacelabs.app                    D  Load /data/user/0/com.spacelabs.app/code_cache/startup_agents/2890feb1-agent.so using system ns (caller=<unknown>): ok
2025-07-08 15:16:33.057  4477-4477  m.spacelabs.app         com.spacelabs.app                    W  hiddenapi: DexFile /data/data/com.spacelabs.app/code_cache/.studio/instruments-4d3ee822.jar is in boot class path but is not in a known location
2025-07-08 15:16:33.134  4477-4477  m.spacelabs.app         com.spacelabs.app                    W  Redefining intrinsic method java.lang.Thread java.lang.Thread.currentThread(). This may cause the unexpected use of the original definition of java.lang.Thread java.lang.Thread.currentThread()in methods that have already been compiled.
2025-07-08 15:16:33.134  4477-4477  m.spacelabs.app         com.spacelabs.app                    W  Redefining intrinsic method boolean java.lang.Thread.interrupted(). This may cause the unexpected use of the original definition of boolean java.lang.Thread.interrupted()in methods that have already been compiled.
2025-07-08 15:16:33.161  4477-4477  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-08 15:16:34.045  4477-4477  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/lib/x86_64:/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 15:16:34.089  4477-4477  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb791d8) locale list changing from [] to [en-US]
2025-07-08 15:16:34.091  4477-4477  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb75038) locale list changing from [] to [en-US]
2025-07-08 15:16:34.097  4477-4477  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 15:16:34.097  4477-4477  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 15:16:34.097  4477-4477  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 15:16:34.097  4477-4477  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 15:16:34.097  4477-4477  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 15:16:34.097  4477-4477  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 15:16:34.098  4477-4477  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 15:16:34.101  4477-4477  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 15:16:34.102  4477-4477  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 15:16:34.104  4477-4477  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 15:16:34.119  4477-4480  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 15:16:34.449  4477-4477  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 15:16:34.866  4477-4477  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 15:16:34.985  4477-4477  StaticTestDataManager   com.spacelabs.app                    D  Static test data loaded successfully
2025-07-08 15:16:35.009  4477-4497  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-08 15:16:35.010  4477-4477  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb7e318) locale list changing from [] to [en-US]
2025-07-08 15:16:35.080  4477-4497  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-08 15:16:35.080  4477-4497  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-08 15:16:35.091  4477-4477  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb79818) locale list changing from [] to [en-US]
2025-07-08 15:16:35.098  4477-4477  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-08 15:16:35.122  4477-4497  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-08 15:16:35.169  4477-4477  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-08 15:16:35.217  4477-4477  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:16:35.217  4477-4477  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:16:35.420  4477-4477  nativeloader            com.spacelabs.app                    D  Load /data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!classes20.dex): ok
2025-07-08 15:16:35.704  4477-4521  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-08 15:16:35.728  4477-4519  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-08 15:16:35.753  4477-4531  NetworkCallback         com.spacelabs.app                    D  Network is available: 100
2025-07-08 15:16:35.753  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 15:16:35.762  4477-4477  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-08 15:16:35.763  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:16:35.764  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-08 15:16:35.764  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:16:35.764  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:16:35.766  4477-4531  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 1Mbps, Tx Link speed: 1Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: 2Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-08 15:16:35.766  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 15:16:35.766  4477-4477  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:16:35.781  4477-4514  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-08 15:16:35.793  4477-4514  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:16:35.802  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:16:35.802  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated=''
2025-07-08 15:16:35.802  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:16:35.802  4477-4531  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:16:35.802  4477-4531  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-08 15:16:35.803  4477-4531  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-08 15:16:35.807  4477-4477  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:16:35.812  4477-4504  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-08 15:16:35.886  4477-4516  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:16:35.906  4477-4516  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-08 15:16:35.932  4477-4516  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-08 15:16:35.964  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-08 15:16:35.990  4477-4516  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-08 15:16:36.042  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-08 15:16:36.049  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-08 15:16:36.059  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-08 15:16:36.071  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-08 15:16:36.094  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-08 15:16:36.183  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-08 15:16:36.199  4477-4519  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-08 15:16:36.279  4477-4481  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-08 15:16:36.329  4477-4516  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-08 15:16:36.331  4477-4477  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:16:36.647  4477-4516  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-08 15:16:37.123  4477-4516  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-08 15:16:37.168  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Setting up activity UI
2025-07-08 15:16:37.174  4477-4477  SNS_QR_DEBUG            com.spacelabs.app                    D  postInitUiActions called - enableSnsApi: true
2025-07-08 15:16:37.178  4477-4477  SNS_QR_DEBUG            com.spacelabs.app                    D  Calling generateAndDisplayQRCode() for SNS flow
2025-07-08 15:16:37.230  4477-4477  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: Android ID: 6fdc3252d58580a1
                                                                                                    OS Version: 16
                                                                                                    Device: Google sdk_gphone64_x86_64
                                                                                                    UUID: 803bf36c-36ad-4be6-b569-416879d541ad
                                                                                                    Passphrase: &M3XlLe#uiJm5o6*
2025-07-08 15:16:37.230  4477-4477  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: DpWMDdvQcA+EAJqQ9w8CuGHEaRuNXe4oowuTbgfmT/RptTpdv+79CGaSEfzagL2MmtCfEZJQAWky
                                                                                                    MEL318MOvlhQmVzOtBr4KgTdAYYFF7KiDQ9L6miHw071HR8XUdVMnPkp4NkVAFbk8yFN4COiDFIj
                                                                                                    3aWcTBE6s8RsXR265Hbi+PD7NG/EZ04JQWs6TpI+ot/7hoCZ3PU1+iT4R9wGzyzo1IP2i/oaT0VG
                                                                                                    NKB2mc8ehXTEIMFc6iuVj5k6R+c26qrPoYRRQqfvheVSp1ESfLvvhGg8ZQv/OQInFZxNVUs9e/TR
                                                                                                    kTPvdsipZrHyqIlcKdalIGEFsivfkXxpLzfIXw==
2025-07-08 15:16:37.413  4477-4504  invalidKey              com.spacelabs.app                    D  DeviceUuid key is Invalid
2025-07-08 15:16:37.432  4477-4504  invalidKey              com.spacelabs.app                    D  DevicePass key is Invalid
2025-07-08 15:16:37.949  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: QR code has been displayed
2025-07-08 15:16:37.949  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Activity UI setup completed
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = ''
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:16:37.957  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:16:37.958  4477-4477  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:16:37.959  4477-4516  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:16:37.959  4477-4516  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:16:37.963  4477-4477  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:16:38.409  4477-4548  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-08 15:16:38.413  4477-4477  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:16:38.509  4477-4477  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:16:38.761  4477-4477  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-08 15:16:38.883  4477-4477  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:16:38.884  4477-4477  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:3024d965: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:16:39.956  4477-4477  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:16:40.214  4477-4567  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
2025-07-08 15:16:40.988  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Delay completed, checking navigation
2025-07-08 15:16:40.989  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Activity is resumed, checking navigation
2025-07-08 15:16:40.989  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:16:40.989  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:16:40.989  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:16:40.990  4477-4477  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:16:40.992  4477-4516  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:16:40.992  4477-4516  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:16:41.542  4477-4477  SNS                     com.spacelabs.app                    D  onNextButtonClickedAction: true hasAuthenticated
2025-07-08 15:16:41.543  4477-4516  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 15:16:41.544  4477-4516  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = ''
2025-07-08 15:16:41.544  4477-4516  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 15:16:41.546  4477-4516  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Staying on AccountConfigActivitySns (HasAuthenticated is empty)
2025-07-08 15:16:41.547  4477-4516  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = AccountConfigActivitySns
2025-07-08 15:16:41.548  4477-4477  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:16:41.550  4477-4516  SAVE_ACCOUNT_ID         com.spacelabs.app                    D  saveSettingsAndNavigate: true ,And HasAuthenticated
2025-07-08 15:16:41.565  4477-4504  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:16:41.578  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 15:16:41.578  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = 'true'
2025-07-08 15:16:41.578  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 15:16:41.578  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 15:16:41.578  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 15:16:41.579  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 15:16:41.579  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = 'true'
2025-07-08 15:16:41.579  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:16:41.579  4477-4477  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = 'true'
2025-07-08 15:16:41.579  4477-4477  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists: true
2025-07-08 15:16:41.584  4477-4504  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is 'true', posting NavigateToNext event
2025-07-08 15:16:41.584  4477-4504  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 15:16:41.589  4477-4504  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = 'true'
2025-07-08 15:16:41.589  4477-4504  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 15:16:41.589  4477-4504  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Going to QrScannerActivitySns (AccountId is empty)
2025-07-08 15:16:41.589  4477-4504  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = QrScannerActivitySns
2025-07-08 15:16:41.664  4477-4477  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-08 15:16:42.235  4477-4482  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 5330KB AllocSpace bytes, 39(1428KB) LOS objects, 49% free, 5688KB/11MB, paused 1.748ms,50.665ms total 201.345ms
2025-07-08 15:16:42.468  4477-4477  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:16:42.640  4477-4477  VRI[Accoun...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:16:42.689  4477-4477  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:16:42.689  4477-4477  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:33819013: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:16:43.827  4477-4477  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:16:44.564  4477-4477  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb81838) locale list changing from [] to [en-US]
2025-07-08 15:16:44.648  4477-4575  CameraManagerGlobal     com.spacelabs.app                    I  Connecting to camera service
2025-07-08 15:16:44.657  4477-4575  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:16:44.664  4477-4575  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:16:44.665  4477-4575  CameraX                 com.spacelabs.app                    W  Retry init. Start time 652074 current time 652103
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:16:44.895  4477-4477  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:16:44.896  4477-4477  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:b69d57d2: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:16:45.177  4477-4575  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:16:45.178  4477-4575  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:16:45.178  4477-4575  CameraX                 com.spacelabs.app                    W  Retry init. Start time 652074 current time 652617
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:16:45.240  4477-4477  VRI[QrScan...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:16:45.680  4477-4575  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:16:45.681  4477-4575  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:16:45.681  4477-4575  CameraX                 com.spacelabs.app                    W  Retry init. Start time 652074 current time 653120
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:16:46.184  4477-4575  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:16:46.184  4477-4575  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:16:46.184  4477-4575  CameraX                 com.spacelabs.app                    W  Retry init. Start time 652074 current time 653624
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:16:46.688  4477-4575  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:16:46.688  4477-4575  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:16:46.688  4477-4575  CameraX                 com.spacelabs.app                    W  Retry init. Start time 652074 current time 654127
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:16:47.191  4477-4575  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:16:47.191  4477-4575  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:16:47.191  4477-4575  CameraX                 com.spacelabs.app                    E  The device might underreport the amount of the cameras. Finish the initialize task since we are already reaching the maximum number of retries.
2025-07-08 15:16:47.211  4477-4477  Qr_Scanner_Activity_Sns com.spacelabs.app                    W  No cameras available, using default back camera
2025-07-08 15:16:47.212  4477-4477  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Use case binding failed
                                                                                                    java.lang.IllegalArgumentException: Provided camera selector unable to resolve a camera for the given use case
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:609)
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:407)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.startCamera$lambda$3(CameraActivity.kt:110)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.$r8$lambda$Cm54_qOtN6cBATNr181dtNFChgA(Unknown Source:0)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity$$ExternalSyntheticLambda2.run(Unknown Source:4)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:995)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:103)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
2025-07-08 15:16:47.212  4477-4477  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  All camera options failed. Device may not have any cameras available.
2025-07-08 15:16:53.983  4477-4533  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7ab479c02c38)
2025-07-08 15:16:53.983  4477-4534  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7ab479c02c38)
2025-07-08 15:16:53.984  4477-4533  libc                    com.spacelabs.app                    A  Fatal signal 6 (SIGABRT), code -1 (SI_QUEUE) in tid 4533 (hwuiTask0), pid 4477 (m.spacelabs.app)
---------------------------- PROCESS ENDED (4477) for package com.spacelabs.app ----------------------------
