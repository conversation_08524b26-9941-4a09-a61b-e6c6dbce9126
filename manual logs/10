---------------------------- PROCESS STARTED (4627) for package com.spacelabs.app ----------------------------
2025-07-08 15:19:43.925  4627-4627  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-08 15:19:44.692  4627-4627  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/lib/x86_64:/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 15:19:44.737  4627-4627  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb791d8) locale list changing from [] to [en-US]
2025-07-08 15:19:44.738  4627-4627  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb75038) locale list changing from [] to [en-US]
2025-07-08 15:19:44.743  4627-4627  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 15:19:44.743  4627-4627  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 15:19:44.743  4627-4627  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 15:19:44.743  4627-4627  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 15:19:44.743  4627-4627  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 15:19:44.743  4627-4627  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 15:19:44.743  4627-4627  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 15:19:44.745  4627-4627  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 15:19:44.746  4627-4627  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 15:19:44.747  4627-4627  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 15:19:44.775  4627-4631  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 15:19:45.058  4627-4627  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 15:19:45.601  4627-4627  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 15:19:45.769  4627-4627  StaticTestDataManager   com.spacelabs.app                    D  Static test data loaded successfully
2025-07-08 15:19:45.779  4627-4651  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-08 15:19:45.782  4627-4627  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb7e318) locale list changing from [] to [en-US]
2025-07-08 15:19:45.876  4627-4627  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb79818) locale list changing from [] to [en-US]
2025-07-08 15:19:45.883  4627-4627  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-08 15:19:45.896  4627-4651  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-08 15:19:45.896  4627-4651  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-08 15:19:45.943  4627-4651  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-08 15:19:46.004  4627-4627  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-08 15:19:46.063  4627-4627  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:19:46.064  4627-4627  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:19:46.312  4627-4627  nativeloader            com.spacelabs.app                    D  Load /data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~WlAeCqvQRjnFsAQDVxEAIw==/com.spacelabs.app-bkst5NiN8QqiZMjoTfU9Uw==/base.apk!classes20.dex): ok
2025-07-08 15:19:46.599  4627-4675  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-08 15:19:46.652  4627-4673  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-08 15:19:46.679  4627-4676  NetworkCallback         com.spacelabs.app                    D  Network is available: 100
2025-07-08 15:19:46.696  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 15:19:46.711  4627-4673  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-08 15:19:46.715  4627-4627  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-08 15:19:46.717  4627-4675  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-08 15:19:46.726  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:19:46.726  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-08 15:19:46.726  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:19:46.727  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:19:46.729  4627-4627  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:19:46.734  4627-4658  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-08 15:19:46.737  4627-4676  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 1Mbps, Tx Link speed: 1Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: 2Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-08 15:19:46.737  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:19:46.737  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated=''
2025-07-08 15:19:46.737  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:19:46.737  4627-4676  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:19:46.737  4627-4676  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-08 15:19:46.737  4627-4676  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-08 15:19:46.751  4627-4675  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:19:46.757  4627-4658  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-08 15:19:46.808  4627-4673  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-08 15:19:46.826  4627-4658  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-08 15:19:46.831  4627-4627  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:19:46.838  4627-4675  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-08 15:19:46.854  4627-4627  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:19:46.854  4627-4675  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-08 15:19:46.918  4627-4675  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-08 15:19:46.933  4627-4675  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-08 15:19:46.964  4627-4673  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-08 15:19:47.001  4627-4658  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-08 15:19:47.035  4627-4660  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-08 15:19:47.079  4627-4660  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-08 15:19:47.090  4627-4660  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-08 15:19:47.110  4627-4675  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-08 15:19:47.122  4627-4675  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-08 15:19:47.195  4627-4632  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-08 15:19:47.373  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Setting up activity UI
2025-07-08 15:19:47.376  4627-4627  SNS_QR_DEBUG            com.spacelabs.app                    D  postInitUiActions called - enableSnsApi: true
2025-07-08 15:19:47.377  4627-4627  SNS_QR_DEBUG            com.spacelabs.app                    D  Calling generateAndDisplayQRCode() for SNS flow
2025-07-08 15:19:47.419  4627-4627  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: Android ID: 6fdc3252d58580a1
                                                                                                    OS Version: 16
                                                                                                    Device: Google sdk_gphone64_x86_64
                                                                                                    UUID: 803bf36c-36ad-4be6-b569-416879d541ad
                                                                                                    Passphrase: &M3XlLe#uiJm5o6*
2025-07-08 15:19:47.419  4627-4627  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: HvDDTVjwEpHwKoTP1/W69rLC0lptl80PZhLU5CxLbDESDfTVIyv3KdvmillzBJ4+35H+JkcgmkOr
                                                                                                    F/LBL4/7pavlk4NjJ90qnYvkEDpGgycgLDptOleHqT+pIIfTSLDGZ8luefKhRwkUnLSEy9o4sn4K
                                                                                                    gKjWqOyInSlqPJDWMPJS/Ce6ejJJR1EoxcMr+PQm6xRuXOToz9iNofx7VPTYxFEcDAKf2Kw4KnlB
                                                                                                    ue/MZ3jeJyMVIknEcKkHkoZAPF8p/qzW2os6ihQAw+o4SRzhhPBbsItA2cDrmZEfX204K06DKx+/
                                                                                                    C3blxiV3rroXPoImPr4zy7B/Gru1hNLsVH4akA==
2025-07-08 15:19:47.448  4627-4660  invalidKey              com.spacelabs.app                    D  DevicePass key is Invalid
2025-07-08 15:19:47.458  4627-4675  invalidKey              com.spacelabs.app                    D  DeviceUuid key is Invalid
2025-07-08 15:19:47.967  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: QR code has been displayed
2025-07-08 15:19:47.967  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Activity UI setup completed
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = ''
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:19:47.974  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:19:47.975  4627-4627  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:19:47.975  4627-4660  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:19:47.975  4627-4660  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:19:47.977  4627-4627  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:19:47.980  4627-4627  Choreographer           com.spacelabs.app                    I  Skipped 47 frames!  The application may be doing too much work on its main thread.
2025-07-08 15:19:48.000  4627-4627  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:19:48.543  4627-4627  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:19:48.773  4627-4627  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:19:48.798  4627-4644  HWUI                    com.spacelabs.app                    I  Davey! duration=700ms; Flags=0, FrameTimelineVsyncId=31998, IntendedVsync=835482361776, Vsync=835965695090, InputEventId=0, HandleInputStart=835974556326, AnimationStart=835974558180, PerformTraversalsStart=835984638494, DrawStart=836113165129, FrameDeadline=836182361748, FrameStartTime=835974398496, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=836114174326, SyncStart=836131995873, IssueDrawCommandsStart=836132210928, SwapBuffers=836146962435, FrameCompleted=836200405598, DequeueBufferDuration=32598607, QueueBufferDuration=395909, GpuCompleted=836200405598, SwapBuffersCompleted=836192485773, DisplayPresentTime=134912152276064, CommandSubmissionCompleted=836146962435,
2025-07-08 15:19:49.231  4627-4627  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-08 15:19:49.248  4627-4627  Choreographer           com.spacelabs.app                    I  Skipped 32 frames!  The application may be doing too much work on its main thread.
2025-07-08 15:19:49.278  4627-4627  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:19:49.278  4627-4627  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:d2936887: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:19:49.483  4627-4638  HWUI                    com.spacelabs.app                    I  Davey! duration=741ms; Flags=0, FrameTimelineVsyncId=32460, IntendedVsync=836149028416, Vsync=836682361728, InputEventId=0, HandleInputStart=836689884691, AnimationStart=836689892240, PerformTraversalsStart=836692085128, DrawStart=836709101387, FrameDeadline=836682361728, FrameStartTime=836688255330, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=836709500448, SyncStart=836709891959, IssueDrawCommandsStart=836710015737, SwapBuffers=836744115498, FrameCompleted=836890607819, DequeueBufferDuration=56815, QueueBufferDuration=2097068, GpuCompleted=836889659214, SwapBuffersCompleted=836890607819, DisplayPresentTime=-67080890893625581, CommandSubmissionCompleted=836744115498,
2025-07-08 15:19:49.559  4627-4686  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-08 15:19:50.808  4627-4690  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
2025-07-08 15:19:50.968  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Delay completed, checking navigation
2025-07-08 15:19:50.968  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Activity is resumed, checking navigation
2025-07-08 15:19:50.968  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:19:50.969  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:19:50.969  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:19:50.969  4627-4627  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:19:50.970  4627-4660  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:19:50.970  4627-4660  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:20:03.185  4627-4627  SNS                     com.spacelabs.app                    D  onNextButtonClickedAction: true hasAuthenticated
2025-07-08 15:20:03.186  4627-4660  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 15:20:03.186  4627-4660  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = ''
2025-07-08 15:20:03.186  4627-4660  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 15:20:03.186  4627-4660  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Staying on AccountConfigActivitySns (HasAuthenticated is empty)
2025-07-08 15:20:03.187  4627-4660  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = AccountConfigActivitySns
2025-07-08 15:20:03.187  4627-4660  SAVE_ACCOUNT_ID         com.spacelabs.app                    D  saveSettingsAndNavigate: true ,And HasAuthenticated
2025-07-08 15:20:03.187  4627-4627  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:20:03.193  4627-4660  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = 'true'
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = 'true'
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:20:03.258  4627-4627  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = 'true'
2025-07-08 15:20:03.259  4627-4675  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is 'true', posting NavigateToNext event
2025-07-08 15:20:03.259  4627-4627  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists: true
2025-07-08 15:20:03.264  4627-4675  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 15:20:03.265  4627-4675  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = 'true'
2025-07-08 15:20:03.265  4627-4675  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 15:20:03.265  4627-4675  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Going to QrScannerActivitySns (AccountId is empty)
2025-07-08 15:20:03.265  4627-4675  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = QrScannerActivitySns
2025-07-08 15:20:03.273  4627-4633  m.spacelabs.app         com.spacelabs.app                    I  NativeAlloc concurrent mark compact GC freed 4749KB AllocSpace bytes, 33(1180KB) LOS objects, 49% free, 5065KB/10131KB, paused 1.466ms,11.895ms total 92.840ms
2025-07-08 15:20:03.286  4627-4627  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-08 15:20:03.407  4627-4627  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7ab2ceb79818) locale list changing from [] to [en-US]
2025-07-08 15:20:04.057  4627-4627  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:20:04.177  4627-4627  VRI[Accoun...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:20:04.224  4627-4627  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:20:04.224  4627-4627  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:c69a8253: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:20:05.514  4627-4627  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:20:06.545  4627-4699  CameraManagerGlobal     com.spacelabs.app                    I  Connecting to camera service
2025-07-08 15:20:06.578  4627-4699  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:20:06.579  4627-4699  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:20:06.580  4627-4699  CameraX                 com.spacelabs.app                    W  Retry init. Start time 853960 current time 854019
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:20:06.778  4627-4627  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:20:06.778  4627-4627  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:d71fed67: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:20:07.084  4627-4699  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:20:07.085  4627-4699  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:20:07.085  4627-4699  CameraX                 com.spacelabs.app                    W  Retry init. Start time 853960 current time 854524
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:20:07.099  4627-4627  VRI[QrScan...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:20:07.588  4627-4699  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:20:07.588  4627-4699  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:20:07.588  4627-4699  CameraX                 com.spacelabs.app                    W  Retry init. Start time 853960 current time 855027
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:20:08.091  4627-4699  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:20:08.091  4627-4699  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:20:08.091  4627-4699  CameraX                 com.spacelabs.app                    W  Retry init. Start time 853960 current time 855531
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:20:08.595  4627-4699  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:20:08.595  4627-4699  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:20:08.595  4627-4699  CameraX                 com.spacelabs.app                    W  Retry init. Start time 853960 current time 856034
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:20:09.098  4627-4699  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:20:09.098  4627-4699  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:20:09.098  4627-4699  CameraX                 com.spacelabs.app                    E  The device might underreport the amount of the cameras. Finish the initialize task since we are already reaching the maximum number of retries.
2025-07-08 15:20:09.113  4627-4627  Qr_Scanner_Activity_Sns com.spacelabs.app                    W  No cameras available, using default back camera
2025-07-08 15:20:09.114  4627-4627  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Use case binding failed
                                                                                                    java.lang.IllegalArgumentException: Provided camera selector unable to resolve a camera for the given use case
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:609)
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:407)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.startCamera$lambda$6(CameraActivity.kt:112)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.$r8$lambda$89ONVqiyJHL7DeWBBZZqMuXSRuY(Unknown Source:0)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity$$ExternalSyntheticLambda4.run(Unknown Source:4)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:995)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:103)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
2025-07-08 15:20:09.114  4627-4627  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  All camera options failed. Device may not have any cameras available.
2025-07-08 15:20:12.349  4627-4678  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7ab479c02c38)
2025-07-08 15:20:12.349  4627-4679  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7ab479c02c38)
2025-07-08 15:20:12.349  4627-4679  libc                    com.spacelabs.app                    A  Fatal signal 6 (SIGABRT), code -1 (SI_QUEUE) in tid 4679 (hwuiTask1), pid 4627 (m.spacelabs.app)
---------------------------- PROCESS ENDED (4627) for package com.spacelabs.app ----------------------------
