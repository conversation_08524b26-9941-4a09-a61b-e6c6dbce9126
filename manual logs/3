--------- beginning of main
--------- beginning of system
---------------------------- PROCESS STARTED (2095) for package com.spacelabs.app ----------------------------
2025-07-04 12:07:22.288  2095-2095  re-initialized>         com.spacelabs.app                    W  type=1400 audit(0.0:96): avc:  granted  { execute } for  path="/data/data/com.spacelabs.app/code_cache/startup_agents/2890feb1-agent.so" dev="dm-55" ino=98492 scontext=u:r:untrusted_app:s0:c216,c256,c512,c768 tcontext=u:object_r:app_data_file:s0:c216,c256,c512,c768 tclass=file app=com.spacelabs.app
2025-07-04 12:07:22.315  2095-2095  nativeloader            com.spacelabs.app                    D  Load /data/user/0/com.spacelabs.app/code_cache/startup_agents/2890feb1-agent.so using system ns (caller=<unknown>): ok
2025-07-04 12:07:22.336  2095-2095  m.spacelabs.app         com.spacelabs.app                    W  hiddenapi: DexFile /data/data/com.spacelabs.app/code_cache/.studio/instruments-4d3ee822.jar is in boot class path but is not in a known location
2025-07-04 12:07:22.763  2095-2095  m.spacelabs.app         com.spacelabs.app                    W  Redefining intrinsic method java.lang.Thread java.lang.Thread.currentThread(). This may cause the unexpected use of the original definition of java.lang.Thread java.lang.Thread.currentThread()in methods that have already been compiled.
2025-07-04 12:07:22.763  2095-2095  m.spacelabs.app         com.spacelabs.app                    W  Redefining intrinsic method boolean java.lang.Thread.interrupted(). This may cause the unexpected use of the original definition of boolean java.lang.Thread.interrupted()in methods that have already been compiled.
2025-07-04 12:07:22.770  2095-2095  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-04 12:07:24.623  2095-2095  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/lib/x86_64:/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-04 12:07:24.773  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x72267ab110b8) locale list changing from [] to [en-US]
2025-07-04 12:07:24.783  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x72267ab0db98) locale list changing from [] to [en-US]
2025-07-04 12:07:24.835  2095-2095  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-04 12:07:24.836  2095-2095  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-04 12:07:24.836  2095-2095  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-04 12:07:24.836  2095-2095  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-04 12:07:24.836  2095-2095  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-04 12:07:24.836  2095-2095  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-04 12:07:24.838  2095-2095  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-04 12:07:24.873  2095-2095  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-04 12:07:24.962  2095-2095  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-04 12:07:24.962  2095-2095  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-04 12:07:25.630  2095-2102  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-04 12:07:26.111  2095-2095  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-04 12:07:27.847  2095-2095  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-04 12:07:28.811  2095-2519  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-04 12:07:28.815  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x72267ab0c5b8) locale list changing from [] to [en-US]
2025-07-04 12:07:29.132  2095-2519  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-04 12:07:29.133  2095-2519  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-04 12:07:29.211  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x72267ab13c78) locale list changing from [] to [en-US]
2025-07-04 12:07:29.221  2095-2519  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-04 12:07:29.252  2095-2095  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-04 12:07:29.626  2095-2095  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-04 12:07:29.773  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-04 12:07:29.773  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-04 12:07:31.183  2095-2095  nativeloader            com.spacelabs.app                    D  Load /data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!classes20.dex): ok
2025-07-04 12:07:33.645  2095-2721  m.spacelabs.app         com.spacelabs.app                    W  Verification of java.lang.Object androidx.room.CoroutinesRoom.execute(androidx.room.RoomDatabase, boolean, android.os.CancellationSignal, java.util.concurrent.Callable, kotlin.coroutines.Continuation) took 105.046ms (190.39 bytecodes/s) (0B arena alloc)
2025-07-04 12:07:33.959  2095-2725  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-04 12:07:33.959  2095-2725  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-04 12:07:33.964  2095-2740  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-04 12:07:33.965  2095-2734  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-04 12:07:33.977  2095-2748  m.spacelabs.app         com.spacelabs.app                    I  Waiting for a blocking GC ClassLinker
2025-07-04 12:07:34.290  2095-2729  m.spacelabs.app         com.spacelabs.app                    W  Verification of double kotlin.math.MathKt__MathJVMKt.acosh(double) took 222.633ms (530.02 bytecodes/s) (0B arena alloc)
2025-07-04 12:07:34.298  2095-2771  m.spacelabs.app         com.spacelabs.app                    W  Verification of void com.spacelabs.app.database.entities.PatientTable.<init>(java.lang.Integer, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, int, java.lang.String, java.lang.Double, java.lang.Double, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, int) took 281.506ms (401.41 bytecodes/s) (0B arena alloc)
2025-07-04 12:07:34.343  2095-2728  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-04 12:07:34.477  2095-2728  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-04 12:07:34.487  2095-2729  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-04 12:07:34.577  2095-2729  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-04 12:07:34.595  2095-2108  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 3374KB AllocSpace bytes, 21(768KB) LOS objects, 49% free, 4106KB/8213KB, paused 4.364ms,162.754ms total 847.711ms
2025-07-04 12:07:34.595  2095-2748  m.spacelabs.app         com.spacelabs.app                    I  WaitForGcToComplete blocked ClassLinker on Background for 617.747ms
2025-07-04 12:07:34.602  2095-2748  m.spacelabs.app         com.spacelabs.app                    W  Verification of void com.spacelabs.app.iomt.IomtApiManager.getToken(java.lang.String, java.lang.String, com.spacelabs.app.iomt.apiHandler.interfaces.TokenCallback) took 625.511ms (71.94 bytecodes/s) (0B arena alloc)
2025-07-04 12:07:34.791  2095-2786  NetworkCallback         com.spacelabs.app                    D  Network is available: 101
2025-07-04 12:07:34.792  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-04 12:07:34.832  2095-2743  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-04 12:07:34.884  2095-2095  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-04 12:07:34.976  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-04 12:07:34.977  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-04 12:07:34.977  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-04 12:07:34.977  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-04 12:07:34.993  2095-2095  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-04 12:07:35.097  2095-2740  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-04 12:07:35.108  2095-2786  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 1Mbps, Tx Link speed: 1Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: 2Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-04 12:07:35.108  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-04 12:07:35.108  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated=''
2025-07-04 12:07:35.108  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-04 12:07:35.108  2095-2786  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-04 12:07:35.110  2095-2786  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-04 12:07:35.111  2095-2786  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-04 12:07:35.126  2095-2095  Choreographer           com.spacelabs.app                    I  Skipped 381 frames!  The application may be doing too much work on its main thread.
2025-07-04 12:07:35.182  2095-2740  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-04 12:07:35.205  2095-2740  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-04 12:07:35.223  2095-2740  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-04 12:07:35.333  2095-2743  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-04 12:07:35.348  2095-2740  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-04 12:07:35.369  2095-2743  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-04 12:07:35.416  2095-2743  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-04 12:07:35.488  2095-2740  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-04 12:07:35.585  2095-2743  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-04 12:07:35.658  2095-2743  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-04 12:07:35.709  2095-2743  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-04 12:07:35.788  2095-2740  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-04 12:07:36.038  2095-2735  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-04 12:07:36.139  2095-2140  HWUI                    com.spacelabs.app                    I  Davey! duration=7350ms; Flags=1, FrameTimelineVsyncId=8078, IntendedVsync=63195104980, Vsync=69545104726, InputEventId=0, HandleInputStart=69559010886, AnimationStart=69559024945, PerformTraversalsStart=69561516392, DrawStart=69810115447, FrameDeadline=63211771646, FrameStartTime=69557615235, FrameInterval=********, WorkloadTarget=********, SyncQueued=***********, SyncStart=***********, IssueDrawCommandsStart=***********, SwapBuffers=***********, FrameCompleted=***********, DequeueBufferDuration=18416, QueueBufferDuration=********, GpuCompleted=***********, SwapBuffersCompleted=***********, DisplayPresentTime=0, CommandSubmissionCompleted=***********,
2025-07-04 12:07:36.226  2095-2095  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-04 12:07:36.759  2095-2095  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-04 12:07:36.878  2095-2095  Choreographer           com.spacelabs.app                    I  Skipped 101 frames!  The application may be doing too much work on its main thread.
2025-07-04 12:07:37.312  2095-2140  HWUI                    com.spacelabs.app                    I  Davey! duration=2075ms; Flags=0, FrameTimelineVsyncId=9392, IntendedVsync=***********, Vsync=***********, InputEventId=0, HandleInputStart=***********, AnimationStart=***********, PerformTraversalsStart=***********, DrawStart=***********, FrameDeadline=***********, FrameStartTime=***********, FrameInterval=********, WorkloadTarget=********, SyncQueued=***********, SyncStart=***********, IssueDrawCommandsStart=***********, SwapBuffers=***********, FrameCompleted=***********, DequeueBufferDuration=13239, QueueBufferDuration=829422, GpuCompleted=***********, SwapBuffersCompleted=***********, DisplayPresentTime=0, CommandSubmissionCompleted=***********,
2025-07-04 12:07:37.356  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x72267ab110b8) locale list changing from [] to [en-US]
2025-07-04 12:07:37.493  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x72267ab142b8) locale list changing from [] to [en-US]
2025-07-04 12:07:37.495  2095-2105  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-04 12:07:37.509  2095-2095  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x72267ab161f8) locale list changing from [] to [en-US]
2025-07-04 12:07:38.117  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Setting up activity UI
2025-07-04 12:07:38.127  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  postInitUiActions called - enableSnsApi: true
2025-07-04 12:07:38.127  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Calling generateAndDisplayQRCode() for SNS flow
2025-07-04 12:07:38.127  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  generateAndDisplayQRCode() method started
2025-07-04 12:07:38.128  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Android ID retrieved: 6fdc3252d58580a1
2025-07-04 12:07:38.128  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Device UUID: 803bf36c-36ad-4be6-b569-416879d541ad
2025-07-04 12:07:38.149  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Device passphrase generated/retrieved
2025-07-04 12:07:38.159  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Device details prepared: Android ID: 6fdc3252d58580a1
                                                                                                    OS Version: 16
                                                                                                    Device: Google sdk_gphone64_x86_64
                                                                                                    UUID: 803bf36c-36ad-4be6-b569-416879d541ad
                                                                                                    Passphrase: veQYAOyv@25pvKgK
2025-07-04 12:07:38.159  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Public key available: true
2025-07-04 12:07:38.184  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Device details encrypted successfully
2025-07-04 12:07:38.184  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  Encrypted data length: 349
2025-07-04 12:07:38.347  2095-2743  invalidKey              com.spacelabs.app                    D  DevicePass key is Invalid
2025-07-04 12:07:38.436  2095-2740  invalidKey              com.spacelabs.app                    D  DeviceUuid key is Invalid
2025-07-04 12:07:41.653  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  QR code bitmap generated: true
2025-07-04 12:07:41.653  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  QR code bitmap size: 550x550
2025-07-04 12:07:41.657  2095-2095  SNS_QR_DEBUG            com.spacelabs.app                    D  QR code bitmap set to ImageView successfully
2025-07-04 12:07:41.657  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: QR code has been displayed
2025-07-04 12:07:41.658  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Activity UI setup completed
2025-07-04 12:07:41.759  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-04 12:07:41.759  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = ''
2025-07-04 12:07:41.759  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-04 12:07:41.759  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-04 12:07:41.759  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-04 12:07:41.759  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-04 12:07:41.760  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-04 12:07:41.760  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-04 12:07:41.764  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-04 12:07:41.770  2095-2095  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-04 12:07:41.771  2095-2748  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-04 12:07:41.771  2095-2748  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-04 12:07:41.776  2095-2095  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-04 12:07:41.865  2095-2095  Choreographer           com.spacelabs.app                    I  Skipped 277 frames!  The application may be doing too much work on its main thread.
2025-07-04 12:07:42.087  2095-2141  HWUI                    com.spacelabs.app                    I  Davey! duration=4813ms; Flags=0, FrameTimelineVsyncId=9728, IntendedVsync=***********, Vsync=***********, InputEventId=0, HandleInputStart=***********, AnimationStart=***********, PerformTraversalsStart=***********, DrawStart=***********, FrameDeadline=76495104448, FrameStartTime=76296908976, FrameInterval=********, WorkloadTarget=********, SyncQueued=76476777749, SyncStart=76496246093, IssueDrawCommandsStart=76496862024, SwapBuffers=***********, FrameCompleted=***********, DequeueBufferDuration=3599, QueueBufferDuration=152310, GpuCompleted=***********, SwapBuffersCompleted=***********, DisplayPresentTime=-758752932686528930, CommandSubmissionCompleted=***********,
2025-07-04 12:07:42.091  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event StopScanningAnimation
2025-07-04 12:07:42.093  2095-2095  UiEvent                 com.spacelabs.app                    D  ACCOUNT_ACTIVITY_UNKNOWN EVENT -> StopScanningAnimation
2025-07-04 12:07:42.147  2095-2095  WifiFound               com.spacelabs.app                    D  [ESS]
2025-07-04 12:07:42.228  2095-2095  ProcessScanResults      com.spacelabs.app                    D  0
2025-07-04 12:07:42.249  2095-2095  WIFI_SSID               com.spacelabs.app                    D  addWifiDiscovered: "AndroidWifi"
2025-07-04 12:07:42.258  2095-2095  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-04 12:07:42.268  2095-2095  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=false newVisibility=true
2025-07-04 12:07:42.291  2095-3100  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
2025-07-04 12:07:42.302  2095-3097  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-04 12:07:42.540  2095-2095  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-04 12:07:42.849  2095-2095  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-04 12:07:43.630  2095-2095  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-04 12:07:43.839  2095-2095  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-04 12:07:43.865  2095-2095  Choreographer           com.spacelabs.app                    I  Skipped 41 frames!  The application may be doing too much work on its main thread.
2025-07-04 12:07:43.898  2095-2095  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-04 12:07:43.899  2095-2095  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:7910453e: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-04 12:07:43.927  2095-2140  HWUI                    com.spacelabs.app                    I  Davey! duration=734ms; Flags=0, FrameTimelineVsyncId=12074, IntendedVsync=77611771070, Vsync=78295104376, InputEventId=0, HandleInputStart=78297029389, AnimationStart=78297037319, PerformTraversalsStart=78298065676, DrawStart=78323766160, FrameDeadline=78178437714, FrameStartTime=78296422919, FrameInterval=********, WorkloadTarget=********, SyncQueued=78324186831, SyncStart=78324241072, IssueDrawCommandsStart=78324344971, SwapBuffers=78325840725, FrameCompleted=78346487811, DequeueBufferDuration=18087, QueueBufferDuration=2108021, GpuCompleted=78346487811, SwapBuffersCompleted=78342400902, DisplayPresentTime=-64710545687045858, CommandSubmissionCompleted=78325840725,
2025-07-04 12:07:44.372  2095-2108  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 2382KB AllocSpace bytes, 11(400KB) LOS objects, 49% free, 5026KB/10052KB, paused 22.491ms,28.927ms total 498.834ms
2025-07-04 12:07:44.658  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Delay completed, checking navigation
2025-07-04 12:07:44.658  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Activity is resumed, checking navigation
2025-07-04 12:07:44.659  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-04 12:07:44.659  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-04 12:07:44.659  2095-2095  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-04 12:07:44.660  2095-2095  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-04 12:07:44.660  2095-2740  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-04 12:07:44.660  2095-2740  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
