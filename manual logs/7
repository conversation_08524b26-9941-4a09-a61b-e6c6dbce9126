---------------------------- PROCESS STARTED (4269) for package com.spacelabs.app ----------------------------
2025-07-08 12:07:11.910  4269-4269  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/lib/x86_64:/data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 12:07:11.958  4269-4269  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7c34b16eda18) locale list changing from [] to [en-US]
2025-07-08 12:07:11.960  4269-4269  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7c34b16f3198) locale list changing from [] to [en-US]
2025-07-08 12:07:11.970  4269-4269  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 12:07:11.970  4269-4269  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 12:07:11.970  4269-4269  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 12:07:11.970  4269-4269  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 12:07:11.971  4269-4269  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 12:07:11.971  4269-4269  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 12:07:11.971  4269-4269  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 12:07:11.975  4269-4269  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 12:07:11.978  4269-4269  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 12:07:11.978  4269-4269  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 12:07:12.018  4269-4276  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 12:07:12.269  4269-4269  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 12:07:12.892  4269-4269  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 12:07:13.204  4269-4269  SimulatorInitializer    com.spacelabs.app                    I  Initializing medical data simulator...
2025-07-08 12:07:13.324  4269-4269  MedicalDataSimulator    com.spacelabs.app                    I  Simulation configuration updated
2025-07-08 12:07:13.324  4269-4269  SimulationManager       com.spacelabs.app                    I  Simulation configuration updated
2025-07-08 12:07:13.324  4269-4269  SimulatorInitializer    com.spacelabs.app                    D  Loaded simulation preferences: enabled=false, autoStart=true, scenario=NORMAL_ADULT
2025-07-08 12:07:13.325  4269-4269  SimulationManager       com.spacelabs.app                    I  Initializing SimulationManager...
2025-07-08 12:07:13.325  4269-4269  SimulationManager       com.spacelabs.app                    I  SimulationManager initialized successfully
2025-07-08 12:07:13.405  4269-4269  AndroidRuntime          com.spacelabs.app                    D  Shutting down VM
2025-07-08 12:07:13.439  4269-4269  AndroidRuntime          com.spacelabs.app                    E  FATAL EXCEPTION: main
                                                                                                    Process: com.spacelabs.app, PID: 4269
                                                                                                    java.lang.ExceptionInInitializerError
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager.initializeDataManager(SimulationManager.kt:61)
                                                                                                    	at com.spacelabs.app.simulator.SimulatorInitializer.initialize(SimulatorInitializer.kt:53)
                                                                                                    	at com.spacelabs.app.ApplicationClass.onCreate(ApplicationClass.kt:29)
                                                                                                    	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1381)
                                                                                                    	at android.app.ActivityThread.handleBindApplication(ActivityThread.java:7830)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleBindApplication(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2546)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:110)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
                                                                                                    Caused by: java.lang.NullPointerException: Context may not be null
                                                                                                    	at io.objectbox.BoxStoreBuilder.androidContext(BoxStoreBuilder.java:201)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.initObjectBox(ObjectBoxManager.kt:20)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.getObjectBoxStore(ObjectBoxManager.kt:32)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.getMeasurementBox(ObjectBoxManager.kt:43)
                                                                                                    	at com.spacelabs.app.dataManager.MeasurementDataManager.<clinit>(MeasurementDataManager.kt:21)
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager.initializeDataManager(SimulationManager.kt:61) 
                                                                                                    	at com.spacelabs.app.simulator.SimulatorInitializer.initialize(SimulatorInitializer.kt:53) 
                                                                                                    	at com.spacelabs.app.ApplicationClass.onCreate(ApplicationClass.kt:29) 
                                                                                                    	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1381) 
                                                                                                    	at android.app.ActivityThread.handleBindApplication(ActivityThread.java:7830) 
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleBindApplication(Unknown Source:0) 
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2546) 
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:110) 
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248) 
                                                                                                    	at android.os.Looper.loop(Looper.java:338) 
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067) 
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method) 
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593) 
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932) 
---------------------------- PROCESS ENDED (4269) for package com.spacelabs.app ----------------------------
2025-07-08 12:07:13.572  4269-4269  Process                 com.spacelabs.app                    I  Sending signal. PID: 4269 SIG: 9
