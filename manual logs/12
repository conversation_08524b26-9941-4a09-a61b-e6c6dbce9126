--------- beginning of main
--------- beginning of system
--------- beginning of main
--------- beginning of system
2025-07-08 16:16:37.285  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  Late-enabling -Xcheck:jni
2025-07-08 16:16:37.350  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  Using CollectorTypeCMC GC.
2025-07-08 16:16:37.350  2396-2396  m.spacelabs.app         com.spacelabs.app                    W  Unexpected CPU variant for x86: x86_64.
                                                                                                    Known variants: atom, sandybridge, silvermont, goldmont, goldmont-plus, goldmont-without-sha-xsaves, tremont, kabylake, alderlake, default
2025-07-08 16:16:37.374  2396-2396  nativeloader            com.spacelabs.app                    D  Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
2025-07-08 16:16:37.560  2396-2396  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-08 16:16:40.653  2396-2396  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/lib/x86_64:/data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 16:16:40.980  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d2403189678) locale list changing from [] to [en-US]
2025-07-08 16:16:41.017  2396-2396  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 16:16:41.017  2396-2396  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 16:16:41.017  2396-2396  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 16:16:41.018  2396-2396  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 16:16:41.030  2396-2396  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 16:16:41.030  2396-2396  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 16:16:41.030  2396-2396  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 16:16:41.075  2396-2396  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 16:16:41.100  2396-2396  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 16:16:41.100  2396-2396  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 16:16:41.592  2396-2406  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 16:16:41.926  2396-2396  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 16:16:43.016  2396-2396  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 16:16:43.871  2396-2396  StaticTestDataManager   com.spacelabs.app                    D  Static test data loaded successfully
2025-07-08 16:16:43.942  2396-2396  Choreographer           com.spacelabs.app                    I  Skipped 37 frames!  The application may be doing too much work on its main thread.
2025-07-08 16:16:43.957  2396-2631  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-08 16:16:43.966  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d240318a938) locale list changing from [] to [en-US]
2025-07-08 16:16:44.325  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d24031906f8) locale list changing from [] to [en-US]
2025-07-08 16:16:44.425  2396-2396  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-08 16:16:44.571  2396-2631  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-08 16:16:44.572  2396-2631  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-08 16:16:44.601  2396-2631  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-08 16:16:44.669  2396-2396  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-08 16:16:44.752  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 16:16:44.752  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 16:16:45.656  2396-2396  nativeloader            com.spacelabs.app                    D  Load /data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk!classes20.dex): ok
2025-07-08 16:16:46.704  2396-2740  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-08 16:16:46.806  2396-2728  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-08 16:16:46.809  2396-2750  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-08 16:16:46.810  2396-2737  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-08 16:16:46.812  2396-2754  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-08 16:16:46.814  2396-2752  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> SettingsChanged
2025-07-08 16:16:46.918  2396-2396  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-08 16:16:46.925  2396-2767  NetworkCallback         com.spacelabs.app                    D  Network is available: 100
2025-07-08 16:16:46.936  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 16:16:46.936  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated=''
2025-07-08 16:16:46.936  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 16:16:46.936  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 16:16:46.938  2396-2767  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 1Mbps, Tx Link speed: 1Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: -1Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-08 16:16:46.938  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 16:16:46.938  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated=''
2025-07-08 16:16:46.938  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 16:16:46.938  2396-2767  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 16:16:46.939  2396-2767  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-08 16:16:46.939  2396-2767  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-08 16:16:46.954  2396-2396  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 16:16:46.958  2396-2748  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-08 16:16:46.971  2396-2748  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-08 16:16:46.986  2396-2744  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-08 16:16:47.008  2396-2749  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-08 16:16:47.170  2396-2744  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-08 16:16:47.206  2396-2396  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 16:16:47.214  2396-2749  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-08 16:16:47.285  2396-2749  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-08 16:16:47.322  2396-2749  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-08 16:16:47.335  2396-2396  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 16:16:47.418  2396-2744  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-08 16:16:47.471  2396-2743  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-08 16:16:47.481  2396-2396  Choreographer           com.spacelabs.app                    I  Skipped 31 frames!  The application may be doing too much work on its main thread.
2025-07-08 16:16:47.555  2396-2408  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 3590KB AllocSpace bytes, 24(820KB) LOS objects, 49% free, 4002KB/8004KB, paused 925us,10.063ms total 66.229ms
2025-07-08 16:16:47.563  2396-2744  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-08 16:16:47.647  2396-2749  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-08 16:16:47.855  2396-2748  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-08 16:16:47.893  2396-2407  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-08 16:16:48.002  2396-2744  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-08 16:16:48.072  2396-2413  HWUI                    com.spacelabs.app                    I  Davey! duration=1112ms; Flags=1, FrameTimelineVsyncId=11551, IntendedVsync=87283408803, Vsync=87800075449, InputEventId=0, HandleInputStart=87813861215, AnimationStart=87813866038, PerformTraversalsStart=87813867227, DrawStart=88141687913, FrameDeadline=87300075469, FrameStartTime=87807819589, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=88142260066, SyncStart=88143179581, IssueDrawCommandsStart=88143289970, SwapBuffers=88143753447, FrameCompleted=88396443585, DequeueBufferDuration=33956, QueueBufferDuration=2591876, GpuCompleted=88349404701, SwapBuffersCompleted=88396443585, DisplayPresentTime=1711701572001956471, CommandSubmissionCompleted=88143753447,
2025-07-08 16:16:48.089  2396-2396  Choreographer           com.spacelabs.app                    I  Skipped 34 frames!  The application may be doing too much work on its main thread.
2025-07-08 16:16:48.130  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d24031983f8) locale list changing from [] to [en-US]
2025-07-08 16:16:48.231  2396-2749  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-08 16:16:48.318  2396-2748  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-08 16:16:48.488  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Setting up activity UI
2025-07-08 16:16:48.511  2396-2396  SNS_QR_DEBUG            com.spacelabs.app                    D  postInitUiActions called - enableSnsApi: true
2025-07-08 16:16:48.511  2396-2396  SNS_QR_DEBUG            com.spacelabs.app                    D  Calling generateAndDisplayQRCode() for SNS flow
2025-07-08 16:16:48.559  2396-2396  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: Android ID: 6fdc3252d58580a1
                                                                                                    OS Version: 16
                                                                                                    Device: Google sdk_gphone64_x86_64
                                                                                                    UUID: 803bf36c-36ad-4be6-b569-416879d541ad
                                                                                                    Passphrase: &M3XlLe#uiJm5o6*
2025-07-08 16:16:48.559  2396-2396  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: EbwQQJkJ9QJGHuQBhpN3AqpBbtg7XfIPNzBprnqaGvHFxBOOrIaHtnA3puOt+tvw5pBWPQhsipfl
                                                                                                    AnameP0gslxu5+1XDAiMEgtvPXvMf89XqBHc/kkVAEwYDfe794sSqKVIomc9VXag2D8/EZwK0+UQ
                                                                                                    piRXpTK8drlSOqFngC2v4TxG/p4VlavEiwmm/tzaR8RXaoMO3FYZ3vzuXPuZqbstf93SmOvYoR5T
                                                                                                    MeGoqXDJ1x0liX5T4wVTJXVzq1Zp8AOF07u6kDWyVKzpe8vUPq8I2NN4XfdygObV9OSy/qLW0vqk
                                                                                                    ivSVVWPeAAlCeAh6RDpAQk+Q9QM8mm/31mzLkw==
2025-07-08 16:16:48.670  2396-2744  invalidKey              com.spacelabs.app                    D  DevicePass key is Invalid
2025-07-08 16:16:48.675  2396-2744  invalidKey              com.spacelabs.app                    D  DeviceUuid key is Invalid
2025-07-08 16:16:49.671  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: QR code has been displayed
2025-07-08 16:16:49.671  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Activity UI setup completed
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = ''
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 16:16:49.679  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 16:16:49.680  2396-2396  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 16:16:49.681  2396-2748  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 16:16:49.681  2396-2748  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 16:16:49.682  2396-2396  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 16:16:49.685  2396-2396  Choreographer           com.spacelabs.app                    I  Skipped 95 frames!  The application may be doing too much work on its main thread.
2025-07-08 16:16:50.029  2396-2396  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 16:16:50.081  2396-2903  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
2025-07-08 16:16:50.820  2396-2416  HWUI                    com.spacelabs.app                    I  Davey! duration=974ms; Flags=0, FrameTimelineVsyncId=11813, IntendedVsync=90016742027, Vsync=90066742025, InputEventId=0, HandleInputStart=90070428159, AnimationStart=90070429968, PerformTraversalsStart=90070654167, DrawStart=90091260677, FrameDeadline=90033408693, FrameStartTime=90070404635, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=90352611809, SyncStart=90352739180, IssueDrawCommandsStart=90352870754, SwapBuffers=90896086574, FrameCompleted=90991253481, DequeueBufferDuration=27469, QueueBufferDuration=1086977, GpuCompleted=90990991564, SwapBuffersCompleted=90991253481, DisplayPresentTime=1693942248762275591, CommandSubmissionCompleted=90896086574,
2025-07-08 16:16:50.896  2396-2396  Choreographer           com.spacelabs.app                    I  Skipped 50 frames!  The application may be doing too much work on its main thread.
2025-07-08 16:16:50.958  2396-2416  HWUI                    com.spacelabs.app                    I  Davey! duration=713ms; Flags=1, FrameTimelineVsyncId=11882, IntendedVsync=90366742013, Vsync=90366742013, InputEventId=0, HandleInputStart=90379963866, AnimationStart=90379966485, PerformTraversalsStart=90380202534, DrawStart=90996674862, FrameDeadline=90383408679, FrameStartTime=90379953632, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=91018323923, SyncStart=91151754663, IssueDrawCommandsStart=91153682228, SwapBuffers=91154714517, FrameCompleted=91213209249, DequeueBufferDuration=29163, QueueBufferDuration=1965033, GpuCompleted=91213000012, SwapBuffersCompleted=91213209249, DisplayPresentTime=1889898720, CommandSubmissionCompleted=91154714517,
2025-07-08 16:16:50.970  2396-2396  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 16:16:51.099  2396-2416  HWUI                    com.spacelabs.app                    I  Davey! duration=984ms; Flags=1, FrameTimelineVsyncId=11882, IntendedVsync=90366742013, Vsync=90366742013, InputEventId=0, HandleInputStart=90379963866, AnimationStart=90379966485, PerformTraversalsStart=90380202534, DrawStart=91221242835, FrameDeadline=91166741981, FrameStartTime=90379953632, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=91221745993, SyncStart=91222035736, IssueDrawCommandsStart=91222184977, SwapBuffers=91258691867, FrameCompleted=91351436401, DequeueBufferDuration=7627, QueueBufferDuration=759767, GpuCompleted=91351436401, SwapBuffersCompleted=91279689298, DisplayPresentTime=-281825600784340735, CommandSubmissionCompleted=91258691867,
2025-07-08 16:16:51.306  2396-2396  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 16:16:51.309  2396-2420  HWUI                    com.spacelabs.app                    I  Davey! duration=988ms; Flags=0, FrameTimelineVsyncId=11889, IntendedVsync=90383408679, Vsync=91216741979, InputEventId=0, HandleInputStart=91223542916, AnimationStart=91223544910, PerformTraversalsStart=91298849089, DrawStart=91299100144, FrameDeadline=91233408645, FrameStartTime=91222743836, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=91299248034, SyncStart=91487718013, IssueDrawCommandsStart=91487956951, SwapBuffers=91488292329, FrameCompleted=91560681971, DequeueBufferDuration=15542, QueueBufferDuration=507742, GpuCompleted=91560681971, SwapBuffersCompleted=91506525581, DisplayPresentTime=1889934392, CommandSubmissionCompleted=91488292329,
2025-07-08 16:16:51.365  2396-2416  HWUI                    com.spacelabs.app                    I  Davey! duration=1228ms; Flags=0, FrameTimelineVsyncId=11889, IntendedVsync=90383408679, Vsync=91216741979, InputEventId=0, HandleInputStart=91223542916, AnimationStart=91223544910, PerformTraversalsStart=91298849089, DrawStart=91490681049, FrameDeadline=91450075303, FrameStartTime=91222743836, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=91506350848, SyncStart=91539276786, IssueDrawCommandsStart=91539448200, SwapBuffers=91561628663, FrameCompleted=91644919476, DequeueBufferDuration=8934, QueueBufferDuration=821074, GpuCompleted=91644919476, SwapBuffersCompleted=91599942081, DisplayPresentTime=7341636293709333401, CommandSubmissionCompleted=91561628663,
2025-07-08 16:16:51.635  2396-2901  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-08 16:16:51.722  2396-2396  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-08 16:16:51.743  2396-2420  HWUI                    com.spacelabs.app                    I  Davey! duration=720ms; Flags=0, FrameTimelineVsyncId=12351, IntendedVsync=91300075309, Vsync=91566741965, InputEventId=0, HandleInputStart=91569065164, AnimationStart=91569069571, PerformTraversalsStart=91574030937, DrawStart=91574235908, FrameDeadline=91583408631, FrameStartTime=91568544069, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=91574353575, SyncStart=91604153139, IssueDrawCommandsStart=91604300137, SwapBuffers=91951069002, FrameCompleted=92050244529, DequeueBufferDuration=12414, QueueBufferDuration=375624, GpuCompleted=92050244529, SwapBuffersCompleted=92039749675, DisplayPresentTime=1890734896, CommandSubmissionCompleted=91951069002,
2025-07-08 16:16:51.768  2396-2396  Choreographer           com.spacelabs.app                    I  Skipped 30 frames!  The application may be doing too much work on its main thread.
2025-07-08 16:16:51.792  2396-2396  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 16:16:51.793  2396-2396  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:3e93658e: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 16:16:52.671  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Delay completed, checking navigation
2025-07-08 16:16:52.671  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Activity is resumed, checking navigation
2025-07-08 16:16:52.671  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 16:16:52.671  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 16:16:52.671  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 16:16:52.671  2396-2396  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 16:16:52.672  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 16:16:52.672  2396-2744  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 16:17:15.468  2396-2396  SNS                     com.spacelabs.app                    D  onNextButtonClickedAction: true hasAuthenticated
2025-07-08 16:17:15.469  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 16:17:15.474  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = ''
2025-07-08 16:17:15.474  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 16:17:15.474  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Staying on AccountConfigActivitySns (HasAuthenticated is empty)
2025-07-08 16:17:15.474  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = AccountConfigActivitySns
2025-07-08 16:17:15.475  2396-2396  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 16:17:15.475  2396-2744  SAVE_ACCOUNT_ID         com.spacelabs.app                    D  saveSettingsAndNavigate: true ,And HasAuthenticated
2025-07-08 16:17:15.507  2396-2744  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = 'true'
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = 'true'
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 16:17:15.538  2396-2396  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = 'true'
2025-07-08 16:17:15.538  2396-2396  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists: true
2025-07-08 16:17:15.539  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is 'true', posting NavigateToNext event
2025-07-08 16:17:15.539  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 16:17:15.539  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = 'true'
2025-07-08 16:17:15.539  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 16:17:15.539  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Going to QrScannerActivitySns (AccountId is empty)
2025-07-08 16:17:15.539  2396-2744  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = QrScannerActivitySns
2025-07-08 16:17:15.541  2396-2396  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-08 16:17:15.983  2396-2408  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 2288KB AllocSpace bytes, 8(324KB) LOS objects, 49% free, 5488KB/10MB, paused 988us,10.120ms total 172.135ms
2025-07-08 16:17:16.401  2396-2396  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 16:17:16.549  2396-2396  VRI[Accoun...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 16:17:16.638  2396-2396  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 16:17:16.638  2396-2396  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:36190388: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 16:17:17.563  2396-2396  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 16:17:17.585  2396-2396  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d2403198d58) locale list changing from [] to [en-US]
2025-07-08 16:17:17.689  2396-4045  CameraManagerGlobal     com.spacelabs.app                    I  Connecting to camera service
2025-07-08 16:17:17.963  2396-4045  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 16:17:18.247  2396-2396  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 16:17:18.247  2396-2396  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:d8cb4b3f: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 16:17:18.261  2396-4045  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 16:17:18.275  2396-4045  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 16:17:18.276  2396-4045  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@4fced27[id=1]]
2025-07-08 16:17:18.281  2396-4045  CameraX                 com.spacelabs.app                    W  Retry init. Start time 117992 current time 118602
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 16:17:18.306  2396-2396  VRI[QrScan...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 16:17:18.788  2396-4045  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 16:17:18.788  2396-4045  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 16:17:18.790  2396-4045  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 16:17:18.790  2396-4045  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@8f34db1[id=1]]
2025-07-08 16:17:18.790  2396-4045  CameraX                 com.spacelabs.app                    W  Retry init. Start time 117992 current time 119116
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 16:17:19.297  2396-4045  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 16:17:19.297  2396-4045  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 16:17:19.298  2396-4045  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 16:17:19.298  2396-4045  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@31cfe6e[id=1]]
2025-07-08 16:17:19.299  2396-4045  CameraX                 com.spacelabs.app                    W  Retry init. Start time 117992 current time 119624
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 16:17:19.806  2396-4045  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 16:17:19.807  2396-4045  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 16:17:19.808  2396-4045  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 16:17:19.808  2396-4045  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@4280b07[id=1]]
2025-07-08 16:17:19.808  2396-4045  CameraX                 com.spacelabs.app                    W  Retry init. Start time 117992 current time 120134
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 16:17:20.311  2396-4045  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 16:17:20.312  2396-4045  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 16:17:20.312  2396-4045  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 16:17:20.313  2396-4045  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@bdea5cc[id=1]]
2025-07-08 16:17:20.313  2396-4045  CameraX                 com.spacelabs.app                    E  The device might underreport the amount of the cameras. Finish the initialize task since we are already reaching the maximum number of retries.
2025-07-08 16:17:20.330  2396-2396  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Use case binding failed
                                                                                                    java.lang.IllegalArgumentException: Provided camera selector unable to resolve a camera for the given use case
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:609)
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:407)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.startCamera$lambda$2(CameraActivity.kt:57)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.$r8$lambda$QsmAJe7FdI1KZZ6w_B0BAiHaOXA(Unknown Source:0)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity$$ExternalSyntheticLambda1.run(Unknown Source:4)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:995)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:103)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
--------- beginning of crash
2025-07-08 16:17:33.702  2396-2771  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7d2683402c38)
2025-07-08 16:17:33.703  2396-2771  libc                    com.spacelabs.app                    A  Fatal signal 6 (SIGABRT), code -1 (SI_QUEUE) in tid 2771 (hwuiTask1), pid 2396 (m.spacelabs.app)
