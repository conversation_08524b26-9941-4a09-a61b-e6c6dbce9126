---------------------------- PROCESS STARTED (4863) for package com.spacelabs.app ----------------------------
2025-07-08 17:25:37.188  4863-4863  m.spacelabs.app         com.spacelabs.app                    W  Redefining intrinsic method java.lang.Thread java.lang.Thread.currentThread(). This may cause the unexpected use of the original definition of java.lang.Thread java.lang.Thread.currentThread()in methods that have already been compiled.
2025-07-08 17:25:37.188  4863-4863  m.spacelabs.app         com.spacelabs.app                    W  Redefining intrinsic method boolean java.lang.Thread.interrupted(). This may cause the unexpected use of the original definition of boolean java.lang.Thread.interrupted()in methods that have already been compiled.
2025-07-08 17:25:37.204  4863-4863  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-08 17:25:38.456  4863-4863  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/lib/x86_64:/data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 17:25:38.504  4863-4863  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d2403189678) locale list changing from [] to [en-US]
2025-07-08 17:25:38.505  4863-4863  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d240318a618) locale list changing from [] to [en-US]
2025-07-08 17:25:38.509  4863-4863  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 17:25:38.509  4863-4863  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 17:25:38.509  4863-4863  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 17:25:38.509  4863-4863  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 17:25:38.509  4863-4863  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 17:25:38.509  4863-4863  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 17:25:38.510  4863-4863  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 17:25:38.512  4863-4863  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 17:25:38.514  4863-4863  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 17:25:38.514  4863-4863  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 17:25:38.569  4863-4867  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 17:25:38.977  4863-4863  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 17:25:39.322  4863-4863  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 17:25:39.411  4863-4863  StaticTestDataManager   com.spacelabs.app                    D  Static test data loaded successfully
2025-07-08 17:25:39.420  4863-4886  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-08 17:25:39.427  4863-4863  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d2403189358) locale list changing from [] to [en-US]
2025-07-08 17:25:39.572  4863-4886  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-08 17:25:39.575  4863-4886  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-08 17:25:39.601  4863-4863  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7d2403191cd8) locale list changing from [] to [en-US]
2025-07-08 17:25:39.609  4863-4863  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-08 17:25:39.616  4863-4886  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-08 17:25:39.673  4863-4863  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-08 17:25:39.706  4863-4863  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 17:25:39.707  4863-4863  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 17:25:39.892  4863-4863  nativeloader            com.spacelabs.app                    D  Load /data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~BsG3cZl5mISSZNYzZUwn9w==/com.spacelabs.app-hwPPGHljZVRkj98bqoPXMg==/base.apk!classes20.dex): ok
2025-07-08 17:25:40.225  4863-4895  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-08 17:25:40.259  4863-4892  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-08 17:25:40.272  4863-4901  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-08 17:25:40.281  4863-4919  NetworkCallback         com.spacelabs.app                    D  Network is available: 100
2025-07-08 17:25:40.281  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 17:25:40.286  4863-4897  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-08 17:25:40.290  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 17:25:40.290  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-08 17:25:40.291  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 17:25:40.291  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 17:25:40.294  4863-4863  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-08 17:25:40.294  4863-4919  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 1Mbps, Tx Link speed: 1Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: -1Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-08 17:25:40.294  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 17:25:40.298  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 17:25:40.298  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-08 17:25:40.298  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 17:25:40.298  4863-4919  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 17:25:40.299  4863-4919  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-08 17:25:40.299  4863-4919  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-08 17:25:40.306  4863-4863  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 17:25:40.353  4863-4892  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 17:25:40.357  4863-4892  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-08 17:25:40.361  4863-4893  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 17:25:40.387  4863-4863  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 17:25:40.397  4863-4901  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-08 17:25:40.435  4863-4902  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-08 17:25:40.474  4863-4863  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 17:25:40.512  4863-4902  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-08 17:25:40.562  4863-4902  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-08 17:25:40.575  4863-4897  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-08 17:25:40.587  4863-4902  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-08 17:25:40.600  4863-4897  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-08 17:25:40.638  4863-4897  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-08 17:25:40.653  4863-4902  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-08 17:25:40.670  4863-4902  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-08 17:25:40.698  4863-4901  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-08 17:25:40.766  4863-4902  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-08 17:25:40.781  4863-4868  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-08 17:25:40.788  4863-4892  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-08 17:25:41.376  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Setting up activity UI
2025-07-08 17:25:41.381  4863-4863  SNS_QR_DEBUG            com.spacelabs.app                    D  postInitUiActions called - enableSnsApi: true
2025-07-08 17:25:41.381  4863-4863  SNS_QR_DEBUG            com.spacelabs.app                    D  Calling generateAndDisplayQRCode() for SNS flow
2025-07-08 17:25:41.404  4863-4863  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: Android ID: 6fdc3252d58580a1
                                                                                                    OS Version: 16
                                                                                                    Device: Google sdk_gphone64_x86_64
                                                                                                    UUID: 803bf36c-36ad-4be6-b569-416879d541ad
                                                                                                    Passphrase: &M3XlLe#uiJm5o6*
2025-07-08 17:25:41.404  4863-4863  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: Jqhx971Yv4ReE5FNe2oQotpScKjSm1bNXPRmkkJO+D3y5NNSvBxlzcrytulFOMXMmUgwPxnn2STs
                                                                                                    2JkZHJqgwQjMtqgICWmRapPW/PIRHVlyAPdYWkHOBacXiEVFXyqa09OeAYRPzU7CVlz7p4VV707p
                                                                                                    MmFF9nwJdXZ/ukzZGNaukXbpt2VlxxKuypw5pZSqWpJHgaNvePcHed4gvQ9cFwtT2kXwHGE2lZNJ
                                                                                                    JpbiisipPc3EX5dDF0QUN9xNtyVC99pkMuqe9UeVOr8I5MdoXKOkuGZjG8NV3Mc8I10JOi3s94B7
                                                                                                    8byNZFNXTVkTicjzZQoGRNdzxpDpsSFXe7cvvA==
2025-07-08 17:25:41.510  4863-4901  invalidKey              com.spacelabs.app                    D  DevicePass key is Invalid
2025-07-08 17:25:41.521  4863-4892  invalidKey              com.spacelabs.app                    D  DeviceUuid key is Invalid
2025-07-08 17:25:42.011  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: QR code has been displayed
2025-07-08 17:25:42.011  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Activity UI setup completed
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = ''
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 17:25:42.016  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 17:25:42.017  4863-4863  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 17:25:42.017  4863-4897  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 17:25:42.017  4863-4897  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 17:25:42.019  4863-4863  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 17:25:42.086  4863-4933  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-08 17:25:42.455  4863-4863  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 17:25:42.527  4863-4863  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 17:25:42.552  4863-4863  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-08 17:25:42.820  4863-4863  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 17:25:42.821  4863-4863  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:de12e8db: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 17:25:44.016  4863-4863  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 17:25:44.736  4863-4938  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
2025-07-08 17:25:45.062  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Delay completed, checking navigation
2025-07-08 17:25:45.062  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Activity is resumed, checking navigation
2025-07-08 17:25:45.062  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 17:25:45.062  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 17:25:45.062  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 17:25:45.064  4863-4863  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 17:25:45.064  4863-4897  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 17:25:45.064  4863-4897  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 17:25:45.553  4863-4863  SNS                     com.spacelabs.app                    D  onNextButtonClickedAction: true hasAuthenticated
2025-07-08 17:25:45.562  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 17:25:45.563  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = ''
2025-07-08 17:25:45.563  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 17:25:45.563  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Staying on AccountConfigActivitySns (HasAuthenticated is empty)
2025-07-08 17:25:45.563  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = AccountConfigActivitySns
2025-07-08 17:25:45.564  4863-4902  SAVE_ACCOUNT_ID         com.spacelabs.app                    D  saveSettingsAndNavigate: true ,And HasAuthenticated
2025-07-08 17:25:45.575  4863-4902  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 17:25:45.605  4863-4863  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = 'true'
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = 'true'
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 17:25:45.641  4863-4863  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = 'true'
2025-07-08 17:25:45.641  4863-4863  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists: true
2025-07-08 17:25:45.642  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is 'true', posting NavigateToNext event
2025-07-08 17:25:45.644  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 17:25:45.645  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = 'true'
2025-07-08 17:25:45.645  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 17:25:45.645  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Going to QrScannerActivitySns (AccountId is empty)
2025-07-08 17:25:45.645  4863-4902  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = QrScannerActivitySns
2025-07-08 17:25:45.700  4863-4863  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-08 17:25:46.379  4863-4863  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 17:25:46.555  4863-4863  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 17:25:46.555  4863-4863  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:78eae838: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 17:25:47.872  4863-4863  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 17:25:48.035  4863-4869  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 5414KB AllocSpace bytes, 39(1428KB) LOS objects, 49% free, 5677KB/11MB, paused 774us,12.648ms total 96.672ms
2025-07-08 17:25:48.069  4863-4945  CameraManagerGlobal     com.spacelabs.app                    I  Connecting to camera service
2025-07-08 17:25:48.175  4863-4945  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 17:25:48.213  4863-4945  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 17:25:48.222  4863-4945  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 17:25:48.226  4863-4945  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@4a94b09[id=1]]
2025-07-08 17:25:48.231  4863-4945  CameraX                 com.spacelabs.app                    W  Retry init. Start time 4228374 current time 4228555
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 17:25:48.306  4863-4863  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 17:25:48.306  4863-4863  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:54f26f9: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 17:25:48.630  4863-4863  VRI[QrScan...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 17:25:48.745  4863-4945  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 17:25:48.748  4863-4945  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 17:25:48.750  4863-4945  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 17:25:48.751  4863-4945  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@5c134c3[id=1]]
2025-07-08 17:25:48.752  4863-4945  CameraX                 com.spacelabs.app                    W  Retry init. Start time 4228374 current time 4229078
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 17:25:49.257  4863-4945  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 17:25:49.257  4863-4945  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 17:25:49.258  4863-4945  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 17:25:49.258  4863-4945  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@fafc858[id=1]]
2025-07-08 17:25:49.259  4863-4945  CameraX                 com.spacelabs.app                    W  Retry init. Start time 4228374 current time 4229584
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 17:25:49.763  4863-4945  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 17:25:49.763  4863-4945  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 17:25:49.764  4863-4945  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 17:25:49.764  4863-4945  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@5f501e9[id=1]]
2025-07-08 17:25:49.764  4863-4945  CameraX                 com.spacelabs.app                    W  Retry init. Start time 4228374 current time 4230090
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 17:25:50.269  4863-4945  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 17:25:50.270  4863-4945  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 17:25:50.272  4863-4945  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 17:25:50.272  4863-4945  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@c726d46[id=1]]
2025-07-08 17:25:50.272  4863-4945  CameraX                 com.spacelabs.app                    W  Retry init. Start time 4228374 current time 4230598
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 17:25:50.776  4863-4945  CameraRepository        com.spacelabs.app                    D  Added camera: 1
2025-07-08 17:25:50.777  4863-4945  Camera2CameraInfo       com.spacelabs.app                    I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-07-08 17:25:50.778  4863-4945  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 17:25:50.778  4863-4945  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: [Camera@5ed53ff[id=1]]
2025-07-08 17:25:50.779  4863-4945  CameraX                 com.spacelabs.app                    E  The device might underreport the amount of the cameras. Finish the initialize task since we are already reaching the maximum number of retries.
2025-07-08 17:25:50.791  4863-4863  Qr_Scanner_Activity_Sns com.spacelabs.app                    D  Running on emulator - using back camera for external webcam QR scanning
2025-07-08 17:25:50.792  4863-4863  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Use case binding failed
                                                                                                    java.lang.IllegalArgumentException: Provided camera selector unable to resolve a camera for the given use case
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:609)
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:407)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.startCamera$lambda$2(CameraActivity.kt:89)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.$r8$lambda$QsmAJe7FdI1KZZ6w_B0BAiHaOXA(Unknown Source:0)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity$$ExternalSyntheticLambda1.run(Unknown Source:4)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:995)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:103)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
2025-07-08 17:26:16.690  4863-4922  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7d2683402c38)
2025-07-08 17:26:16.690  4863-4921  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7d2683402c38)
2025-07-08 17:26:16.691  4863-4922  libc                    com.spacelabs.app                    A  Fatal signal 6 (SIGABRT), code -1 (SI_QUEUE) in tid 4922 (hwuiTask1), pid 4863 (m.spacelabs.app)
---------------------------- PROCESS ENDED (4863) for package com.spacelabs.app ----------------------------
