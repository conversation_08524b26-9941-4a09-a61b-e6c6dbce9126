---------------------------- PROCESS STARTED (4761) for package com.spacelabs.app ----------------------------
2025-07-08 11:39:27.637  4761-4761  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-08 11:39:28.235  4761-4761  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/lib/x86_64:/data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 11:39:28.275  4761-4761  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7bd756975f98) locale list changing from [] to [en-US]
2025-07-08 11:39:28.276  4761-4761  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7bd756971df8) locale list changing from [] to [en-US]
2025-07-08 11:39:28.280  4761-4761  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 11:39:28.280  4761-4761  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 11:39:28.280  4761-4761  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 11:39:28.280  4761-4761  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 11:39:28.280  4761-4761  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 11:39:28.280  4761-4761  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 11:39:28.280  4761-4761  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 11:39:28.282  4761-4761  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 11:39:28.283  4761-4761  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 11:39:28.283  4761-4761  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 11:39:28.304  4761-4765  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 11:39:28.597  4761-4761  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 11:39:29.035  4761-4761  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 11:39:29.153  4761-4761  SimulatorInitializer    com.spacelabs.app                    I  Initializing medical data simulator...
2025-07-08 11:39:29.303  4761-4761  AndroidRuntime          com.spacelabs.app                    D  Shutting down VM
2025-07-08 11:39:29.307  4761-4761  AndroidRuntime          com.spacelabs.app                    E  FATAL EXCEPTION: main
                                                                                                    Process: com.spacelabs.app, PID: 4761
                                                                                                    java.lang.ExceptionInInitializerError
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager.<init>(SimulationManager.kt:30)
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager$Companion.getInstance(SimulationManager.kt:24)
                                                                                                    	at com.spacelabs.app.simulator.SimulatorInitializer.initialize(SimulatorInitializer.kt:44)
                                                                                                    	at com.spacelabs.app.ApplicationClass.onCreate(ApplicationClass.kt:29)
                                                                                                    	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1381)
                                                                                                    	at android.app.ActivityThread.handleBindApplication(ActivityThread.java:7830)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleBindApplication(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2546)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:110)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
                                                                                                    Caused by: java.lang.NullPointerException: Context may not be null
                                                                                                    	at io.objectbox.BoxStoreBuilder.androidContext(BoxStoreBuilder.java:201)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.initObjectBox(ObjectBoxManager.kt:20)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.getObjectBoxStore(ObjectBoxManager.kt:32)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.getMeasurementBox(ObjectBoxManager.kt:43)
                                                                                                    	at com.spacelabs.app.dataManager.MeasurementDataManager.<clinit>(MeasurementDataManager.kt:21)
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager.<init>(SimulationManager.kt:30) 
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager$Companion.getInstance(SimulationManager.kt:24) 
                                                                                                    	at com.spacelabs.app.simulator.SimulatorInitializer.initialize(SimulatorInitializer.kt:44) 
                                                                                                    	at com.spacelabs.app.ApplicationClass.onCreate(ApplicationClass.kt:29) 
                                                                                                    	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1381) 
                                                                                                    	at android.app.ActivityThread.handleBindApplication(ActivityThread.java:7830) 
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleBindApplication(Unknown Source:0) 
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2546) 
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:110) 
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248) 
                                                                                                    	at android.os.Looper.loop(Looper.java:338) 
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067) 
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method) 
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593) 
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932) 
2025-07-08 11:39:38.360  4761-4761  Process                 com.spacelabs.app                    I  Sending signal. PID: 4761 SIG: 9
---------------------------- PROCESS ENDED (4761) for package com.spacelabs.app ----------------------------
