---------------------------- PROCESS STARTED (5231) for package com.spacelabs.app ----------------------------
2025-07-08 17:31:31.351  5231-5231  Qr_Scanner_Activity_Sns com.spacelabs.app                    D  Emulator detection result: true
2025-07-08 17:31:31.351  5231-5231  Qr_Scanner_Activity_Sns com.spacelabs.app                    D  Build info - FINGERPRINT: google/sdk_gphone64_x86_64/emu64xa:16/BP22.250325.006/13344233:user/release-keys, MODEL: sdk_gphone64_x86_64, MANUFACTURER: Google
2025-07-08 17:31:34.263  5231-5231  Qr_Scanner_Activity_Sns com.spacelabs.app                    D  Running on emulator - using back camera for external webcam QR scanning
2025-07-08 17:31:34.263  5231-5231  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Use case binding failed
                                                                                                    java.lang.IllegalArgumentException: Provided camera selector unable to resolve a camera for the given use case
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:609)
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:407)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.startCamera$lambda$2(CameraActivity.kt:92)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.$r8$lambda$QsmAJe7FdI1KZZ6w_B0BAiHaOXA(Unknown Source:0)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity$$ExternalSyntheticLambda1.run(Unknown Source:4)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:995)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:103)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
2025-07-08 17:31:34.263  5231-5231  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Camera-specific error: Provided camera selector unable to resolve a camera for the given use case
---------------------------- PROCESS ENDED (5231) for package com.spacelabs.app ----------------------------
