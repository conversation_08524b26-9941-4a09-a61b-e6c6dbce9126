--------- beginning of main
--------- beginning of system
---------------------------- PROCESS STARTED (4014) for package com.spacelabs.app ----------------------------
2025-07-08 15:57:25.616  4014-4014  nativeloader            com.spacelabs.app                    D  Load libframework-connectivity-tiramisu-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity-t.jar: ok
2025-07-08 15:57:25.800  4014-4014  CompatChangeReporter    com.spacelabs.app                    D  Compat change id reported: 242716250; UID 10216; state: ENABLED
2025-07-08 15:57:27.891  4014-4014  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~-LLS-cs8ewG4eKMssWNk5g==/com.spacelabs.app-5Pzlt_UM6E7Y8VOKDapb_w==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~-LLS-cs8ewG4eKMssWNk5g==/com.spacelabs.app-5Pzlt_UM6E7Y8VOKDapb_w==/lib/x86_64:/data/app/~~-LLS-cs8ewG4eKMssWNk5g==/com.spacelabs.app-5Pzlt_UM6E7Y8VOKDapb_w==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 15:57:28.371  4014-4014  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7178ed00eff8) locale list changing from [] to [en-US]
2025-07-08 15:57:28.466  4014-4014  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 15:57:28.467  4014-4014  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 15:57:28.467  4014-4014  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 15:57:28.467  4014-4014  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 15:57:28.487  4014-4014  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 15:57:28.488  4014-4014  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 15:57:28.501  4014-4014  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 15:57:28.570  4014-4014  ActivityThread          com.spacelabs.app                    W  Application com.spacelabs.app is suspending. Debugger needs to resume to continue.
2025-07-08 15:57:28.630  4014-4014  System.out              com.spacelabs.app                    I  Sending WAIT chunk
2025-07-08 15:57:28.631  4014-4014  System.out              com.spacelabs.app                    I  Waiting for debugger first packet
2025-07-08 15:57:29.176  4014-4019  nativeloader            com.spacelabs.app                    D  Load libjdwp.so using system ns (caller=<unknown>): ok
2025-07-08 15:57:29.365  4014-4014  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSentVmStart
2025-07-08 15:57:31.412  4014-4014  System.out              com.spacelabs.app                    I  Debug.suspendAllAndSendVmStart, resumed
2025-07-08 15:57:31.694  4014-4014  StaticTestDataManager   com.spacelabs.app                    D  Static test data loaded successfully
2025-07-08 15:57:31.782  4014-4053  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-08 15:57:31.804  4014-4014  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7178ed00b7b8) locale list changing from [] to [en-US]
2025-07-08 15:57:32.012  4014-4053  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-08 15:57:32.013  4014-4053  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-08 15:57:32.037  4014-4053  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-08 15:57:32.043  4014-4014  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7178ed00a818) locale list changing from [] to [en-US]
2025-07-08 15:57:32.061  4014-4014  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-08 15:57:32.149  4014-4014  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-08 15:57:32.198  4014-4014  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:57:32.198  4014-4014  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-08 15:57:32.708  4014-4014  nativeloader            com.spacelabs.app                    D  Load /data/app/~~-LLS-cs8ewG4eKMssWNk5g==/com.spacelabs.app-5Pzlt_UM6E7Y8VOKDapb_w==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~-LLS-cs8ewG4eKMssWNk5g==/com.spacelabs.app-5Pzlt_UM6E7Y8VOKDapb_w==/base.apk!classes20.dex): ok
2025-07-08 15:57:33.347  4014-4067  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-08 15:57:33.403  4014-4075  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-08 15:57:33.431  4014-4065  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-08 15:57:33.442  4014-4065  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-08 15:57:33.452  4014-4065  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-08 15:57:33.463  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-08 15:57:33.474  4014-4065  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-08 15:57:33.483  4014-4065  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-08 15:57:33.490  4014-4065  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-08 15:57:33.501  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-08 15:57:33.513  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-08 15:57:33.524  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-08 15:57:33.535  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-08 15:57:33.543  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-08 15:57:33.557  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-08 15:57:33.574  4014-4070  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-08 15:57:33.592  4014-4065  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-08 15:57:33.613  4014-4065  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-08 15:57:33.655  4014-4086  NetworkCallback         com.spacelabs.app                    D  Network is available: 100
2025-07-08 15:57:33.655  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 15:57:33.722  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:57:33.722  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-08 15:57:33.722  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:57:33.722  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:57:33.738  4014-4086  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 1Mbps, Tx Link speed: 1Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: 2Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-08 15:57:33.738  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Resetting HasAuthenticated because AccountId is empty
2025-07-08 15:57:33.775  4014-4014  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-08 15:57:33.778  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-08 15:57:33.800  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-08 15:57:33.807  4014-4014  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:57:33.813  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-08 15:57:33.818  4014-4086  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=AccountConfigActivitySns
2025-07-08 15:57:33.819  4014-4086  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-08 15:57:33.820  4014-4086  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-08 15:57:33.859  4014-4075  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:57:33.943  4014-4065  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:57:33.982  4014-4014  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:57:34.274  4014-4020  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-08 15:57:34.340  4014-4014  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:57:35.011  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Setting up activity UI
2025-07-08 15:57:35.053  4014-4014  SNS_QR_DEBUG            com.spacelabs.app                    D  postInitUiActions called - enableSnsApi: true
2025-07-08 15:57:35.053  4014-4014  SNS_QR_DEBUG            com.spacelabs.app                    D  Calling generateAndDisplayQRCode() for SNS flow
2025-07-08 15:57:35.175  4014-4014  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: Android ID: 6fdc3252d58580a1
                                                                                                    OS Version: 16
                                                                                                    Device: Google sdk_gphone64_x86_64
                                                                                                    UUID: 803bf36c-36ad-4be6-b569-416879d541ad
                                                                                                    Passphrase: &M3XlLe#uiJm5o6*
2025-07-08 15:57:35.185  4014-4014  deviceDetails           com.spacelabs.app                    D  generateAndDisplayQRCode: ABTYB4kEOoUyrc1TCZpYjpRW3VtndYsnnyBaz69XJw7G6/Jcr7+JFMMW096nRQB+mx09mNXtzlHd
                                                                                                    +45QtiymyxAxG2QPJgd7uKW9mHYH5ZdIaQVjRFyiA7Qw/HR7cbDyI6I8OhO44b/XBn3bhK+YMp9R
                                                                                                    hbxJNTcCS/FYNz0l5N5zGiSYn6An2BmRbfITgSOkc/OdK+IG2hD7JwnscTojYIcSWoDs5ITj16Pu
                                                                                                    bH2jXqvt0zHs3OXiA3V/s7xiFFI1JTBcH0uJLfjZEMkZrrdCHGy+x3fZY5HLKujtCx4bTocgEBWn
                                                                                                    umJPtyGqLePa+EvjI2dVTVxy6+EA4jvyDCDrWQ==
2025-07-08 15:57:35.221  4014-4021  m.spacelabs.app         com.spacelabs.app                    W  Suspending all threads took: 5.112ms
2025-07-08 15:57:35.367  4014-4014  m.spacelabs.app         com.spacelabs.app                    W  Verification of com.google.zxing.common.BitMatrix com.google.zxing.MultiFormatWriter.encode(java.lang.String, com.google.zxing.BarcodeFormat, int, int, java.util.Map) took 181.880ms (1132.61 bytecodes/s) (0B arena alloc)
2025-07-08 15:57:35.375  4014-4070  invalidKey              com.spacelabs.app                    D  DeviceUuid key is Invalid
2025-07-08 15:57:35.435  4014-4075  invalidKey              com.spacelabs.app                    D  DevicePass key is Invalid
2025-07-08 15:57:35.475  4014-4021  m.spacelabs.app         com.spacelabs.app                    I  Background concurrent mark compact GC freed 3935KB AllocSpace bytes, 34(1212KB) LOS objects, 49% free, 5301KB/10MB, paused 10.068ms,52.778ms total 520.541ms
2025-07-08 15:57:35.997  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: QR code has been displayed
2025-07-08 15:57:35.997  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onCreate: Activity UI setup completed
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = ''
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:57:36.008  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:57:36.011  4014-4014  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:57:36.012  4014-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:57:36.012  4014-4075  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:57:36.014  4014-4014  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-08 15:57:36.141  4014-4101  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-08 15:57:36.507  4014-4014  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:57:36.610  4014-4014  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:57:36.819  4014-4014  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-08 15:57:37.056  4014-4014  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:57:37.056  4014-4014  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:e0849a60: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:57:37.618  4014-4106  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
2025-07-08 15:57:38.006  4014-4014  VRI[Accoun...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:57:38.997  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Delay completed, checking navigation
2025-07-08 15:57:38.997  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onQrCodeDisplayed: Activity is resumed, checking navigation
2025-07-08 15:57:38.997  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = ''
2025-07-08 15:57:38.997  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:57:38.997  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = ''
2025-07-08 15:57:38.998  4014-4014  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists:
2025-07-08 15:57:38.998  4014-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is not 'true', no navigation
2025-07-08 15:57:38.998  4014-4075  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfHasAuthenticatedExist:
2025-07-08 15:58:21.336  4014-4014  SNS                     com.spacelabs.app                    D  onNextButtonClickedAction: true hasAuthenticated
2025-07-08 15:58:21.338  4014-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 15:58:21.340  4014-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = ''
2025-07-08 15:58:21.340  4014-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 15:58:21.340  4014-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Staying on AccountConfigActivitySns (HasAuthenticated is empty)
2025-07-08 15:58:21.340  4014-4075  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = AccountConfigActivitySns
2025-07-08 15:58:21.340  4014-4075  SAVE_ACCOUNT_ID         com.spacelabs.app                    D  saveSettingsAndNavigate: true ,And HasAuthenticated
2025-07-08 15:58:21.344  4014-4014  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.AccountConfigActivitySns
2025-07-08 15:58:21.356  4014-4065  invalidKey              com.spacelabs.app                    D  HasAuthenticated key is Invalid
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Called
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: HasAuthenticated value = 'true'
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId value = ''
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: QR code displayed = true
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: AccountId is empty, ensuring QR code is shown
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  onResume: Calling navigateIfAccountIdExists()
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: HasAuthenticated = 'true'
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfAccountIdExists: Calling navigateIfHasAuthenticatedExist
2025-07-08 15:58:21.393  4014-4014  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key = 'true'
2025-07-08 15:58:21.394  4014-4014  DEVICE_EXIST            com.spacelabs.app                    D  navigateIfAccountIdExists: true
2025-07-08 15:58:21.396  4014-4070  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  navigateIfHasAuthenticatedExist: key is 'true', posting NavigateToNext event
2025-07-08 15:58:21.400  4014-4070  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  UiEventHandler.uiEvent: Received event NavigateToNext
2025-07-08 15:58:21.401  4014-4070  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: HasAuthenticated = 'true'
2025-07-08 15:58:21.401  4014-4070  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: AccountId = ''
2025-07-08 15:58:21.401  4014-4070  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Going to QrScannerActivitySns (AccountId is empty)
2025-07-08 15:58:21.401  4014-4070  SNS_NAVIGATION_DEBUG    com.spacelabs.app                    D  NavigateToNext: Target activity = QrScannerActivitySns
2025-07-08 15:58:21.465  4014-4014  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-08 15:58:21.551  4014-4014  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7178ed0108f8) locale list changing from [] to [en-US]
2025-07-08 15:58:22.155  4014-4014  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-08 15:58:22.190  4014-4014  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:58:22.197  4014-4014  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:8bf18905: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:58:22.253  4014-4014  VRI[Accoun...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:58:23.372  4014-4014  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-08 15:58:23.488  4014-4130  CameraManagerGlobal     com.spacelabs.app                    I  Connecting to camera service
2025-07-08 15:58:23.543  4014-4130  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:58:23.546  4014-4130  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:58:23.560  4014-4130  CameraX                 com.spacelabs.app                    W  Retry init. Start time 203777 current time 203869
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:58:23.957  4014-4014  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-08 15:58:23.957  4014-4014  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:eb668513: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-08 15:58:24.065  4014-4130  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:58:24.065  4014-4130  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:58:24.066  4014-4130  CameraX                 com.spacelabs.app                    W  Retry init. Start time 203777 current time 204388
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:58:24.115  4014-4014  VRI[QrScan...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-08 15:58:24.569  4014-4130  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:58:24.569  4014-4130  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:58:24.569  4014-4130  CameraX                 com.spacelabs.app                    W  Retry init. Start time 203777 current time 204892
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:58:25.072  4014-4130  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:58:25.073  4014-4130  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:58:25.073  4014-4130  CameraX                 com.spacelabs.app                    W  Retry init. Start time 203777 current time 205396
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:58:25.576  4014-4130  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:58:25.576  4014-4130  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:58:25.576  4014-4130  CameraX                 com.spacelabs.app                    W  Retry init. Start time 203777 current time 205899
                                                                                                    androidx.camera.core.impl.CameraValidator$CameraIdListIncorrectException: Expected camera missing from device.
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:97)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338)
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)
                                                                                                    	at java.lang.Thread.run(Thread.java:1119)
                                                                                                    Caused by: java.lang.IllegalArgumentException: No available camera can be found
                                                                                                    	at androidx.camera.core.CameraSelector.select(CameraSelector.java:94)
                                                                                                    	at androidx.camera.core.impl.CameraValidator.validateCameras(CameraValidator.java:83)
                                                                                                    	at androidx.camera.core.CameraX.lambda$initAndRetryRecursively$2$androidx-camera-core-CameraX(CameraX.java:338) 
                                                                                                    	at androidx.camera.core.CameraX$$ExternalSyntheticLambda3.run(Unknown Source:10) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1119) 
2025-07-08 15:58:26.079  4014-4130  CameraValidator         com.spacelabs.app                    D  Verifying camera lens facing on emu64xa, lensFacingInteger: null
2025-07-08 15:58:26.079  4014-4130  CameraValidator         com.spacelabs.app                    E  Camera LensFacing verification failed, existing cameras: []
2025-07-08 15:58:26.079  4014-4130  CameraX                 com.spacelabs.app                    E  The device might underreport the amount of the cameras. Finish the initialize task since we are already reaching the maximum number of retries.
2025-07-08 15:58:26.099  4014-4014  Qr_Scanner_Activity_Sns com.spacelabs.app                    E  Use case binding failed
                                                                                                    java.lang.IllegalArgumentException: Provided camera selector unable to resolve a camera for the given use case
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:609)
                                                                                                    	at androidx.camera.lifecycle.ProcessCameraProvider.bindToLifecycle(ProcessCameraProvider.java:407)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.startCamera$lambda$2(CameraActivity.kt:57)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity.$r8$lambda$QsmAJe7FdI1KZZ6w_B0BAiHaOXA(Unknown Source:0)
                                                                                                    	at com.spacelabs.app.activities.snsActivities.helpers.CameraActivity$$ExternalSyntheticLambda1.run(Unknown Source:4)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:995)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:103)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
--------- beginning of crash
2025-07-08 15:58:29.447  4014-4091  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7179c5402c38)
2025-07-08 15:58:29.447  4014-4090  libc                    com.spacelabs.app                    A  FORTIFY: pthread_mutex_lock called on a destroyed mutex (0x7179c5402c38)
2025-07-08 15:58:29.458  4014-4091  libc                    com.spacelabs.app                    A  Fatal signal 6 (SIGABRT), code -1 (SI_QUEUE) in tid 4091 (hwuiTask1), pid 4014 (m.spacelabs.app)
---------------------------- PROCESS ENDED (4014) for package com.spacelabs.app ----------------------------
---------------------------- PROCESS STARTED (4014) for package com.spacelabs.app ----------------------------
---------------------------- PROCESS ENDED (4014) for package com.spacelabs.app ----------------------------
