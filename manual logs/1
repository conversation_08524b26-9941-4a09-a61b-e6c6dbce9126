---------------------------- PROCESS STARTED (5002) for package com.spacelabs.app ----------------------------
2025-07-04 11:36:19.269  5002-5002  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/lib/x86_64:/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-04 11:36:19.338  5002-5002  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20d9618) locale list changing from [] to [en-US]
2025-07-04 11:36:19.340  5002-5002  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20d8358) locale list changing from [] to [en-US]
2025-07-04 11:36:19.346  5002-5002  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-04 11:36:19.346  5002-5002  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-04 11:36:19.346  5002-5002  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-04 11:36:19.347  5002-5002  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-04 11:36:19.347  5002-5002  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-04 11:36:19.347  5002-5002  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-04 11:36:19.347  5002-5002  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-04 11:36:19.410  5002-5018  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-04 11:36:19.414  5002-5002  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20da5b8) locale list changing from [] to [en-US]
2025-07-04 11:36:19.466  5002-5002  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20db558) locale list changing from [] to [en-US]
2025-07-04 11:36:19.476  5002-5002  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-04 11:36:19.479  5002-5018  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-04 11:36:19.479  5002-5018  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-04 11:36:19.521  5002-5018  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-04 11:36:19.554  5002-5002  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-04 11:36:19.597  5002-5002  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-04 11:36:19.598  5002-5002  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-04 11:36:19.786  5002-5002  nativeloader            com.spacelabs.app                    D  Load /data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!classes20.dex): ok
2025-07-04 11:36:20.100  5002-5034  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-04 11:36:20.128  5002-5033  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-04 11:36:20.131  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-04 11:36:20.147  5002-5029  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-04 11:36:20.153  5002-5047  NetworkCallback         com.spacelabs.app                    D  Network is available: 101
2025-07-04 11:36:20.156  5002-5047  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 2Mbps, Tx Link speed: 2Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: 2Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-04 11:36:20.158  5002-5047  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-04 11:36:20.160  5002-5047  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-04 11:36:20.165  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-04 11:36:20.172  5002-5002  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-04 11:36:20.180  5002-5002  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-04 11:36:20.188  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-04 11:36:20.195  5002-5002  Choreographer           com.spacelabs.app                    I  Skipped 46 frames!  The application may be doing too much work on its main thread.
2025-07-04 11:36:20.217  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-04 11:36:20.268  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-04 11:36:20.279  5002-5029  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-04 11:36:20.295  5002-5029  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-04 11:36:20.323  5002-5029  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-04 11:36:20.357  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-04 11:36:20.397  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-04 11:36:20.425  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-04 11:36:20.436  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-04 11:36:20.444  5002-5029  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-04 11:36:20.454  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-04 11:36:20.472  5002-5033  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-04 11:36:20.681  5002-5006  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-04 11:36:21.153  5002-5002  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-04 11:36:21.162  5002-5011  HWUI                    com.spacelabs.app                    I  Davey! duration=1683ms; Flags=1, FrameTimelineVsyncId=39911, IntendedVsync=2630754130879, Vsync=2631520797515, InputEventId=0, HandleInputStart=2631534559795, AnimationStart=2631534581494, PerformTraversalsStart=2631535362375, DrawStart=2631735252308, FrameDeadline=2630770797545, FrameStartTime=2631533800160, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=2631753317248, SyncStart=2631769197663, IssueDrawCommandsStart=2631769564666, SwapBuffers=2632273116832, FrameCompleted=2632453553078, DequeueBufferDuration=21436, QueueBufferDuration=2226919, GpuCompleted=2632453553078, SwapBuffersCompleted=2632445909359, DisplayPresentTime=0, CommandSubmissionCompleted=2632273116832,
2025-07-04 11:36:21.232  5002-5002  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-04 11:36:21.281  5002-5002  Choreographer           com.spacelabs.app                    I  Skipped 63 frames!  The application may be doing too much work on its main thread.
2025-07-04 11:36:21.355  5002-5057  InteractionJankMonitor  com.spacelabs.app                    W  Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.spacelabs.app
2025-07-04 11:36:21.632  5002-5012  HWUI                    com.spacelabs.app                    I  Davey! duration=1262ms; Flags=0, FrameTimelineVsyncId=39950, IntendedVsync=2631554130847, Vsync=2632604130805, InputEventId=0, HandleInputStart=2632621047345, AnimationStart=2632621050013, PerformTraversalsStart=2632621716588, DrawStart=2632650293124, FrameDeadline=2632470797477, FrameStartTime=2632620064515, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=2632657271159, SyncStart=2632657441746, IssueDrawCommandsStart=2632658623645, SwapBuffers=2632755213259, FrameCompleted=2632816552854, DequeueBufferDuration=25889, QueueBufferDuration=18779129, GpuCompleted=2632816372727, SwapBuffersCompleted=2632816552854, DisplayPresentTime=0, CommandSubmissionCompleted=2632755213259,
2025-07-04 11:36:22.187  5002-5002  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-04 11:36:22.298  5002-5002  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-04 11:36:22.768  5002-5002  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-04 11:36:22.874  5002-5002  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-04 11:36:22.923  5002-5002  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-04 11:36:22.939  5002-5002  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-04 11:36:22.939  5002-5002  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:38f95df9: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
2025-07-04 11:36:25.322  5002-5063  ProfileInstaller        com.spacelabs.app                    D  Installing profile for com.spacelabs.app
