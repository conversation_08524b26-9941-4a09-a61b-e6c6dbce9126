--------- beginning of main
--------- beginning of system
---------------------------- PROCESS STARTED (2494) for package com.spacelabs.app ----------------------------
2025-07-08 11:31:21.926  2494-2494  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/lib/x86_64:/data/app/~~29tgGnKil_Y6odIVtDdtvA==/com.spacelabs.app-J8lCJAxLNJd6bVCnvMIeSQ==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-08 11:31:22.132  2494-2494  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7bd756975f98) locale list changing from [] to [en-US]
2025-07-08 11:31:22.160  2494-2494  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-08 11:31:22.160  2494-2494  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-08 11:31:22.160  2494-2494  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-08 11:31:22.160  2494-2494  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-08 11:31:22.161  2494-2494  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-08 11:31:22.161  2494-2494  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-08 11:31:22.161  2494-2494  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-08 11:31:22.526  2494-2494  SimulatorInitializer    com.spacelabs.app                    I  Initializing medical data simulator...
2025-07-08 11:31:22.596  2494-2494  AndroidRuntime          com.spacelabs.app                    D  Shutting down VM
--------- beginning of crash
2025-07-08 11:31:22.600  2494-2494  AndroidRuntime          com.spacelabs.app                    E  FATAL EXCEPTION: main
                                                                                                    Process: com.spacelabs.app, PID: 2494
                                                                                                    java.lang.ExceptionInInitializerError
                                                                                                    	at com.spacelabs.app.simulator.MedicalDataSimulator.<init>(MedicalDataSimulator.kt:59)
                                                                                                    	at com.spacelabs.app.simulator.MedicalDataSimulator$Companion.getInstance(MedicalDataSimulator.kt:48)
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager.<init>(SimulationManager.kt:29)
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager$Companion.getInstance(SimulationManager.kt:24)
                                                                                                    	at com.spacelabs.app.simulator.SimulatorInitializer.initialize(SimulatorInitializer.kt:44)
                                                                                                    	at com.spacelabs.app.ApplicationClass.onCreate(ApplicationClass.kt:29)
                                                                                                    	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1381)
                                                                                                    	at android.app.ActivityThread.handleBindApplication(ActivityThread.java:7830)
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleBindApplication(Unknown Source:0)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2546)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:110)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248)
                                                                                                    	at android.os.Looper.loop(Looper.java:338)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932)
                                                                                                    Caused by: java.lang.NullPointerException: Context may not be null
                                                                                                    	at io.objectbox.BoxStoreBuilder.androidContext(BoxStoreBuilder.java:201)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.initObjectBox(ObjectBoxManager.kt:20)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.getObjectBoxStore(ObjectBoxManager.kt:32)
                                                                                                    	at com.spacelabs.app.dataManager.ObjectBoxManager$Companion.getMeasurementBox(ObjectBoxManager.kt:43)
                                                                                                    	at com.spacelabs.app.dataManager.MeasurementDataManager.<clinit>(MeasurementDataManager.kt:21)
                                                                                                    	at com.spacelabs.app.simulator.MedicalDataSimulator.<init>(MedicalDataSimulator.kt:59) 
                                                                                                    	at com.spacelabs.app.simulator.MedicalDataSimulator$Companion.getInstance(MedicalDataSimulator.kt:48) 
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager.<init>(SimulationManager.kt:29) 
                                                                                                    	at com.spacelabs.app.simulator.SimulationManager$Companion.getInstance(SimulationManager.kt:24) 
                                                                                                    	at com.spacelabs.app.simulator.SimulatorInitializer.initialize(SimulatorInitializer.kt:44) 
                                                                                                    	at com.spacelabs.app.ApplicationClass.onCreate(ApplicationClass.kt:29) 
                                                                                                    	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1381) 
                                                                                                    	at android.app.ActivityThread.handleBindApplication(ActivityThread.java:7830) 
                                                                                                    	at android.app.ActivityThread.-$$Nest$mhandleBindApplication(Unknown Source:0) 
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2546) 
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:110) 
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:248) 
                                                                                                    	at android.os.Looper.loop(Looper.java:338) 
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9067) 
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method) 
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:593) 
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:932) 
2025-07-08 11:31:22.961  2494-2494  Process                 com.spacelabs.app                    I  Sending signal. PID: 2494 SIG: 9
---------------------------- PROCESS ENDED (2494) for package com.spacelabs.app ----------------------------
