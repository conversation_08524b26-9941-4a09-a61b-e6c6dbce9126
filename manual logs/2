---------------------------- PROCESS ENDED (5121) for package com.spacelabs.app ----------------------------
---------------------------- PROCESS STARTED (5218) for package com.spacelabs.app ----------------------------
2025-07-04 11:43:30.597  5218-5218  nativeloader            com.spacelabs.app                    D  Configuring clns-9 for other apk /data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/lib/x86_64:/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.spacelabs.app
2025-07-04 11:43:30.652  5218-5218  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20d9618) locale list changing from [] to [en-US]
2025-07-04 11:43:30.654  5218-5218  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20d8358) locale list changing from [] to [en-US]
2025-07-04 11:43:30.659  5218-5218  GraphicsEnvironment     com.spacelabs.app                    V  Currently set values for:
2025-07-04 11:43:30.659  5218-5218  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_pkgs=[]
2025-07-04 11:43:30.659  5218-5218  GraphicsEnvironment     com.spacelabs.app                    V    angle_gl_driver_selection_values=[]
2025-07-04 11:43:30.659  5218-5218  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in per-application setting
2025-07-04 11:43:30.659  5218-5218  GraphicsEnvironment     com.spacelabs.app                    V  ANGLE allowlist from config:
2025-07-04 11:43:30.660  5218-5218  GraphicsEnvironment     com.spacelabs.app                    V  com.spacelabs.app is not listed in ANGLE allowlist or settings, returning default
2025-07-04 11:43:30.661  5218-5218  GraphicsEnvironment     com.spacelabs.app                    V  Neither updatable production driver nor prerelease driver is supported.
2025-07-04 11:43:30.706  5218-5234  DisplayManager          com.spacelabs.app                    I  Choreographer implicitly registered for the refresh rate.
2025-07-04 11:43:30.708  5218-5218  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20dc1d8) locale list changing from [] to [en-US]
2025-07-04 11:43:30.731  5218-5218  m.spacelabs.app         com.spacelabs.app                    I  AssetManager2(0x7f71c20e1318) locale list changing from [] to [en-US]
2025-07-04 11:43:30.738  5218-5218  ashmem                  com.spacelabs.app                    E  Pinning is deprecated since Android Q. Please use trim or other methods.
2025-07-04 11:43:30.750  5218-5234  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv1_CM_emulation.so
2025-07-04 11:43:30.750  5218-5234  EGL_emulation           com.spacelabs.app                    I  Opening libGLESv2_emulation.so
2025-07-04 11:43:30.767  5218-5234  HWUI                    com.spacelabs.app                    W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-07-04 11:43:30.770  5218-5218  DesktopModeFlags        com.spacelabs.app                    D  Toggle override initialized to: OVERRIDE_UNSET
2025-07-04 11:43:30.789  5218-5218  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-04 11:43:30.789  5218-5218  m.spacelabs.app         com.spacelabs.app                    I  hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
2025-07-04 11:43:30.919  5218-5218  nativeloader            com.spacelabs.app                    D  Load /data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!/lib/x86_64/libsqlcipher.so using class loader ns clns-9 (caller=/data/app/~~yrxXipUBMe7IGmTfiIX4Hw==/com.spacelabs.app-nCKfPJzg8Q6vdSW1p9gllg==/base.apk!classes20.dex): ok
2025-07-04 11:43:31.164  5218-5259  HandleNonE...meterList: com.spacelabs.app                    D  HR
2025-07-04 11:43:31.184  5218-5257  HandleNonE...meterList: com.spacelabs.app                    D  RR
2025-07-04 11:43:31.187  5218-5245  UiEvent                 com.spacelabs.app                    D  WIFI_UNKNOWN EVENT -> PatientChange
2025-07-04 11:43:31.194  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  SpO2
2025-07-04 11:43:31.202  5218-5260  NetworkCallback         com.spacelabs.app                    D  Network is available: 101
2025-07-04 11:43:31.203  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-04 11:43:31.203  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-04 11:43:31.203  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-04 11:43:31.203  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=QrScannerActivitySns
2025-07-04 11:43:31.204  5218-5245  HandleNonE...meterList: com.spacelabs.app                    D  COUGH_COUNT
2025-07-04 11:43:31.204  5218-5260  NetworkCallback         com.spacelabs.app                    D  Network capabilities changed: [ Transports: WIFI Capabilities: NOT_METERED&INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VPN&VALIDATED&NOT_ROAMING&FOREGROUND&NOT_CONGESTED&NOT_SUSPENDED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED LinkUpBandwidth>=12000Kbps LinkDnBandwidth>=30000Kbps TransportInfo: <SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*********, Security type: 0, Supplicant state: COMPLETED, Wi-Fi standard: legacy, RSSI: -50, Link speed: 2Mbps, Tx Link speed: 2Mbps, Max Supported Tx Link speed: 11Mbps, Rx Link speed: 2Mbps, Max Supported Rx Link speed: 11Mbps, Frequency: 2447MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>> SignalStrength: -50 UnderlyingNetworks: Null], hasInternet: true
2025-07-04 11:43:31.204  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: enableSnsApi=true
2025-07-04 11:43:31.204  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: HasAuthenticated='true'
2025-07-04 11:43:31.204  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: AccountId=''
2025-07-04 11:43:31.204  5218-5260  WIFI_NAVIGATION_DEBUG   com.spacelabs.app                    D  checkSettingsAndNavigate: Target activity=QrScannerActivitySns
2025-07-04 11:43:31.205  5218-5260  NetworkCallback         com.spacelabs.app                    D  Network is onLinkPropertiesChanged
2025-07-04 11:43:31.205  5218-5260  NetworkCallback         com.spacelabs.app                    D  Network is onBlockedStatusChanged
2025-07-04 11:43:31.212  5218-5218  HWUI                    com.spacelabs.app                    W  Unknown dataspace 0
2025-07-04 11:43:31.215  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  PR
2025-07-04 11:43:31.220  5218-5218  InsetsController        com.spacelabs.app                    D  Setting requestedVisibleTypes to -10 (was -9)
2025-07-04 11:43:31.226  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  ECG
2025-07-04 11:43:31.264  5218-5245  HandleNonE...meterList: com.spacelabs.app                    D  RESP
2025-07-04 11:43:31.278  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  PI
2025-07-04 11:43:31.288  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  TEMP_SKIN
2025-07-04 11:43:31.308  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  PPG
2025-07-04 11:43:31.321  5218-5218  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-04 11:43:31.322  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  BP_SYS
2025-07-04 11:43:31.332  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  BP_DIA
2025-07-04 11:43:31.343  5218-5218  activity                com.spacelabs.app                    D  CurrentActivity -> com.spacelabs.app.activities.snsActivities.QrScannerActivitySns
2025-07-04 11:43:31.343  5218-5253  HandleNonE...meterList: com.spacelabs.app                    D  BP
2025-07-04 11:43:31.362  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  BODY_POSITION
2025-07-04 11:43:31.370  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  FALL_COUNT
2025-07-04 11:43:31.387  5218-5253  HandleNonE...meterList: com.spacelabs.app                    D  STEP_COUNT
2025-07-04 11:43:31.421  5218-5242  HandleNonE...meterList: com.spacelabs.app                    D  ANGLE
2025-07-04 11:43:31.558  5218-5223  m.spacelabs.app         com.spacelabs.app                    I  Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
2025-07-04 11:43:31.936  5218-5218  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-04 11:43:32.051  5218-5218  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=false
2025-07-04 11:43:33.014   732-2014  ActivityTaskManager     system_server                        E  convertFromTranslucent expects ActivityRecord{165257070 u0 com.spacelabs.app/.activities.snsActivities.QrScannerActivitySns t91} but is ActivityRecord{75777146 u0 com.spacelabs.app/.activities.snsActivities.QrScannerActivitySns t91}
2025-07-04 11:43:33.023  5218-5218  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-04 11:43:33.027   732-2447  ActivityTaskManager     system_server                        E  convertFromTranslucent expects null but is ActivityRecord{165257070 u0 com.spacelabs.app/.activities.snsActivities.QrScannerActivitySns t91}
2025-07-04 11:43:33.029  5218-5218  VRI[QrScan...tivitySns] com.spacelabs.app                    D  changeCanvasOpacity: opaque=true
2025-07-04 11:43:33.235  5218-5218  VRI[QrScan...tivitySns] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-04 11:43:33.236  5218-5218  VRI[WifiConfigActivity] com.spacelabs.app                    D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-04 11:43:33.422  5218-5218  NetworkCallback         com.spacelabs.app                    D  Network is unregisterCallbacksAndCoroutines
2025-07-04 11:43:33.447  5218-5218  InsetsController        com.spacelabs.app                    D  hide(ime(), fromIme=false)
2025-07-04 11:43:33.447  5218-5218  ImeTracker              com.spacelabs.app                    I  com.spacelabs.app:c7b4eff8: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
